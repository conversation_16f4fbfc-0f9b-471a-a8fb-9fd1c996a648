BasedOnStyle: Google
IndentWidth: 3
IndentAccessModifiers: true
BreakAfterReturnType: TopLevelDefinitions
IncludeCategories:
  - Regex: '^<.*>' # system includes
    Priority: 1
  - Regex: '^"(absl|gflags|glog|gtest)/.+\.h"$' # external libraries
    Priority: 2
  - Regex: '^".+\.h"$' # internal libraries
    Priority: 3
IncludeBlocks: Regroup # put spaces in between
SortIncludes: true
IncludeIsMainRegex: '(?!)' # don't put "main" header above others
DerivePointerAlignment: false
PointerAlignment: Left
