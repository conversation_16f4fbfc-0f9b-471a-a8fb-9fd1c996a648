#include <algorithm>
#include <fstream>
#include <iostream>
#include <string>
#include <vector>

#include "absl/status/status.h"
#include "absl/strings/str_format.h"
#include "gflags/gflags.h"
#include "glog/logging.h"

#include "apps-common.h"
#include "camraw/file_data_accessor.h"
#include "camraw/file_type_classifier.h"
#include "camraw/pipeline_source.h"
#include "camraw/status_macros.h"
#include "camraw/tiff_file_parser.h"
#include "camraw/tiff_ifd.h"
#include "camraw/tiff_tag.h"
#include "camraw/tiff_tag_id.h"

using cmrw::FileDataAccessor;
using cmrw::FileParser;
using cmrw::FileTypeClassifier;
using cmrw::PipelineSource;
using cmrw::TIFFFileParser;
using cmrw::TIFFIFD;
using cmrw::TIFFTag;
using cmrw::TIFFTagId;

DEFINE_string(rawfile, "", "Input RAW file to extract thumbnails from");

#define FILE_WRITER_CHUNK_SIZE 4096
void
copy_between_files(std::fstream& in_file, std::ofstream& out_file,
                   uint32_t in_offset, uint32_t num_bytes) {
   in_file.seekg(in_offset);

   int bytes_remaining = num_bytes;
   while (bytes_remaining > 0) {
      int bytes_to_write = std::min(bytes_remaining, FILE_WRITER_CHUNK_SIZE);
      auto buffer = std::make_unique<char[]>(bytes_to_write);

      in_file.read(buffer.get(), bytes_to_write);
      out_file.write(buffer.get(), bytes_to_write);

      bytes_remaining -= bytes_to_write;
   }
}

void
write_jpeg_to_file(std::fstream& raw_file, uint32_t offset, uint32_t length) {
   std::ofstream out_file;
   std::string file_name = absl::StrFormat("%d.jpg", offset);
   out_file.open(file_name, std::ios::binary | std::ios::out);

   copy_between_files(raw_file, out_file, offset, length);

   LOG(INFO) << "Wrote " << length << " bytes to file '" << file_name << "'";

   out_file.close();
}

absl::Status
extract_jpegs(std::fstream& raw_file, TIFFFileParser& tiff_parser) {
   uint32_t num_thumbnails = 0;

   std::vector<const TIFFIFD*> ifds;
   ASSIGN_OR_RETURN(
       ifds,
       tiff_parser.GetAllIFDsOfType(cmrw::TIFF_IFD_TYPE_REDUCED_RES_IMAGE),
       LOG(ERROR) << "Failed to get IFDs for jpeg thumbnails.");

   for (const TIFFIFD* ifd : ifds) {
      uint32_t jpeg_offset, jpeg_length;

      auto jpeg_offset_tag_or = ifd->GetTag(TIFFTagId::JPEG_INT_FMT);
      if (jpeg_offset_tag_or.ok()) {
         ASSIGN_OR_RETURN(
             jpeg_offset, (*jpeg_offset_tag_or)->GetDataValue<uint32_t>(),
             LOG(ERROR) << "Failed to get jpeg offset value from tag");

         const TIFFTag* jpeg_length_tag;
         ASSIGN_OR_RETURN(jpeg_length_tag,
                          ifd->GetTag(TIFFTagId::JPEG_INT_FMT_LEN),
                          LOG(ERROR) << "Failed to get jpeg length tag");
         ASSIGN_OR_RETURN(
             jpeg_length, jpeg_length_tag->GetDataValue<uint32_t>(),
             LOG(ERROR) << "Failed to get jpeg length value from tag");

         write_jpeg_to_file(raw_file, jpeg_offset, jpeg_length);
         num_thumbnails++;
      } else {
         const TIFFTag* strip_offsets_tag;
         ASSIGN_OR_RETURN(strip_offsets_tag,
                          ifd->GetTag(TIFFTagId::STRIP_OFFSETS),
                          LOG(ERROR) << "Unknown thumbnail format");

         if (strip_offsets_tag->GetDataCount() > 1) {
            LOG(ERROR) << "Multiple strip thumbnails not implemented.";
            return absl::UnimplementedError(
                "Multiple strip thumbnails not implemented");
         }

         uint32_t strip_offset;
         ASSIGN_OR_RETURN(
             strip_offset, strip_offsets_tag->GetDataValue<uint32_t>(),
             LOG(ERROR) << "Failed to get strip offset value from tag");

         const TIFFTag* strip_byte_counts_tag;
         ASSIGN_OR_RETURN(strip_byte_counts_tag,
                          ifd->GetTag(TIFFTagId::STRIP_BYTE_COUNTS),
                          LOG(ERROR) << "Failed to get strip byte counts tag");

         if (strip_byte_counts_tag->GetDataCount() > 1) {
            LOG(ERROR) << "Multiple strip thumbnails not implemented.";
            return absl::UnimplementedError(
                "Multiple strip thumbnails not implemented");
         }

         uint32_t strip_byte_count;
         ASSIGN_OR_RETURN(
             strip_byte_count, strip_byte_counts_tag->GetDataValue<uint32_t>(),
             LOG(ERROR) << "Failed to get strip byte count value from tag");

         write_jpeg_to_file(raw_file, strip_offset, strip_byte_count);
         num_thumbnails++;
      }
   }

   if (num_thumbnails == 0) {
      LOG(ERROR) << "No thumbnails found in file";
      return absl::NotFoundError("No thumbnails found in image");
   }

   return absl::OkStatus();
}

int
main(int argc, char* argv[]) {
   google::ParseCommandLineFlags(&argc, &argv, true);
   google::InitGoogleLogging(argv[0]);

   if (FLAGS_rawfile == "") {
      LOG(FATAL) << "Must specify input RAW file with --rawfile <file>";
   }

   std::fstream raw_file(FLAGS_rawfile, std::ios::in | std::ios::binary);
   if (!raw_file) {
      LOG(FATAL) << "Could not open input raw file: " << FLAGS_rawfile;
   }
   LOG(INFO) << "Opened RAW file: " << FLAGS_rawfile;

   FileDataAccessor file_accessor(raw_file);

   FileTypeClassifier classifier;
   EXIT_IF_ERROR(classifier.ClassifyFile(file_accessor, FLAGS_rawfile));

   std::string file_type;
   ASSIGN_OR_EXIT(file_type, classifier.GetFileTypeString());
   LOG(INFO) << "File type classified as: " << file_type;

   std::unique_ptr<FileParser> parser;
   ASSIGN_OR_EXIT(parser, classifier.GetFileParser());

   EXIT_IF_ERROR(parser->Init());

   std::unique_ptr<PipelineSource> pipeline_source;
   ASSIGN_OR_EXIT(pipeline_source, classifier.GetPipelineSource(parser.get()));

   TIFFFileParser* tiff_parser = dynamic_cast<TIFFFileParser*>(parser.get());
   if (!tiff_parser) {
      LOG(ERROR) << "Extracting thumbnails is only supported for "
                 << "TIFF-based files.";
      return 1;
   }

   EXIT_IF_ERROR(extract_jpegs(raw_file, *tiff_parser));

   raw_file.close();

   return 0;
}
