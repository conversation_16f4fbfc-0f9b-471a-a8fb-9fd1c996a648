#ifndef APPS_COMMON_H_
#define APPS_COMMON_H_

#define ASSIGN_OR_EXIT(var, rexpr)          \
   do {                                     \
      auto _status_or = (rexpr);            \
      if (!_status_or.ok()) {               \
         LOG(ERROR) << _status_or.status(); \
         return 1;                          \
      }                                     \
      var = std::move(_status_or).value();  \
   } while (0)

#define EXIT_IF_ERROR(expr)     \
   do {                         \
      auto _status = (expr);    \
      if (!_status.ok()) {      \
         LOG(ERROR) << _status; \
         return 1;              \
      }                         \
   } while (0)

#endif  // APPS_COMMON_H_
