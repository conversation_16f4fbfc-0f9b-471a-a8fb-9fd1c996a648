#!/bin/bash

usage() {
   echo "Usage: $0 --process-raw <process-raw-bin> --imagedir <images-dir> [--threads=N]"
   echo "Description: Integration test for process-raw."
   echo "Arguments:"
   echo "  --process-raw  Location of the process-raw binary"
   echo "  --imagedir  Directory containing images to use for test"
   echo "  --outdir    Directory to host the output directory (default to .)"
   echo "  --threads   Number of threads to use"
   exit 1
}

OPTIONS=p:,i:,o:,t:
LONGOPTIONS=process-raw:,imagedir:,outdir:,threads:

PARSED=$(getopt --options=$OPTIONS --longoptions=$LONGOPTIONS -- "$@")
if [[ $? -ne 0 ]]; then
   usage
fi
eval set -- "$PARSED"

processraw_bin=""
image_dir=""
out_dir="`pwd`"
threads=0

# Handle each option
while true; do
    case "$1" in
        -p|--process-raw) processraw_bin="$2"; shift 2;;
        -i|--imagedir) image_dir="$2"; shift 2;;
        -o|--outdir) out_dir="$2"; shift 2;;
        -t|--threads) threads="$2"; shift 2 ;;
        --) shift; break ;;
        *) echo "Unexpected option: $1"; exit 1 ;;
    esac
 done

PROCESSRAW=$(realpath $processraw_bin)
DEFAULT_OPTS="--alsologtostderr --v 2"

LINEAR_RAW_FILES=""
# See if linear raw files are specified
if [ -f "$(realpath $image_dir)/.linear_raw_files" ]; then
   mapfile -t LINEAR_RAW_FILES < "$(realpath $image_dir)/.linear_raw_files"
fi

OUTPUT_ALL_FULL_OPTS="--output-decoded --output-scaled --output-demosaic --output-wb --output-cs --output-processed"
OUTPUT_ALL_LINEAR_RAW_OPTS="--output-decoded --output-scaled --output-processed"
OUTPUT_PROCESSED_OPTS="--output-processed"

echo "Output directory is $out_dir"
cd "$out_dir"
TEST_DIR=process-raw-test-$(date '+%Y-%m-%d-%H:%M:%S')
echo "Test directory is $TEST_DIR"
mkdir -p "$TEST_DIR"
cd "$TEST_DIR"

errors=0

# run_single_test <image_file> <output=all|processed>
run_single_test() {
   if [ "$2" == "all" ]; then
      "$PROCESSRAW" $(echo $DEFAULT_OPTS --rawfile $1 $OUTPUT_ALL_FULL_OPTS --threads $threads) 2> output.log
   elif [ "$2" == "all-linear" ]; then
      "$PROCESSRAW" $(echo $DEFAULT_OPTS --rawfile $1 $OUTPUT_ALL_LINEAR_RAW_OPTS --threads $threads) 2> output.log
   else
      "$PROCESSRAW" $(echo $DEFAULT_OPTS --rawfile $1 $OUTPUT_PROCESSED_OPTS --threads $threads) 2> output.log
   fi

   validate_test_result $2
}

# validate_test_result <output=all|processed>
validate_test_result() {
   if [ $? -ne 0 ];then
      echo "FAIL"
      ((errors++))
   else
      if [ "$1" == "all" ]; then
         if [ -e "decoded.tiff" ] && [ -e "scaled.tiff" ] && [ -e "demosaiced.tiff" ] && [ -e "whitebalanced.tiff" ] && [ -e "colorspace.tiff" ] && [ -e "processed.tiff" ] &&
            [ -s "decoded.tiff" ] && [ -s "scaled.tiff" ] && [ -s "demosaiced.tiff" ] && [ -s "whitebalanced.tiff" ] && [ -s "colorspace.tiff" ] && [ -s "processed.tiff" ]; then
            echo "PASS"
         else
            echo "FAIL"
            ((errors++))
         fi
      elif [ "$1" == "all-linear" ]; then
         if [ -e "decoded.tiff" ] && [ -e "scaled.tiff" ] && [ -e "processed.tiff" ] &&
            [ -s "decoded.tiff" ] && [ -s "scaled.tiff" ] && [ -s "processed.tiff" ]; then
            echo "PASS"
         else
            echo "FAIL"
            ((errors++))
         fi
      else
         if [ -e "processed.tiff" ] && [ -s "processed.tiff" ]; then
            echo "PASS"
         else
            echo "FAIL"
            ((errors++))
         fi
      fi
   fi
}

# return if input parameter is a linear raw file
is_linear_raw() {
   for filename in "${LINEAR_RAW_FILES[@]}"; do
      if [ "$filename" == "$1" ]; then
         return 0
      fi
   done
   return 1
}

for image_file in `find "$(realpath $image_dir)" -type f \( -name "*.ARW" -o -name "*.dng" \)`; do
   echo "Testing with ${image_file}"
   FILE_NAME=`basename $image_file`

   is_linear_raw "${FILE_NAME}"
   LINEAR_RAW="false"
   if [ $? -eq 0 ]; then
      LINEAR_RAW="true"
   fi

   mkdir -p $FILE_NAME
   cd $FILE_NAME
      echo -n "- Output ALL: "
      mkdir output-all
      cd output-all
         if [ "$LINEAR_RAW" == "true" ]; then
            run_single_test "$image_file" "all-linear"
         else
            run_single_test "$image_file" "all"
         fi
      cd ..
      echo -n "- Output Processed: "
      mkdir output-processed
      cd output-processed
         run_single_test "$image_file" "processed"
      cd ..
   cd ..
done

echo "Test suite completed with $errors error(s)."

if [ $errors -eq 0 ]; then
   exit 0
else
   exit 1
fi
