#include <algorithm>
#include <fstream>
#include <iostream>
#include <string>

#include "absl/status/statusor.h"
#include "gflags/gflags.h"
#include "glog/logging.h"

#include "apps-common.h"
#include "camraw/bilinear_demosaic_processor.h"
#include "camraw/color_space_processor.h"
#include "camraw/colorspace.h"
#include "camraw/file_data_accessor.h"
#include "camraw/file_type_classifier.h"
#include "camraw/imagemeta.h"
#include "camraw/pipeline.h"
#include "camraw/pipeline_source.h"
#include "camraw/scale_data_processor.h"
#include "camraw/status_macros.h"
#include "camraw/tiff_export_sink.h"
#include "camraw/tone_curve_processor.h"
#include "camraw/white_balance_processor.h"

using cmrw::BilinearDemosaicProcessor;
using cmrw::Colorspace;
using cmrw::ColorSpaceProcessor;
using cmrw::FileDataAccessor;
using cmrw::FileParser;
using cmrw::FileTypeClassifier;
using cmrw::ImageMeta;
using cmrw::ImageMetaId;
using cmrw::ImageMetaPhotoInt;
using cmrw::Pipeline;
using cmrw::PipelineSource;
using cmrw::ScaleDataProcessor;
using cmrw::TiffExportSink;
using cmrw::ToneCurveProcessor;
using cmrw::WhiteBalanceProcessor;

DEFINE_string(rawfile, "", "Input RAW file to process");
DEFINE_bool(output_decoded, false,
            "Output decoded RAW image data to a TIFF file");
DEFINE_bool(output_scaled, false,
            "Output black/white point scaled image to a TIFF file");
DEFINE_bool(output_wb, false, "Output white balanced image to a TIFF file");
DEFINE_bool(output_demosaic, false, "Output demosaiced image to a TIFF file");
DEFINE_bool(output_cs, false,
            "Output color space transformed image to a TIFF file");
DEFINE_bool(output_processed, false, "Output processed image to a TIFF file");
DEFINE_bool(no_crop, false, "Don't crop before outputting TIFF images");
DEFINE_uint32(threads, 0,
              "Number of worker threads to use - default: 0 (all cores)");

struct ProcessingProfile {
   public:
      bool scale_values = true;
      bool white_balance = true;
      bool demosaic = true;
      bool convert_color_space = true;
      bool apply_tone_curve = true;
      uint16_t tone_curve_wl_target = 39064;
      float tone_curve_gamma_target = 1.0f;
};

absl::StatusOr<ProcessingProfile>
GetProcessingProfile(PipelineSource& pipeline_source) {
   ProcessingProfile profile;

   std::unique_ptr<ImageMeta> image_meta;
   ASSIGN_OR_RETURN(
       image_meta,
       pipeline_source.GetMetadata(
           /*required*/ {ImageMetaId::PHOTOMETRIC_INTERPRETATION,
                         ImageMetaId::CAMERA_MAKE, ImageMetaId::CAMERA_MODEL},
           /*optional*/ {}));

   ImageMetaPhotoInt photo_int;
   ASSIGN_OR_RETURN(photo_int, image_meta->GetOne<ImageMetaPhotoInt>(
                                   ImageMetaId::PHOTOMETRIC_INTERPRETATION));

   std::string camera_make;
   ASSIGN_OR_RETURN(camera_make,
                    image_meta->GetOne<std::string>(ImageMetaId::CAMERA_MAKE));
   // Transform camera_make to uppercase
   std::transform(camera_make.begin(), camera_make.end(), camera_make.begin(),
                  [](unsigned char c) { return std::toupper(c); });

   std::string camera_model;
   ASSIGN_OR_RETURN(camera_model,
                    image_meta->GetOne<std::string>(ImageMetaId::CAMERA_MODEL));
   // Transform camera_model to uppercase
   std::transform(camera_model.begin(), camera_model.end(),
                  camera_model.begin(),
                  [](unsigned char c) { return std::toupper(c); });

   if (photo_int == ImageMetaPhotoInt::LINEAR_RAW) {
      // Linear RAW file
      profile.white_balance = false;
      profile.demosaic = false;
      profile.convert_color_space = false;
      if (camera_make == "APPLE") {
         // iPhone ProRAW photos are very dark, adjust white level target
         profile.tone_curve_wl_target = 4096;
         if (camera_model == "IPHONE 14 PRO") {
            profile.tone_curve_wl_target = 640;
         } else if (camera_model == "IPHONE 13 PRO") {
            profile.tone_curve_wl_target = 2304;
            profile.tone_curve_gamma_target = 2.0f;
         }
         LOG(INFO) << "Using the Apple ProRAW processing profile.";
      } else if (camera_make == "SAMSUNG") {
         // Samsung linear RAW files aren't white balanced / color converted
         profile.white_balance = true;
         profile.convert_color_space = true;
         LOG(INFO) << "Using the Samsung Galaxy processing profile.";
      } else {
         LOG(INFO) << "Using the generic linear RAW processing profile.";
      }
   } else if (photo_int == ImageMetaPhotoInt::YCBCR) {
      // Sony mRAW or sRAW file
      CHECK_EQ(camera_make, "SONY");
      profile.white_balance = false;
      profile.demosaic = false;
      profile.convert_color_space = false;
      profile.tone_curve_wl_target = 14336;
      LOG(INFO) << "Using the Sony small/medium RAW processing profile";
   } else {
      LOG(INFO) << "Using the default processing profile.";
   }

   return profile;
}

int
main(int argc, char* argv[]) {
   google::ParseCommandLineFlags(&argc, &argv, true);
   google::InitGoogleLogging(argv[0]);

   if (FLAGS_rawfile == "") {
      LOG(FATAL) << "Must specify input RAW file with --rawfile <file>";
   }

   if (!FLAGS_output_decoded && !FLAGS_output_scaled &&
       !FLAGS_output_demosaic && !FLAGS_output_wb && !FLAGS_output_cs &&
       !FLAGS_output_processed) {
      LOG(FATAL) << "No --output_* option specified!";
   }

   std::fstream raw_file(FLAGS_rawfile, std::ios::in | std::ios::binary);
   if (!raw_file) {
      LOG(FATAL) << "Could not open input raw file: " << FLAGS_rawfile;
   }
   LOG(INFO) << "Opened RAW file: " << FLAGS_rawfile;

   FileDataAccessor file_accessor(raw_file);

   FileTypeClassifier classifier;
   EXIT_IF_ERROR(classifier.ClassifyFile(file_accessor, FLAGS_rawfile));

   std::string file_type;
   ASSIGN_OR_EXIT(file_type, classifier.GetFileTypeString());
   LOG(INFO) << "File type classified as: " << file_type;

   std::unique_ptr<FileParser> parser;
   ASSIGN_OR_EXIT(parser, classifier.GetFileParser());

   EXIT_IF_ERROR(parser->Init());

   std::unique_ptr<PipelineSource> pipeline_source;
   ASSIGN_OR_EXIT(pipeline_source, classifier.GetPipelineSource(parser.get()));

   EXIT_IF_ERROR(pipeline_source->Init());

   // The processing profile determines what kind of pipeline we will build
   ProcessingProfile profile;
   ASSIGN_OR_EXIT(profile, GetProcessingProfile(*pipeline_source));

   if (FLAGS_output_scaled && !profile.scale_values) {
      LOG(ERROR) << "Processing profile for input file not compatible with "
                    "--output-scaled";
      return 1;
   }
   if (FLAGS_output_wb && !profile.white_balance) {
      LOG(ERROR) << "Processing profile for input file not compatible with "
                    "--output-wb";
      return 1;
   }
   if (FLAGS_output_demosaic && !profile.demosaic) {
      LOG(ERROR) << "Processing profile for input file not compatible with "
                    "--output-demosaic";
      return 1;
   }
   if (FLAGS_output_cs && !profile.convert_color_space) {
      LOG(ERROR) << "Processing profile for input file not compatible with "
                    "--output-cs";
      return 1;
   }

   Pipeline pipeline;
   EXIT_IF_ERROR(pipeline.AddSource(std::move(pipeline_source)));

   if (FLAGS_output_decoded) {
      EXIT_IF_ERROR(pipeline.AddSink(
          std::make_unique<TiffExportSink>("decoded.tiff",
                                           /*crop*/ !FLAGS_no_crop)));
   }

   if (FLAGS_output_scaled || FLAGS_output_demosaic || FLAGS_output_wb ||
       FLAGS_output_cs || FLAGS_output_processed) {
      if (profile.scale_values) {
         EXIT_IF_ERROR(
             pipeline.AddProcessor(std::make_unique<ScaleDataProcessor>()));

         if (FLAGS_output_scaled) {
            EXIT_IF_ERROR(pipeline.AddSink(
                std::make_unique<TiffExportSink>("scaled.tiff",
                                                 /*crop*/ !FLAGS_no_crop)));
         }
      }

      if (FLAGS_output_demosaic || FLAGS_output_wb || FLAGS_output_cs ||
          FLAGS_output_processed) {
         if (profile.white_balance) {
            EXIT_IF_ERROR(pipeline.AddProcessor(
                std::make_unique<WhiteBalanceProcessor>()));

            if (FLAGS_output_wb) {
               EXIT_IF_ERROR(pipeline.AddSink(
                   std::make_unique<TiffExportSink>("whitebalanced.tiff",
                                                    /*crop*/ !FLAGS_no_crop)));
            }
         }

         if (FLAGS_output_demosaic || FLAGS_output_cs ||
             FLAGS_output_processed) {
            if (profile.demosaic) {
               EXIT_IF_ERROR(pipeline.AddProcessor(
                   std::make_unique<BilinearDemosaicProcessor>()));

               if (FLAGS_output_demosaic) {
                  EXIT_IF_ERROR(
                      pipeline.AddSink(std::make_unique<TiffExportSink>(
                          "demosaiced.tiff",
                          /*crop*/ !FLAGS_no_crop)));
               }
            }

            if (FLAGS_output_cs || FLAGS_output_processed) {
               if (profile.convert_color_space) {
                  EXIT_IF_ERROR(pipeline.AddProcessor(
                      std::make_unique<ColorSpaceProcessor>(Colorspace::CAMERA,
                                                            Colorspace::SRGB)));

                  if (FLAGS_output_cs) {
                     EXIT_IF_ERROR(
                         pipeline.AddSink(std::make_unique<TiffExportSink>(
                             "colorspace.tiff",
                             /*crop*/ !FLAGS_no_crop)));
                  }
               }

               if (FLAGS_output_processed) {
                  if (profile.apply_tone_curve) {
                     EXIT_IF_ERROR(pipeline.AddProcessor(
                         std::make_unique<ToneCurveProcessor>(
                             profile.tone_curve_wl_target,
                             profile.tone_curve_gamma_target)));
                  }

                  EXIT_IF_ERROR(
                      pipeline.AddSink(std::make_unique<TiffExportSink>(
                          "processed.tiff",
                          /*crop*/ !FLAGS_no_crop)));
               }
            }
         }
      }
   }

   EXIT_IF_ERROR(pipeline.Run(FLAGS_threads));

   raw_file.close();

   return 0;
}
