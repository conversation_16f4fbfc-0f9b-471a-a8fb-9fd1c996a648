#include <fstream>
#include <iostream>
#include <string>

#include "absl/container/flat_hash_map.h"
#include "absl/strings/str_format.h"
#include "gflags/gflags.h"
#include "glog/logging.h"

#include "apps-common.h"
#include "camraw/file_data_accessor.h"
#include "camraw/file_type_classifier.h"
#include "camraw/tiff_file_parser.h"
#include "camraw/tiff_ifd.h"
#include "camraw/tiff_tag.h"
#include "camraw/tiff_tag_id.h"

using cmrw::FileDataAccessor;
using cmrw::FileParser;
using cmrw::FileTypeClassifier;
using cmrw::TIFFFileParser;
using cmrw::TIFFIFD;
using cmrw::TIFFTag;
using cmrw::TIFFTagDataType;
using cmrw::TIFFTagId;

DEFINE_string(rawfile, "", "Input RAW file to parse");

absl::flat_hash_map<uint16_t, const std::string> tag_id_to_name_map = {
    {static_cast<uint16_t>(TIFFTagId::NEW_SUBFILE_TYPE), "NewSubfileType"},
    {0x00FF, "SubfileType"},
    {static_cast<uint16_t>(TIFFTagId::IMAGE_WIDTH), "ImageWidth"},
    {static_cast<uint16_t>(TIFFTagId::IMAGE_LENGTH), "ImageLength"},
    {static_cast<uint16_t>(TIFFTagId::BITS_PER_SAMPLE), "BitsPerSample"},
    {static_cast<uint16_t>(TIFFTagId::COMPRESSION), "Compression"},
    {static_cast<uint16_t>(TIFFTagId::PHOTOMETRIC_INT),
     "PhotometricInterpretation"},
    {0x010E, "ImageDescription"},
    {0x010F, "Make"},
    {static_cast<uint16_t>(TIFFTagId::MODEL), "Model"},
    {static_cast<uint16_t>(TIFFTagId::STRIP_OFFSETS), "StripOffsets"},
    {static_cast<uint16_t>(TIFFTagId::ORIENTATION), "Orientation"},
    {static_cast<uint16_t>(TIFFTagId::SAMPLES_PER_PIXEL), "SamplesPerPixel"},
    {static_cast<uint16_t>(TIFFTagId::ROWS_PER_STRIP), "RowsPerStrip"},
    {static_cast<uint16_t>(TIFFTagId::STRIP_BYTE_COUNTS), "StripByteCounts"},
    {0x011A, "XResolution"},
    {0x011B, "YResolution"},
    {static_cast<uint16_t>(TIFFTagId::PLANAR_CONFIG), "PlanarConfiguration"},
    {0x0128, "ResolutionUnit"},
    {0x0131, "Software"},
    {0x0132, "DateTime"},
    {static_cast<uint16_t>(TIFFTagId::TILE_WIDTH), "TileWidth"},
    {static_cast<uint16_t>(TIFFTagId::TILE_LENGTH), "TileLength"},
    {static_cast<uint16_t>(TIFFTagId::TILE_OFFSETS), "TileOffsets"},
    {static_cast<uint16_t>(TIFFTagId::TILE_BYTE_COUNTS), "TileByteCounts"},
    {static_cast<uint16_t>(TIFFTagId::SUB_IFDS), "SubIFDs"},
    {static_cast<uint16_t>(TIFFTagId::JPEG_INT_FMT), "JPEGInterchangeFormat"},
    {static_cast<uint16_t>(TIFFTagId::JPEG_INT_FMT_LEN),
     "JPEGInterchangeFormatLength"},
    {0x0211, "YCbCrCoefficients"},
    {0x0212, "YCbCrSubSampling"},
    {0x0213, "YCbCrPositioning"},
    {0x0214, "ReferenceBlackWhite"},
    {0x02BC, "XMP"},
    {static_cast<uint16_t>(TIFFTagId::SONY_RAW_TYPE), "SonyRAWType"},
    {static_cast<uint16_t>(TIFFTagId::SONY_TONE_CURVE), "SonyToneCurve"},
    {static_cast<uint16_t>(TIFFTagId::SONY_SR2_SUB_IFD_OFFSET),
     "SonySR2SubIFDOffset"},
    {static_cast<uint16_t>(TIFFTagId::SONY_SR2_SUB_IFD_LENGTH),
     "SonySR2SubIFDLength"},
    {static_cast<uint16_t>(TIFFTagId::SONY_SR2_SUB_IFD_KEY),
     "SonySR2SubIFDKey"},
    {static_cast<uint16_t>(TIFFTagId::SONY_BLACK_LEVEL), "SonyBlackLevel"},
    {static_cast<uint16_t>(TIFFTagId::SONY_WHITE_BALANCE), "SonyWhiteBalance"},
    {static_cast<uint16_t>(TIFFTagId::SONY_COLOR_MATRIX), "SonyColorMatrix"},
    {static_cast<uint16_t>(TIFFTagId::SONY_WHITE_LEVEL), "SonyWhiteLevel"},
    {static_cast<uint16_t>(TIFFTagId::CFA_REPEAT_PATTERN_DIM),
     "CFARepeatPatternDim"},
    {static_cast<uint16_t>(TIFFTagId::CFA_PATTERN), "CFAPattern"},
    {0x829A, "ExposureTime"},
    {0x829D, "FNumber"},
    {static_cast<uint16_t>(TIFFTagId::EXIF_IFD), "EXIF IFD"},
    {0x8822, "ExposureProgram"},
    {0x8827, "ISOSpeedRatings"},
    {0x8830, "SensitivityType"},
    {0x8832, "RecommendedExposureIndex"},
    {0x9000, "ExifVersion"},
    {0x9003, "DateTimeOriginal"},
    {0x9004, "DateTimeDigitized"},
    {0x9201, "ShutterSpeedValue"},
    {0x9202, "ApertureValue"},
    {0x9203, "BrightnessValue"},
    {0x9204, "ExposureBiasValue"},
    {0x9205, "MaxApertureValue"},
    {0x9207, "MeteringMode"},
    {0x9208, "LightSource"},
    {0x9209, "Flash"},
    {0x920A, "FocalLength"},
    {static_cast<uint16_t>(TIFFTagId::MAKER_NOTE), "MakerNote"},
    {0x9286, "UserComment"},
    {0x9290, "SubsecTime"},
    {0x9291, "SubsecTimeOriginal"},
    {0x9292, "SubsecTimeDigitized"},
    {0xA000, "FlashpixVersion"},
    {0xA001, "ColorSpace"},
    {0xA002, "PixelXDimension"},
    {0xA003, "PixelYDimension"},
    {0xA005, "InteroperabilityIFD"},
    {0xA20E, "FocalPlaneXResolution"},
    {0xA20F, "FocalPlaneYResolution"},
    {0xA210, "FocalPlaneResolutionUnit"},
    {0xA300, "FileSource"},
    {0xA301, "SceneType"},
    {0xA401, "CustomRendered"},
    {0xA402, "ExposureMode"},
    {0xA403, "WhiteBalance"},
    {0xA404, "DigitalZoomRatio"},
    {0xA405, "FocalLengthIn35mmFilm"},
    {0xA406, "SceneCaptureType"},
    {0xA407, "GainControl"},
    {0xA408, "Contrast"},
    {0xA409, "Saturation"},
    {0xA40A, "Sharpness"},
    {0xA432, "LensSpecification"},
    {0xA434, "LensModel"},
    {0xC4A5, "PrintImageMatching"},
    {static_cast<uint16_t>(TIFFTagId::DNG_VERSION), "DNGVersion"},
    {0xC613, "DNGBackwardVersion"},
    {0xC614, "UniqueCameraModel"},
    {0xC616, "CFAPlaneColor"},
    {0xC617, "CFALayout"},
    {0xC619, "BlackLevelRepeatDim"},
    {static_cast<uint16_t>(TIFFTagId::BLACK_LEVEL), "BlackLevel"},
    {static_cast<uint16_t>(TIFFTagId::WHITE_LEVEL), "WhiteLevel"},
    {0xC61e, "DefaultScale"},
    {static_cast<uint16_t>(TIFFTagId::DEFAULT_CROP_ORIGIN),
     "DefaultCropOrigin"},
    {static_cast<uint16_t>(TIFFTagId::DEFAULT_CROP_SIZE), "DefaultCropSize"},
    {static_cast<uint16_t>(TIFFTagId::COLOR_MATRIX1), "ColorMatrix1"},
    {static_cast<uint16_t>(TIFFTagId::COLOR_MATRIX2), "ColorMatrix2"},
    {0xC623, "CameraCalibration1"},
    {0xC624, "CameraCalibration2"},
    {0xC627, "AnalogBalance"},
    {static_cast<uint16_t>(TIFFTagId::AS_SHOT_NEUTRAL), "AsShotNeutral"},
    {0xC62A, "BaselineExposure"},
    {0xC62B, "BaselineNoise"},
    {0xC62C, "BaselineSharpness"},
    {0xC62D, "BayerGreenSplit"},
    {0xC62E, "LinearResponseLimit"},
    {0xC630, "LensInfo"},
    {0xC632, "AntiAliasStrength"},
    {0xC633, "ChromeBlurRadius"},
    {static_cast<uint16_t>(TIFFTagId::DNG_PRIVATE_DATA), "DNGPrivateData"},
    {0xC65A, "CalibrationIlluminant1"},
    {0xC65B, "CalibrationIlluminant2"},
    {0xC65C, "BestQualityScale"},
    {0xC65D, "RawDataUniqueID"},
    {0xC68B, "OriginalRawFileName"},
    {0xC68D, "ActiveArea"},
    {0xC6F3, "CameraCalibrationSignature"},
    {0xC6F4, "ProfileCalibrationSignature"},
    {0xC6F8, "ProfileName"},
    {0xC6F9, "ProfileHueSatMapDims"},
    {0xC6FA, "ProfileHueSatMapData1"},
    {0xC6FB, "ProfileHueSatMapData2"},
    {0xC6FD, "ProfileEmbedPolicy"},
    {0xC6FE, "ProfileCopyright"},
    {0xC714, "ForwardMatrix1"},
    {0xC715, "ForwardMatrix2"},
    {0xC716, "PreviewApplicationName"},
    {0xC717, "PreviewApplicationVersion"},
    {0xC719, "PreviewSettingsDigest"},
    {0xC71A, "PreviewColorSpace"},
    {0xC71B, "PreviewDateTime"},
    {0xC725, "ProfileLookTableDims"},
    {0xC726, "ProfileLookTableData"},
    {0xC741, "OpcodeList2"},
    {0xC74E, "OpcodeList3"},
    {0xC761, "NoiseProfile"},
    {0xC7A7, "NewRawImageDigest"},
};

const std::string
tag_id_to_name(TIFFTagId tag_id) {
   auto iter = tag_id_to_name_map.find(static_cast<uint16_t>(tag_id));
   if (iter == tag_id_to_name_map.end()) {
      return absl::StrFormat("0x%04X", tag_id);
   } else {
      return iter->second;
   }
}

const std::string
new_subfile_type_handler(const TIFFTag& tag) {
   CHECK_EQ(static_cast<int>(tag.GetId()),
            static_cast<int>(TIFFTagId::NEW_SUBFILE_TYPE));
   CHECK_EQ(static_cast<int>(tag.GetDataType()),
            static_cast<int>(TIFFTagDataType::LONG));
   CHECK_EQ(tag.GetDataCount(), 1U);

   auto tag_data_or = tag.GetDataValue<uint32_t>();
   if (!tag_data_or.ok()) {
      return "";
   }

   if (*tag_data_or % 2 == 1) {
      return "REDUCED_RES_IMAGE";
   }

   return "PRIMARY_IMAGE";
}

const std::string
photo_int_handler(const TIFFTag& tag) {
   CHECK_EQ(static_cast<int>(tag.GetId()),
            static_cast<int>(TIFFTagId::PHOTOMETRIC_INT));
   CHECK_EQ(static_cast<int>(tag.GetDataType()),
            static_cast<int>(TIFFTagDataType::SHORT));
   CHECK_EQ(tag.GetDataCount(), 1U);

   auto tag_data_or = tag.GetDataValue<uint16_t>();
   if (!tag_data_or.ok()) {
      return "";
   }

   switch (*tag_data_or) {
      case cmrw::TIFF_PHOTOMETRIC_INT_GRAY_BLACK_ZERO:
         return "Gray (black is zero)";
      case cmrw::TIFF_PHOTOMETRIC_INT_GRAY_WHITE_ZERO:
         return "Gray (white is zero)";
      case cmrw::TIFF_PHOTOMETRIC_INT_RGB:
         return "RGB";
      case cmrw::TIFF_PHOTOMETRIC_INT_PALETTE:
         return "Palette";
      case cmrw::TIFF_PHOTOMETRIC_INT_TRANS_MASK:
         return "Transparency Mask";
      case cmrw::TIFF_PHOTOMETRIC_INT_CMYK:
         return "CMYK";
      case cmrw::TIFF_PHOTOMETRIC_INT_YCBCR:
         return "YCbCr";
      case cmrw::TIFF_PHOTOMETRIC_INT_CFA:
         return "CFA";
      case cmrw::TIFF_PHOTOMETRIC_INT_LINEAR_RAW:
         return "Linear RAW";
      default:
         return absl::StrFormat("Value: %d", *tag_data_or);
   }
}

const std::string
compression_handler(const TIFFTag& tag) {
   CHECK_EQ(static_cast<int>(tag.GetId()),
            static_cast<int>(TIFFTagId::COMPRESSION));
   CHECK_EQ(static_cast<int>(tag.GetDataType()),
            static_cast<int>(TIFFTagDataType::SHORT));
   CHECK_EQ(tag.GetDataCount(), 1U);

   auto tag_data_or = tag.GetDataValue<uint16_t>();
   if (!tag_data_or.ok()) {
      return "";
   }

   switch (*tag_data_or) {
      case cmrw::TIFF_COMPRESSION_UNCOMPRESSED:
         return "Uncompressed";
      case cmrw::TIFF_COMPRESSION_OLD_STYLE_JPEG:
         return "JPEG (old-style)";
      case cmrw::TIFF_COMPRESSION_JPEG:
         return "JPEG";
      case cmrw::TIFF_COMPRESSION_SONY_ARW:
         return "SONY ARW Compression";
      default:
         return absl::StrFormat("Value: %d", *tag_data_or);
   }
}

const std::string
sony_raw_type_handler(const TIFFTag& tag) {
   CHECK_EQ(static_cast<int>(tag.GetId()),
            static_cast<int>(TIFFTagId::SONY_RAW_TYPE));
   CHECK_EQ(static_cast<int>(tag.GetDataType()),
            static_cast<int>(TIFFTagDataType::SHORT));
   CHECK_EQ(tag.GetDataCount(), 1U);

   auto tag_data_or = tag.GetDataValue<uint16_t>();
   if (!tag_data_or.ok()) {
      return "";
   }

   switch (*tag_data_or) {
      case 0:
         return "Uncompressed RAW";
      case 2:
         return "Compressed RAW";
      case 4:
         return "Lossless Compressed RAW";
      default:
         return absl::StrFormat("Value: %d", *tag_data_or);
   }
}

typedef std::function<const std::string(const TIFFTag&)> CustomValueHandler;

absl::flat_hash_map<TIFFTagId, CustomValueHandler>
    tag_id_to_custom_value_handler_map = {
        {TIFFTagId::NEW_SUBFILE_TYPE, new_subfile_type_handler},
        {TIFFTagId::COMPRESSION, compression_handler},
        {TIFFTagId::PHOTOMETRIC_INT, photo_int_handler},
        {TIFFTagId::SONY_RAW_TYPE, sony_raw_type_handler},
};

void
PrintIFD(const TIFFIFD* ifd) {
   LOG(INFO) << "IFD at offset " << ifd->GetIFDOffset() << " has "
             << ifd->GetNumTags() << " tags";

   uint16_t current_tag = 1;
   for (const TIFFTag& tag : *ifd) {
      VLOG(3) << tag.DebugString();

      auto custom_handler_iter =
          tag_id_to_custom_value_handler_map.find(tag.GetId());
      if (custom_handler_iter != tag_id_to_custom_value_handler_map.end()) {
         LOG(INFO) << "[TAG #" << current_tag << "] "
                   << tag_id_to_name(tag.GetId()) << ": "
                   << custom_handler_iter->second(tag);
      } else {
         LOG(INFO) << "[TAG #" << current_tag << "] " << tag.PrettyPrint();
      }

      current_tag++;
   }
}

int
main(int argc, char* argv[]) {
   google::ParseCommandLineFlags(&argc, &argv, true);
   google::InitGoogleLogging(argv[0]);

   // Global TIFF tag name function override
   TIFFTag::TagIdToStringFn = tag_id_to_name;

   if (FLAGS_rawfile == "") {
      LOG(FATAL) << "Must specify input RAW file with --rawfile <file>";
   }

   std::fstream raw_file(FLAGS_rawfile, std::ios::in | std::ios::binary);
   if (!raw_file) {
      LOG(FATAL) << "Could not open input raw file: " << FLAGS_rawfile;
   }
   LOG(INFO) << "Opened RAW file: " << FLAGS_rawfile;

   FileDataAccessor file_accessor(raw_file);

   FileTypeClassifier classifier;
   EXIT_IF_ERROR(classifier.ClassifyFile(file_accessor, FLAGS_rawfile));

   std::string file_type;
   ASSIGN_OR_EXIT(file_type, classifier.GetFileTypeString());
   LOG(INFO) << "File type classified as: " << file_type;

   std::unique_ptr<FileParser> parser;
   ASSIGN_OR_EXIT(parser, classifier.GetFileParser());

   EXIT_IF_ERROR(parser->Init());

   TIFFFileParser* tiff_parser = dynamic_cast<TIFFFileParser*>(parser.get());
   if (!tiff_parser) {
      LOG(ERROR) << "Dumping tags is only supported for TIFF-based files.";
      return 1;
   }

   for (size_t i = 0; i < tiff_parser->GetNumIFDs(); i++) {
      const TIFFIFD* ifd;
      ASSIGN_OR_EXIT(ifd, tiff_parser->GetIFDAtIndex(i));
      PrintIFD(ifd);
   }

   raw_file.close();

   return 0;
}
