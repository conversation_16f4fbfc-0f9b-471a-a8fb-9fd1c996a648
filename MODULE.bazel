bazel_dep(name = "gflags", version = "2.2.2")
#bazel_dep(name = "glog", version = "0.7.1")
# Using glog at a specific commit > 0.7.1 until a new version is released
bazel_dep(name = "glog", dev_dependency = True)
git_override(
    module_name = "glog",
    remote = "https://github.com/google/glog.git",
    commit = "6c5c692c8e423f651c74de9477ff0b5a59008bcc",
)
bazel_dep(name = "bazel_skylib", version = "1.7.1")
bazel_dep(name = "abseil-cpp", version = "20250512.0")
bazel_dep(name = "googletest", version = "1.17.0")
bazel_dep(name = "hedron_compile_commands", dev_dependency = True)
git_override(
    module_name = "hedron_compile_commands",
    remote = "https://github.com/hedronvision/bazel-compile-commands-extractor.git",
    commit = "4f28899228fb3ad0126897876f147ca15026151e",
    patches = ["//patches:hedron-compile-commands-issue-219.patch"],
    patch_strip = 1,
)
