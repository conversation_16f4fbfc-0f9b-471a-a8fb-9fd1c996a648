#ifndef SONY_RAW_PIPELINE_SOURCE_H_
#define SONY_RAW_PIPELINE_SOURCE_H_

#include <memory>
#include <set>

#include "absl/status/status.h"
#include "absl/status/statusor.h"

#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/sony_raw_file_parser.h"
#include "camraw/tiff.h"
#include "camraw/tiff_pipeline_source.h"

namespace cmrw {

constexpr TIFFCompressionType TIFF_COMPRESSION_SONY_ARW = 32767;

class SonyRawPipelineSource : public TIFFPipelineSource {
   public:
      SonyRawPipelineSource(SonyRawFileParser* file_parser)
          : TIFFPipelineSource(file_parser, "SonyRawPipelineSource") {
         supported_compression_types.insert(TIFF_COMPRESSION_JPEG);
         supported_compression_types.insert(TIFF_COMPRESSION_SONY_ARW);
      }
      absl::Status Init() override;
      absl::Status ProcessChunk(const PipelineSourceChunk& chunk,
                                ImageBuf<uint16_t>& output) override;
      absl::Status MutateMetadata(ImageMeta& image_meta) override;

   protected:
      absl::Status FetchAndSetMetadata(ImageMetaId id,
                                       ImageMeta& image_meta) override;
      absl::StatusOr<ChunkConfiguration> GetChunkConfiguration() override;

   private:
      std::unique_ptr<uint16_t[]> sony_tone_curve_ = nullptr;
};

}  // namespace cmrw

#endif /* SONY_RAW_PIPELINE_SOURCE_H_ */
