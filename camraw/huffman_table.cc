#include <cassert>

#include "absl/status/statusor.h"
#include "absl/strings/str_format.h"
#include "glog/logging.h"

#include "camraw/huffman_table.h"
#include "camraw/status_macros.h"

namespace cmrw {

absl::Status
HuffmanTable::SetTableClass(HuffmanTableClass table_class) {
   if (table_class != HuffmanTableClass::AC &&
       table_class != HuffmanTableClass::DC) {
      return absl::FailedPreconditionError(
          absl::StrFormat("Invalid table class %d", table_class));
   }

   table_class_ = table_class;
   return absl::OkStatus();
}

absl::Status
HuffmanTable::SetTableId(uint8_t table_id) {
   if (table_id > 4) {
      return absl::FailedPreconditionError(
          absl::StrFormat("Invalid table id %d", table_id));
   }

   table_id_ = table_id;
   return absl::OkStatus();
}

absl::Status
HuffmanTable::ParseTable() {
   uint32_t data_offset = accessor_offset_;

   uint8_t tcth;
   RETURN_IF_ERROR(data_accessor_->CopyBytes(data_offset, sizeof(tcth), &tcth),
                   VLOG(4) << "Failed to copy tcth data.");
   data_offset += sizeof(tcth);

   RETURN_IF_ERROR(SetTableClass(static_cast<HuffmanTableClass>(tcth >> 4)),
                   VLOG(4) << "Failed to set table class.");
   if (VLOG_IS_ON(5)) {
      if (table_class_ == HuffmanTableClass::AC) {
         VLOG(5) << "Table class: AC";
      } else {
         VLOG(5) << "Table class: DC";
      }
   }

   RETURN_IF_ERROR(SetTableId(tcth & 0x0F), VLOG(4)
                                                << "Failed to set table id.");
   VLOG(5) << "Table id: " << static_cast<uint32_t>(table_id_);

   RETURN_IF_ERROR(
       data_accessor_->CopyBytes(data_offset, sizeof(num_codes_of_len_),
                                 &num_codes_of_len_),
       VLOG(4) << "Failed to copy num_codes_of_len data.");
   data_offset += sizeof(num_codes_of_len_);

   VLOG(5) << "Number of codes with given bit length:";
   for (int i = 0; i < 16; i++) {
      num_total_codes_ += num_codes_of_len_[i];
      VLOG(5) << i + 1 << ": " << static_cast<uint32_t>(num_codes_of_len_[i]);
   }
   VLOG(5) << "Total number of codes:" << num_total_codes_;

   // This isn't an exact equals check because there may be more tables
   // after this one
   CHECK_LE(data_offset + (num_total_codes_ * sizeof(uint8_t)),
            accessor_offset_ + accessor_bytes_available_);
   symbol_values_.resize(num_total_codes_);
   RETURN_IF_ERROR(data_accessor_->CopyBytes(data_offset,
                                             num_total_codes_ * sizeof(uint8_t),
                                             symbol_values_.data()),
                   VLOG(4) << "Failed to copy symbol values.");
   data_offset += num_total_codes_ * sizeof(uint8_t);
   // Keep track of how many bytes (other than marker and length field)
   // was used by this table to see if more tables need parsing
   accessor_bytes_used_ = data_offset - accessor_offset_;

   if (VLOG_IS_ON(5)) {
      for (uint32_t i = 0; i < num_total_codes_; i++) {
         VLOG(5) << "idx: " << i << " symbol value: "
                 << static_cast<uint32_t>(symbol_values_[i]);
      }
   }

   return absl::OkStatus();
}

absl::Status
HuffmanTable::ConstructDecodeTable() {
   // Resize the decode table vectors appropriately
   for (int i = 0; i < 16; i++) {
      // The ith entry corresponds to code words of len i+1 and holds up to
      // 2^(i+1) symbol values. We keep track of the max code value per each
      // code length so we don't always have to allocate 2^(i+1) entries.
      decode_table_[i].resize(max_code_val_of_len_[i] + 1);
   }

   // Generate all the codes and insert to the decode table with the
   // corresponding symbol value.
   uint16_t code_candidate = 0;
   uint16_t num_codes_inserted = 0;
   for (uint8_t i = 0; i < 16; i++) {
      uint8_t code_len = i + 1;
      uint8_t num_codes_of_this_len = num_codes_of_len_[i];
      // Make sure there aren't more than 2^code_len codes in this bit length
      CHECK_LE(num_codes_of_this_len, 1 << code_len);
      for (uint j = 0; j < num_codes_of_this_len; j++) {
         RETURN_IF_ERROR(
             InsertToDecodeTable(code_len, code_candidate,
                                 symbol_values_[num_codes_inserted]),
             VLOG(4) << "Failed to add code to decode table");
         code_candidate++;
         num_codes_inserted++;
      }
      // Shift code candidate to the new code length
      code_candidate <<= 1;
   }

   return absl::OkStatus();
}

absl::Status
HuffmanTable::Init() {
   RETURN_IF_ERROR(ParseTable(), VLOG(4) << "Failed to parse Huffman table");

   RETURN_IF_ERROR(ConstructDecodeTable(),
                   VLOG(4) << "Failed to construct decoding table");

   initialized_ = true;
   return absl::OkStatus();
}

absl::Status
HuffmanTable::InsertToDecodeTable(uint8_t code_len, uint16_t code_val,
                                  uint8_t symbol_val) {
   assert(code_len > 0);
   assert(code_len <= 16);
   assert(code_val <= (1 << code_len));

   DecodeTableEntry& entry = decode_table_[code_len - 1][code_val];
   if (entry.populated) {
      std::string error_msg = absl::StrFormat(
          "Attempting to insert code that already exists len: %d val: %d",
          code_len, code_val);
      VLOG(4) << error_msg;
      return absl::FailedPreconditionError(error_msg);
   }

   VLOG(5) << "Inserted entry to decode table code_len: "
           << static_cast<uint32_t>(code_len) << " code_val: " << code_val
           << " symbol_val: " << static_cast<uint32_t>(symbol_val);

   entry.populated = true;
   entry.symbol_value = symbol_val;

   if (code_len < shortest_code_len_) {
      shortest_code_len_ = code_len;
   }
   if (code_len > longest_code_len_) {
      longest_code_len_ = code_len;
   }
   if (code_val > max_code_val_of_len_[code_len - 1]) {
      max_code_val_of_len_[code_len - 1] = code_val;
   }

   return absl::OkStatus();
}

absl::StatusOr<uint8_t>
HuffmanTable::Decode(uint8_t code_len, uint16_t code_val) const {
   assert(code_len > 0);
   assert(code_len <= 16);
   assert(code_val <= (1 << code_len));
   assert(initialized_);

   const DecodeTableEntry& entry = decode_table_[code_len - 1][code_val];
   if (!entry.populated) {
      if (VLOG_IS_ON(6)) {
         VLOG(6) << absl::StrFormat(
             "Code not found in decode table len: %d val: %d", code_len,
             code_val);
      }
      return absl::NotFoundError("");
   }

   return entry.symbol_value;
}

}  // namespace cmrw
