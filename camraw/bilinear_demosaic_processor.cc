#include <glog/logging.h>

#include "camraw/bilinear_demosaic_processor.h"
#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"

namespace cmrw {

absl::Status
BilinearDemosaicProcessor::Init(const ImageMeta& image_meta) {
   auto photo_int_or = image_meta.GetOne<ImageMetaPhotoInt>(
       ImageMetaId::PHOTOMETRIC_INTERPRETATION);
   CHECK(photo_int_or.ok());
   CHECK_EQ(static_cast<int>(*photo_int_or),
            static_cast<int>(ImageMetaPhotoInt::CFA));

   // 2x2 CFA repeat
   auto cfa_dim_or =
       image_meta.GetMulti<uint16_t>(ImageMetaId::CFA_PATTERN_DIM);
   CHECK(cfa_dim_or.ok());
   CHECK_EQ((*cfa_dim_or)[0], 2);
   CHECK_EQ((*cfa_dim_or)[1], 2);

   // RGGB CFA pattern = 0,1,1,2
   auto cfa_ptrn_or = image_meta.GetMulti<uint16_t>(ImageMetaId::CFA_PATTERN);
   CHECK(cfa_ptrn_or.ok());
   CHECK_EQ((*cfa_ptrn_or)[0], 0);
   CHECK_EQ((*cfa_ptrn_or)[1], 1);
   CHECK_EQ((*cfa_ptrn_or)[2], 1);
   CHECK_EQ((*cfa_ptrn_or)[3], 2);

   return absl::OkStatus();
}

void
BilinearDemosaicProcessor::LogStart() {
   LOG(INFO) << "Demosaicing image using bilinear interpolation...";
}

// Edge-weighted average of the pixels directly above/below and left/right
uint16_t
demosaic_cross_avg(const ImageBuf<uint16_t>& raw_data, uint32_t row,
                   uint32_t col) {
   int left_val = 0, right_val = 0;
   int top_val = 0, bottom_val = 0;
   bool has_left = false, has_right = false;
   bool has_top = false, has_bottom = false;

   // Get horizontal pixels
   if (col > 0) {  // Pixel to the left
      left_val = raw_data(row, col - 1);
      has_left = true;
   }
   if (col < (raw_data.get_width() - 1)) {  // Pixel to the right
      right_val = raw_data(row, col + 1);
      has_right = true;
   }

   // Get vertical pixels
   if (row > 0) {  // Pixel above
      top_val = raw_data(row - 1, col);
      has_top = true;
   }
   if (row < (raw_data.get_height() - 1)) {  // Pixel below
      bottom_val = raw_data(row + 1, col);
      has_bottom = true;
   }

   // Check if we have both pixels in at least one direction
   bool has_horizontal = has_left && has_right;
   bool has_vertical = has_top && has_bottom;

   // If we have both pixels in at least one direction, use edge-weighted scheme
   if (has_horizontal && has_vertical) {
      // Full edge-weighted calculation when we have all four neighbors
      int h_diff = std::abs(left_val - right_val);
      int h_sum = left_val + right_val;
      int v_diff = std::abs(top_val - bottom_val);
      int v_sum = top_val + bottom_val;

      // Avoid division by zero
      if (h_diff + v_diff > 0) {
         return static_cast<uint16_t>(0.5 * (v_sum * h_diff + h_sum * v_diff) /
                                      (h_diff + v_diff));
      } else {
         // If no gradient (flat area), use simple average
         return static_cast<uint16_t>((h_sum + v_sum) / 4);
      }
   } else if (has_horizontal) {
      // Only horizontal direction is complete
      return static_cast<uint16_t>((left_val + right_val) / 2);
   } else if (has_vertical) {
      // Only vertical direction is complete
      return static_cast<uint16_t>((top_val + bottom_val) / 2);
   } else {
      // Handle partial neighborhoods (tile boundaries)
      int sum = 0;
      int count = 0;

      if (has_left) {
         sum += left_val;
         count++;
      }
      if (has_right) {
         sum += right_val;
         count++;
      }
      if (has_top) {
         sum += top_val;
         count++;
      }
      if (has_bottom) {
         sum += bottom_val;
         count++;
      }

      return (count > 0) ? static_cast<uint16_t>(sum / count) : 0;
   }
}

// Average of the pixels directly above/below
uint16_t
demosaic_vert_avg(const ImageBuf<uint16_t>& raw_data, uint32_t row,
                  uint32_t col) {
   uint32_t accumulator = 0, num_samples = 0;

   if (row > 0) {  // Pixel above
      accumulator += raw_data(row - 1, col);
      num_samples++;
   }
   if (row < (raw_data.get_height() - 1)) {  // Pixel below
      accumulator += raw_data(row + 1, col);
      num_samples++;
   }

   return (num_samples > 0) ? static_cast<uint16_t>(accumulator / num_samples)
                            : 0;
}

// Average of the pixels directly to the left/right
uint16_t
demosaic_horiz_avg(const ImageBuf<uint16_t>& raw_data, uint32_t row,
                   uint32_t col) {
   uint32_t accumulator = 0, num_samples = 0;

   if (col > 0) {  // Pixel to the left
      accumulator += raw_data(row, col - 1);
      num_samples++;
   }
   if (col < (raw_data.get_width() - 1)) {  // Pixel to the right
      accumulator += raw_data(row, col + 1);
      num_samples++;
   }

   return (num_samples > 0) ? static_cast<uint16_t>(accumulator / num_samples)
                            : 0;
}

// Edge-weighted average of the pixels diagonally around the current pixel
uint16_t
demosaic_diag_avg(const ImageBuf<uint16_t>& raw_data, uint32_t row,
                  uint32_t col) {
   int ne_val = 0, sw_val = 0;
   int nw_val = 0, se_val = 0;
   bool has_ne = false, has_sw = false;
   bool has_nw = false, has_se = false;

   // Northeast-Southwest diagonal
   if (row > 0 && col < (raw_data.get_width() - 1)) {
      ne_val = raw_data(row - 1, col + 1);
      has_ne = true;
   }
   if (row < (raw_data.get_height() - 1) && col > 0) {
      sw_val = raw_data(row + 1, col - 1);
      has_sw = true;
   }

   // Northwest-Southeast diagonal
   if (row > 0 && col > 0) {
      nw_val = raw_data(row - 1, col - 1);
      has_nw = true;
   }
   if (row < (raw_data.get_height() - 1) && col < (raw_data.get_width() - 1)) {
      se_val = raw_data(row + 1, col + 1);
      has_se = true;
   }

   // Check if we have both pixels in at least one diagonal
   bool has_ne_sw = has_ne && has_sw;
   bool has_nw_se = has_nw && has_se;

   // If we have both diagonals, use edge-weighted scheme
   if (has_ne_sw && has_nw_se) {
      // Full edge-weighted calculation when we have all four diagonal neighbors
      int ne_sw_diff = std::abs(ne_val - sw_val);
      int ne_sw_sum = ne_val + sw_val;
      int nw_se_diff = std::abs(nw_val - se_val);
      int nw_se_sum = nw_val + se_val;

      // Avoid division by zero
      if (ne_sw_diff + nw_se_diff > 0) {
         return static_cast<uint16_t>(
             0.5 * (nw_se_sum * ne_sw_diff + ne_sw_sum * nw_se_diff) /
             (ne_sw_diff + nw_se_diff));
      } else {
         // If no gradient (flat area), use simple average
         return static_cast<uint16_t>((ne_sw_sum + nw_se_sum) / 4);
      }
   } else if (has_ne_sw) {
      // Only NE-SW diagonal is complete
      return static_cast<uint16_t>((ne_val + sw_val) / 2);
   } else if (has_nw_se) {
      // Only NW-SE diagonal is complete
      return static_cast<uint16_t>((nw_val + se_val) / 2);
   } else {
      // Handle partial neighborhoods (tile boundaries)
      int sum = 0;
      int count = 0;

      if (has_ne) {
         sum += ne_val;
         count++;
      }
      if (has_sw) {
         sum += sw_val;
         count++;
      }
      if (has_nw) {
         sum += nw_val;
         count++;
      }
      if (has_se) {
         sum += se_val;
         count++;
      }

      return (count > 0) ? static_cast<uint16_t>(sum / count) : 0;
   }
}

absl::Status
BilinearDemosaicProcessor::Run(const ImageBuf<uint16_t>& input_buffer,
                               ImageBuf<uint16_t>& output_buffer) {
   CHECK_EQ(input_buffer.get_height(), output_buffer.get_height());
   CHECK_EQ(input_buffer.get_width(), output_buffer.get_width());
   CHECK_EQ(input_buffer.get_components(), 1U);
   CHECK_EQ(output_buffer.get_components(), 3U);

   for (uint32_t row = 0; row < input_buffer.get_height(); row++) {
      for (uint32_t col = 0; col < input_buffer.get_width(); col++) {
         uint16_t red_val = 0, green_val = 0, blue_val = 0;

         // Determine the CFA color of the current pixel
         CFAFilterColor cur_pixel_color;
         if (row % 2 == 0) {     // odd row (starts from 0)
            if (col % 2 == 0) {  // odd col (starts from 0)
               cur_pixel_color = CFAFilterColor::RED;
            } else {
               cur_pixel_color = CFAFilterColor::GREEN1;
            }
         } else {
            if (col % 2 == 0) {  // odd col (starts from 0)
               cur_pixel_color = CFAFilterColor::GREEN2;
            } else {
               cur_pixel_color = CFAFilterColor::BLUE;
            }
         }

         uint16_t orig_val = input_buffer(row, col);

         switch (cur_pixel_color) {
            case CFAFilterColor::RED:
               red_val = orig_val;
               green_val = demosaic_cross_avg(input_buffer, row, col);
               blue_val = demosaic_diag_avg(input_buffer, row, col);
               break;
            case CFAFilterColor::GREEN1:
               red_val = demosaic_horiz_avg(input_buffer, row, col);
               green_val = orig_val;
               blue_val = demosaic_vert_avg(input_buffer, row, col);
               break;
            case CFAFilterColor::GREEN2:
               red_val = demosaic_vert_avg(input_buffer, row, col);
               green_val = orig_val;
               blue_val = demosaic_horiz_avg(input_buffer, row, col);
               break;
            case CFAFilterColor::BLUE:
               red_val = demosaic_diag_avg(input_buffer, row, col);
               green_val = demosaic_cross_avg(input_buffer, row, col);
               blue_val = orig_val;
               break;
         }

         // Output the RGB values for this input pixel
         output_buffer(row, col, 0) = red_val;
         output_buffer(row, col, 1) = green_val;
         output_buffer(row, col, 2) = blue_val;
      }
   }

   return absl::OkStatus();
}

absl::Status
BilinearDemosaicProcessor::MutateMetadata(ImageMeta& image_meta) {
   image_meta.SetOne<uint16_t>(ImageMetaId::SAMPLES_PER_PIXEL, 3);
   VLOG(3) << "Set metadata SAMPLES_PER_PIXEL = 3";

   image_meta.SetOne<ImageMetaPhotoInt>(ImageMetaId::PHOTOMETRIC_INTERPRETATION,
                                        ImageMetaPhotoInt::RGB);
   VLOG(3) << "Set metadata PHOTOMETRIC_INT = RGB";

   return absl::OkStatus();
}

}  // namespace cmrw
