#include "absl/status/status.h"

#include "camraw/imagebuf.h"
#include "camraw/pipeline_accumulator.h"

namespace cmrw {

absl::Status
PipelineAccumulator::Accumulate(uint16_t y_offset, uint16_t x_offset,
                                const ImageBuf<uint16_t>& data) {
   buf_->copy_tile_in_from(data, y_offset, x_offset);
   /*
    * TODO: We can do better tracking than this by actually tracking each
    * tile - that way we can also make sure we don't accumulate duplicate
    * tiles.
    */
   area_ += data.get_height() * data.get_width();

   if (completion_func_) {
      if (IsComplete()) {
         return completion_func_(*buf_);
      }
   }

   return absl::OkStatus();
}

bool
PipelineAccumulator::IsComplete() {
   return area_ == image_height_ * image_width_;
}

std::unique_ptr<ImageBuf<uint16_t>>
PipelineAccumulator::ReleaseBuffer() {
   return std::move(buf_);
}

void
PipelineAccumulator::RegisterCompletionFunc(
    std::function<absl::Status(const ImageBuf<uint16_t>&)> func) {
   completion_func_ = func;
}

}  // namespace cmrw
