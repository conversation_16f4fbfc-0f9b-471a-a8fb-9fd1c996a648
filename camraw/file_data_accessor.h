#ifndef FILE_DATA_ACCESSOR_H_
#define FILE_DATA_ACCESSOR_H_

#include "absl/status/status.h"

#include "camraw/data_accessor.h"

namespace cmrw {

class FileDataAccessor : public DataAccessor {
   public:
      FileDataAccessor(std::fstream& file) : file_(file) {}
      absl::Status CopyBytes(uint32_t read_offset, uint32_t num_bytes,
                             void* dst) override;

   private:
      std::fstream& file_;
};

}  // namespace cmrw

#endif  // FILE_DATA_ACCESSOR_H_
