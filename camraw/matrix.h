#ifndef MATRIX_H_
#define MATRIX_H_

#include <cassert>
#include <cstddef>
#include <string>
#include <vector>

#include "absl/status/statusor.h"
#include "absl/strings/str_cat.h"
#include "absl/strings/str_format.h"

namespace cmrw {

template <typename T>
class Matrix {
   public:
      Matrix(size_t num_rows, size_t num_cols)
          : num_rows_(num_rows), num_cols_(num_cols) {
         values_ = std::make_unique<T[]>(num_rows * num_cols);
      }

      Matrix(size_t num_rows, size_t num_cols, const std::vector<T>& values)
          : num_rows_(num_rows), num_cols_(num_cols) {
         assert(values.size() == num_rows_ * num_cols_);

         values_ = std::make_unique<T[]>(num_rows * num_cols);
         for (size_t i = 0; i < num_rows_; i++) {
            for (size_t j = 0; j < num_cols_; j++) {
               (*this)(i, j) = values[i * num_cols_ + j];
            }
         }
      }

      T& operator()(size_t row, size_t col) {
         assert(row < num_rows_ && col < num_cols_);
         return values_[row * num_cols_ + col];
      }

      T operator()(size_t row, size_t col) const {
         assert(row < num_rows_ && col < num_cols_);
         return values_[row * num_cols_ + col];
      }

      const std::string DisplayString() const {
         std::string result =
             absl::StrFormat("Matrix(%d,%d)=[", num_rows_, num_cols_);

         for (size_t i = 0; i < num_rows_; i++) {
            absl::StrAppend(&result, "[");
            for (size_t j = 0; j < num_cols_; j++) {
               absl::StrAppend(&result, (*this)(i, j));
               if (j < num_cols_ - 1) {
                  absl::StrAppend(&result, ",");
               }
            }
            absl::StrAppend(&result, "]");
            if (i < num_rows_ - 1) {
               absl::StrAppend(&result, ",");
            }
         }
         absl::StrAppend(&result, "]");
         return result;
      }

      Matrix<T> Multiply(const Matrix<T>& other) const {
         assert(num_cols_ == other.num_rows_);

         Matrix<T> result(num_rows_, other.num_cols_);

         for (size_t i = 0; i < num_rows_; i++) {
            for (size_t j = 0; j < other.num_cols_; j++) {
               T sum = T();
               for (size_t k = 0; k < num_cols_; k++) {
                  sum += (*this)(i, k) * other(k, j);
               }
               result(i, j) = sum;
            }
         }

         return result;
      }

      absl::StatusOr<Matrix<T>> invert() {
         if (num_rows_ != num_cols_) {
            return absl::FailedPreconditionError("Matrix is not invertible.");
         }
         Matrix inverse(num_rows_, num_cols_);

         // Initialize the augmented matrix with the original matrix and the
         // identity matrix
         std::vector<std::vector<T>> augmented(num_rows_,
                                               std::vector<T>(2 * num_cols_));
         for (size_t i = 0; i < num_rows_; i++) {
            for (size_t j = 0; j < num_cols_; j++) {
               augmented[i][j] = (*this)(i, j);
            }
            augmented[i][i + num_cols_] = 1.0;  // Set identity matrix part
         }

         // Perform Gaussian elimination
         for (size_t i = 0; i < num_rows_; i++) {
            // Find the pivot row and swap
            double maxElement = std::abs(augmented[i][i]);
            size_t pivotRow = i;
            for (size_t k = i + 1; k < num_rows_; k++) {
               if (std::abs(augmented[k][i]) > maxElement) {
                  maxElement = std::abs(augmented[k][i]);
                  pivotRow = k;
               }
            }

            if (maxElement == 0) {
               return absl::FailedPreconditionError(
                   "Matrix is not invertible.");
            }

            // Swap the current row with the pivot row
            if (pivotRow != i) {
               std::swap(augmented[i], augmented[pivotRow]);
            }

            // Normalize the pivot row
            double pivot = augmented[i][i];
            for (size_t j = 0; j < 2 * num_cols_; j++) {
               augmented[i][j] /= pivot;
            }

            // Eliminate the current column in all other rows
            for (size_t k = 0; k < num_rows_; k++) {
               if (k != i) {
                  double factor = augmented[k][i];
                  for (size_t j = 0; j < 2 * num_cols_; j++) {
                     augmented[k][j] -= factor * augmented[i][j];
                  }
               }
            }
         }

         // Extract the inverse matrix from the augmented matrix
         for (size_t i = 0; i < num_rows_; i++) {
            for (size_t j = 0; j < num_cols_; j++) {
               inverse(i, j) = augmented[i][j + num_cols_];
            }
         }

         return inverse;
      }

   private:
      size_t num_rows_, num_cols_;
      std::unique_ptr<T[]> values_;
};

}  // namespace cmrw

#endif /* MATRIX_H_ */
