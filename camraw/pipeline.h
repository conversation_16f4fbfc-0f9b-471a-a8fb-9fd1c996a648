#ifndef PIPELINE_H_
#define PIPELINE_H_

#include <list>

#include "absl/status/status.h"

#include "camraw/pipeline_element.h"
#include "camraw/pipeline_processor.h"
#include "camraw/pipeline_sink.h"
#include "camraw/pipeline_source.h"

namespace cmrw {

class Pipeline {
   public:
      Pipeline() : last_output_spec_({false, false}) {}
      absl::Status AddSource(std::unique_ptr<PipelineSource> source);
      absl::Status AddProcessor(std::unique_ptr<PipelineProcessor> processor);
      absl::Status AddSink(std::unique_ptr<PipelineSink> sink);
      absl::Status Run(unsigned int num_threads);

   private:
      absl::Status ValidateNewElement(const PipelineElement& element);

      PipelineElementIOSpec last_output_spec_;
      std::list<std::unique_ptr<PipelineElement>> elements_;
};

}  // namespace cmrw

#endif /* PIPELINE_H_ */
