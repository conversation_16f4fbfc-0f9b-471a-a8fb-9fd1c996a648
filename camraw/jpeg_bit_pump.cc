#include <cassert>
#include <cstdint>

#include "absl/status/status.h"
#include "absl/status/statusor.h"
#include "absl/strings/str_format.h"
#include "glog/logging.h"

#include "camraw/bit_pump.h"
#include "camraw/jpeg_bit_pump.h"
#include "camraw/status_macros.h"

namespace cmrw {

absl::Status
JPEGBitPump::CopyNextByte() {
   RETURN_IF_ERROR(BitPump::CopyNextByte());

   // Marker / byte stuffing handling
   RETURN_IF_ERROR(CheckAndHandleByteStuffing());

   return absl::OkStatus();
}

absl::Status
JPEGBitPump::CheckAndHandleByteStuffing() {
   if (cur_byte_ == 0xFF) {
      uint8_t next_byte;
      ASSIGN_OR_RETURN(next_byte, PeekNextByte(),
                       VLOG(5) << "Failed to peek next byte.");
      if (next_byte == 0x00) {
         // cur_byte is an actual encoded 0xFF - discard the 0x00 byte
         next_byte_offset_++;
      } else {
         // we detected a marker inside the encoded bit stream
         VLOG(5) << absl::StrFormat(
             "Marker inside bit stream - byte offset: %d marker: "
             "0x%02X%02X",
             next_byte_offset_ - 2, cur_byte_, next_byte);
         return absl::UnimplementedError(
             "Markers inside bit stream not supported");
      }
   }

   return absl::OkStatus();
}

absl::StatusOr<uint16_t>
JPEGBitPump::SkipMarker() {
   uint16_t marker = 0;
   uint8_t next_byte;
   ASSIGN_OR_RETURN(next_byte, PeekNextByte(),
                    VLOG(5) << "Failed to peek next byte.");
   CHECK_EQ(next_byte, 0xFF);
   next_byte_offset_++;
   marker = 0xFF00;

   ASSIGN_OR_RETURN(next_byte, PeekNextByte(),
                    VLOG(5) << "Failed to peek next byte.");
   CHECK_NE(next_byte, 0x00);
   CHECK_NE(next_byte, 0xFF);
   next_byte_offset_++;
   marker |= next_byte;

   auto status = CopyNextByte();
   if (!status.ok()) {
      if (absl::IsOutOfRange(status)) {
         // No more data after marker, reset state
         cur_byte_ = 0;
         cur_bits_left_ = 0;
      } else {
         return status;
      }
   }

   return marker;
}

}  // namespace cmrw
