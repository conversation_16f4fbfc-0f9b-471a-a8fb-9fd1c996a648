#include "absl/status/status.h"
#include "absl/status/statusor.h"
#include "absl/strings/str_format.h"
#include "gtest/gtest.h"

#include "camraw/memory_data_accessor.h"
#include "camraw/tiff_tag.h"

using cmrw::MemoryDataAccessor;
using cmrw::TIFFTag;
using cmrw::TIFFTagDataType;
using cmrw::TIFFTagId;

TEST(TiffTagTest, BasicFields) {
   TIFFTag tag(static_cast<TIFFTagId>(0), TIFFTagDataType::LONG, 1, 0);
   EXPECT_EQ(tag.GetId(), static_cast<TIFFTagId>(0));
   EXPECT_EQ(tag.GetDataType(), TIFFTagDataType::LONG);
   EXPECT_EQ(tag.GetDataCount(), 1);
}

TEST(TiffTagTest, GetDataValueInDataOffset) {
   TIFFTag tag1(static_cast<TIFFTagId>(0), TIFFTagDataType::LONG, 1,
                0x12345678);

   auto val1 = tag1.GetDataValue<uint32_t>();
   EXPECT_TRUE(val1.ok());
   EXPECT_EQ(val1.value(), 0x12345678);

   val1 = tag1.GetDataValue<uint16_t>();  // Data type too small
   EXPECT_FALSE(val1.ok());

   TIFFTag tag2(static_cast<TIFFTagId>(0), TIFFTagDataType::SHORT, 1,
                0x0000AABB);

   auto val2 = tag2.GetDataValue<uint16_t>();
   EXPECT_TRUE(val2.ok());
   EXPECT_EQ(val2.value(), 0xAABB);

   val2 = tag2.GetDataValue<uint8_t>();  // Data type too small
   EXPECT_FALSE(val2.ok());

   TIFFTag tag3(static_cast<TIFFTagId>(0), TIFFTagDataType::BYTE, 1,
                0x00000042);

   auto val3 = tag3.GetDataValue<uint16_t>();
   EXPECT_TRUE(val3.ok());
   EXPECT_EQ(val3.value(), 0x42);

   TIFFTag tag4(static_cast<TIFFTagId>(0), TIFFTagDataType::BYTE, 4,
                0x11223344);
   auto val4 = tag4.GetDataValue<uint16_t>();  // can't use GetDataValue for
                                               // data_count > 1
   EXPECT_FALSE(val4.ok());
}

TEST(TiffTagTest, GetDataOffsetAsValue) {
   TIFFTag tag(static_cast<TIFFTagId>(0), TIFFTagDataType::BYTE, 4, 0x11223344);
   auto val_or = tag.GetDataOffsetAsValue();
   EXPECT_TRUE(val_or.ok());
   EXPECT_EQ(*val_or, 0x11223344);
}

TEST(TiffTagTest, GetDataValueAtIdxInDataOffset) {
   TIFFTag tag1(static_cast<TIFFTagId>(0), TIFFTagDataType::BYTE, 4,
                0x44332211);

   auto val1 = tag1.GetDataValueAtIdx<uint8_t>(0);
   EXPECT_TRUE(val1.ok());
   EXPECT_EQ(val1.value(), 0x11);

   val1 = tag1.GetDataValueAtIdx<uint8_t>(1);
   EXPECT_TRUE(val1.ok());
   EXPECT_EQ(val1.value(), 0x22);

   val1 = tag1.GetDataValueAtIdx<uint8_t>(2);
   EXPECT_TRUE(val1.ok());
   EXPECT_EQ(val1.value(), 0x33);

   val1 = tag1.GetDataValueAtIdx<uint8_t>(3);
   EXPECT_TRUE(val1.ok());
   EXPECT_EQ(val1.value(), 0x44);

   val1 = tag1.GetDataValueAtIdx<uint8_t>(4);
   EXPECT_FALSE(val1.ok());

   TIFFTag tag2(static_cast<TIFFTagId>(0), TIFFTagDataType::SHORT, 2,
                0xBBBBAAAA);

   auto val2 = tag2.GetDataValueAtIdx<uint16_t>(0);
   EXPECT_TRUE(val2.ok());
   EXPECT_EQ(val2.value(), 0xAAAA);

   val2 = tag2.GetDataValueAtIdx<uint16_t>(1);
   EXPECT_TRUE(val2.ok());
   EXPECT_EQ(val2.value(), 0xBBBB);

   val2 = tag2.GetDataValueAtIdx<uint16_t>(2);  // index out of bounds
   EXPECT_FALSE(val2.ok());

   val2 = tag2.GetDataValueAtIdx<uint8_t>(0);  // type too small
   EXPECT_FALSE(val2.ok());

   TIFFTag tag3(static_cast<TIFFTagId>(0), TIFFTagDataType::LONG, 1,
                0x42424242);

   auto val3 = tag3.GetDataValueAtIdx<uint32_t>(0);
   EXPECT_TRUE(val3.ok());
   EXPECT_EQ(val3.value(), 0x42424242);

   val3 = tag3.GetDataValueAtIdx<uint32_t>(1);  // index out of bounds
   EXPECT_FALSE(val3.ok());

   val3 = tag3.GetDataValueAtIdx<uint16_t>(0);  // type too small
   EXPECT_FALSE(val3.ok());
}

TEST(TiffTagTest, GetAllDataValuesInDataOffset) {
   TIFFTag tag1(static_cast<TIFFTagId>(0), TIFFTagDataType::BYTE, 4,
                0x03020100);

   auto result = tag1.GetAllDataValues<uint8_t>();
   EXPECT_TRUE(result.ok());

   std::unique_ptr<uint8_t[]> values = std::move(result.value());
   for (unsigned int i = 0; i < 4; i++) {
      EXPECT_EQ(values[i], i);
   }

   TIFFTag tag2(static_cast<TIFFTagId>(0), TIFFTagDataType::SHORT, 2,
                0xBBBBAAAA);

   auto result2 = tag2.GetAllDataValues<uint16_t>();
   EXPECT_TRUE(result2.ok());

   std::unique_ptr<uint16_t[]> values2 = std::move(result2.value());
   EXPECT_EQ(values2[0], 0xAAAA);
   EXPECT_EQ(values2[1], 0xBBBB);

   auto result2a = tag2.GetAllDataValues<uint8_t>();  // type too small
   EXPECT_FALSE(result2a.ok());

   TIFFTag tag3(static_cast<TIFFTagId>(0), TIFFTagDataType::LONG, 1,
                0x42424242);
   auto result3 = tag3.GetAllDataValues<uint32_t>();
   EXPECT_TRUE(result3.ok());

   std::unique_ptr<uint32_t[]> values3 = std::move(result3.value());
   EXPECT_EQ(values3[0], 0x42424242);

   auto result3a = tag3.GetAllDataValues<uint16_t>();  // type too small
   EXPECT_FALSE(result3a.ok());
}

TEST(TiffTagTest, MemoryDataAccessor) {
   uint8_t external_data[] = {0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77};
   MemoryDataAccessor mda(external_data, 8);

   TIFFTag tag1(static_cast<TIFFTagId>(0), TIFFTagDataType::BYTE, 8,
                /*data_offset*/ 0, &mda);

   for (int i = 0; i < 8; i++) {
      auto val1 = tag1.GetDataValueAtIdx<uint8_t>(i);
      EXPECT_TRUE(val1.ok());
      EXPECT_EQ(val1.value(), 0x11 * i);
   }

   uint8_t new_data[8];
   absl::Status status = tag1.CopyAllDataValues<uint8_t>(new_data);
   EXPECT_TRUE(status.ok());
   for (int i = 0; i < 8; i++) {
      EXPECT_EQ(new_data[i], 0x11 * i);
   }

   TIFFTag tag2(static_cast<TIFFTagId>(0), TIFFTagDataType::SHORT, 4,
                /*data_offset*/ 0, &mda);

   auto val2 = tag2.GetDataValueAtIdx<uint16_t>(0);
   EXPECT_TRUE(val2.ok());
   EXPECT_EQ(val2.value(), 0x1100);

   val2 = tag2.GetDataValueAtIdx<uint16_t>(1);
   EXPECT_TRUE(val2.ok());
   EXPECT_EQ(val2.value(), 0x3322);

   val2 = tag2.GetDataValueAtIdx<uint16_t>(2);
   EXPECT_TRUE(val2.ok());
   EXPECT_EQ(val2.value(), 0x5544);

   val2 = tag2.GetDataValueAtIdx<uint16_t>(3);
   EXPECT_TRUE(val2.ok());
   EXPECT_EQ(val2.value(), 0x7766);

   TIFFTag tag3(static_cast<TIFFTagId>(0), TIFFTagDataType::LONG, 2,
                /*data_offset*/ 0, &mda);

   auto val3 = tag3.GetDataValueAtIdx<uint32_t>(0);
   EXPECT_TRUE(val3.ok());
   EXPECT_EQ(val3.value(), 0x33221100);

   val3 = tag3.GetDataValueAtIdx<uint32_t>(1);
   EXPECT_TRUE(val3.ok());
   EXPECT_EQ(val3.value(), 0x77665544);
}

TEST(TiffTagTest, Rational) {
   uint32_t external_data[] = {0x000000F0, 0x00000F00, 0x00000100, 0x00000A00};
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(external_data), 16);

   TIFFTag tag1(static_cast<TIFFTagId>(0), TIFFTagDataType::RATIONAL, 2,
                /*data_offset*/ 0, &mda);

   auto val1 = tag1.GetDataValueAtIdx<uint32_t>(0);
   EXPECT_FALSE(val1.ok());  // Rational requires float or double
   val1 = tag1.GetDataValueAtIdx<uint32_t>(1);
   EXPECT_FALSE(val1.ok());  // Rational requires float or double

   auto val2 = tag1.GetDataValueAtIdx<float>(0);
   EXPECT_TRUE(val2.ok());
   EXPECT_EQ(val2.value(), 0.0625f);

   val2 = tag1.GetDataValueAtIdx<float>(1);
   EXPECT_TRUE(val2.ok());
   EXPECT_EQ(val2.value(), 0.1f);

   auto val3 = tag1.GetDataValueAtIdx<double>(0);
   EXPECT_TRUE(val3.ok());
   EXPECT_EQ(val3.value(), 0.0625f);

   val3 = tag1.GetDataValueAtIdx<double>(1);
   EXPECT_TRUE(val3.ok());
   EXPECT_EQ(val3.value(), 0.1);

   float all_values[2];
   auto status = tag1.CopyAllDataValues<float>(all_values);
   EXPECT_TRUE(status.ok());
   EXPECT_EQ(all_values[0], 0.0625f);
   EXPECT_EQ(all_values[1], 0.1f);
}

TEST(TiffTagTest, DebugString) {
   TIFFTag tag(static_cast<TIFFTagId>(42), TIFFTagDataType::LONG, 1, 12345);
   EXPECT_EQ(
       tag.DebugString(),
       "[TIFFTag|id=0x002A|data_type=long|data_count=1|data_offset=12345]");
}

TEST(TiffTagTest, PrettyPrintSingleValue) {
   TIFFTag tag(static_cast<TIFFTagId>(42), TIFFTagDataType::LONG, 1, 12345);
   EXPECT_EQ(tag.PrettyPrint(), "[002A]:12345");
}

TEST(TiffTagTest, PrettyPrintMultipleValues) {
   TIFFTag tag(static_cast<TIFFTagId>(42), TIFFTagDataType::BYTE, 4,
               0x40302010);
   EXPECT_EQ(tag.PrettyPrint(), "[002A]:(16,32,48,64)");
}

TEST(TiffTagTest, PrettyPrintStringInTag) {
   std::string test_str = "abc";
   TIFFTag tag(static_cast<TIFFTagId>(42), TIFFTagDataType::STRING, 4,
               *reinterpret_cast<const uint32_t*>(test_str.c_str()));
   EXPECT_EQ(tag.PrettyPrint(), "[002A]:abc");
}

TEST(TiffTagTest, PrettyPrintExternalString) {
   std::string test_str = "Hello world!";
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_str[0]), 13);
   TIFFTag tag(static_cast<TIFFTagId>(42), TIFFTagDataType::STRING, 13,
               /*data_offset*/ 0, &mda);
   EXPECT_EQ(tag.PrettyPrint(), "[002A]:Hello world!");
}

TEST(TiffTagTest, PrettyPrintTooManyValues) {
   uint8_t values[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16};
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&values), 16);
   TIFFTag tag(static_cast<TIFFTagId>(42), TIFFTagDataType::BYTE, 16,
               /*data_offset*/ 0, &mda);
   EXPECT_EQ(tag.PrettyPrint(),
             "[002A]:(1,2,3,4,5,6,7,8,9,10,...{6 more values})");
}

TEST(TiffTagTest, PrettyPrintOverrideMaxValuesToPrint) {
   uint8_t values[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&values), 10);
   TIFFTag tag(static_cast<TIFFTagId>(42), TIFFTagDataType::BYTE, 10,
               /*data_offset*/ 0, &mda);
   TIFFTag::MaxDataValuesToPrint = 6;
   EXPECT_EQ(tag.PrettyPrint(), "[002A]:(1,2,3,4,5,6,...{4 more values})");
   TIFFTag::MaxDataValuesToPrint = TIFF_TAG_MAX_DATA_VAL_TO_PRINT_DEFAULT;
   EXPECT_EQ(tag.PrettyPrint(), "[002A]:(1,2,3,4,5,6,7,8,9,10)");
}

std::string
TestTagIdToStringFn(TIFFTagId id) {
   if (id == static_cast<TIFFTagId>(42))
      return "FortyTwo";
   else
      return absl::StrFormat("%d", id);
}

TEST(TiffTagTest, PrettyPrintOverrideTagIdToStringFn) {
   TIFFTag tag(static_cast<TIFFTagId>(42), TIFFTagDataType::LONG, 1, 12345);
   TIFFTag::TagIdToStringFn = TestTagIdToStringFn;
   EXPECT_EQ(tag.PrettyPrint(), "FortyTwo:12345");
   TIFFTag::TagIdToStringFn = TIFFTag::TagIdToStringDefault;
   EXPECT_EQ(tag.PrettyPrint(), "[002A]:12345");
}

TEST(TiffTagTest, PrettyPrintSignedValues) {
   int8_t values[] = {-1, -2, -3, -4, -5};
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&values), 5);
   TIFFTag tag(static_cast<TIFFTagId>(42), TIFFTagDataType::SINT, 5,
               /*data_offset*/ 0, &mda);
   EXPECT_EQ(tag.PrettyPrint(), "[002A]:(-1,-2,-3,-4,-5)");
}
