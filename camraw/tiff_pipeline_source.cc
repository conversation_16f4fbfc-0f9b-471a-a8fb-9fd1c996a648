#include <string>
#include <unordered_set>

#include "glog/logging.h"

#include "camraw/colorspace.h"
#include "camraw/endian.h"
#include "camraw/tiff_file_parser.h"
#include "camraw/tiff_pipeline_source.h"
#include "camraw/tiff_tag_id.h"

namespace cmrw {

absl::Status
TIFFPipelineSource::Init() {
   ASSIGN_TAG_VAL_OR_RETURN(uint16_t, compression_, TIFFTagId::COMPRESSION);

   if (supported_compression_types.find(compression_) ==
       supported_compression_types.end()) {
      LOG(ERROR) << "Unsupported compression type: " << compression_;
      return absl::FailedPreconditionError("Unsupported compression type.");
   }

   ASSIGN_TAG_VAL_OR_RETURN(uint16_t, photo_int_, TIFFTagId::PHOTOMETRIC_INT);
   ASSIGN_TAG_VAL_OR_RETURN(uint16_t, samples_per_pixel_,
                            TIFFTagId::SAMPLES_PER_PIXEL);

   const TIFFTag* bits_per_sample_tag;
   ASSIGN_TAG_OR_RETURN(bits_per_sample_tag, TIFFTagId::BITS_PER_SAMPLE,
                        TIFF_IFD_TYPE_PRIMARY_IMAGE);
   if (bits_per_sample_tag->GetDataCount() == 1) {
      ASSIGN_OR_RETURN(bits_per_sample_,
                       bits_per_sample_tag->GetDataValue<uint16_t>(),
                       VLOG(2) << "Failed to get bits per sample value");
   } else {
      CHECK_EQ(bits_per_sample_tag->GetDataCount(), 3U);
      auto bps_or = bits_per_sample_tag->GetAllDataValues<uint16_t>();
      if (!bps_or.ok()) {
         VLOG(2) << "Failed to get bits per sample values";
         return bps_or.status();
      }
      CHECK_EQ((*bps_or)[0], (*bps_or)[1]);
      CHECK_EQ((*bps_or)[0], (*bps_or)[2]);
      bits_per_sample_ = (*bps_or)[0];
   }

   ASSIGN_TAG_VAL_OR_RETURN(uint32_t, image_width_, TIFFTagId::IMAGE_WIDTH);
   ASSIGN_TAG_VAL_OR_RETURN(uint32_t, image_height_, TIFFTagId::IMAGE_LENGTH);

   initialized_ = true;
   return absl::OkStatus();
}

absl::StatusOr<TIFFPipelineSource::ChunkConfiguration>
TIFFPipelineSource::GetChunkConfiguration() {
   if (compression_ == TIFF_COMPRESSION_UNCOMPRESSED) {
      return ChunkConfiguration{
          .allowed_data_layouts =
              {ChunkConfiguration::DataLayout::SINGLE_STRIP},
          .data_type = "uncompressed TIFF",
          .expected_strip_size =
              image_width_ * image_height_ * sizeof(uint16_t),
          // different endianness needs processing
          .needs_processing =
              NOT_SAME_ENDIANNESS_AS_ARCH(file_parser_->GetEndianness()),
          .sub_chunk_byte_alignment = sizeof(uint16_t),
      };
   }

   return absl::UnimplementedError("Unsupported compression scheme");
}

absl::Status
TIFFPipelineSource::FetchAndSetMetadata(ImageMetaId id, ImageMeta& image_meta) {
   switch (id) {
      case ImageMetaId::IMAGE_WIDTH: {
         image_meta.SetOne<uint16_t>(ImageMetaId::IMAGE_WIDTH,
                                     static_cast<uint16_t>(image_width_));
         VLOG(3) << "Fetch metadata IMAGE_WIDTH = " << image_width_;
      } break;
      case ImageMetaId::IMAGE_HEIGHT: {
         image_meta.SetOne<uint16_t>(ImageMetaId::IMAGE_HEIGHT,
                                     static_cast<uint16_t>(image_height_));
         VLOG(3) << "Fetch metadata IMAGE_HEIGHT = " << image_height_;
      } break;
      case ImageMetaId::ORIENTATION: {
         const TIFFIFD* first_ifd;
         ASSIGN_OR_RETURN(first_ifd, file_parser_->GetIFDAtIndex(0),
                          VLOG(2) << "Failed to retrieve first IFD");

         const TIFFTag* orientation_tag;
         ASSIGN_OR_RETURN(orientation_tag,
                          first_ifd->GetTag(TIFFTagId::ORIENTATION),
                          VLOG(2) << "Failed to get orientation tag");

         uint16_t orientation;
         ASSIGN_OR_RETURN(orientation,
                          orientation_tag->GetDataValue<uint16_t>(),
                          VLOG(2) << "Failed to get orientation value");

         image_meta.SetOne<uint16_t>(ImageMetaId::ORIENTATION, orientation);
         VLOG(3) << "Fetch metadata ORIENTATION = " << orientation;
      } break;
      case ImageMetaId::BITS_PER_SAMPLE: {
         image_meta.SetOne<uint16_t>(ImageMetaId::BITS_PER_SAMPLE,
                                     bits_per_sample_);
         VLOG(3) << "Fetch metadata BITS_PER_SAMPLE = " << bits_per_sample_;
      } break;
      case ImageMetaId::SAMPLES_PER_PIXEL: {
         image_meta.SetOne<uint16_t>(ImageMetaId::SAMPLES_PER_PIXEL,
                                     samples_per_pixel_);
         VLOG(3) << "Fetch metadata SAMPLES_PER_PIXEL = " << samples_per_pixel_;
      } break;
      case ImageMetaId::CFA_PATTERN_DIM: {
         const TIFFTag* cfa_pattern_dim_tag;
         ASSIGN_TAG_OR_RETURN(cfa_pattern_dim_tag,
                              TIFFTagId::CFA_REPEAT_PATTERN_DIM,
                              TIFF_IFD_TYPE_PRIMARY_IMAGE);

         CHECK_EQ(cfa_pattern_dim_tag->GetDataCount(), 2U);
         CHECK_EQ(static_cast<int>(cfa_pattern_dim_tag->GetDataType()),
                  static_cast<int>(TIFFTagDataType::SHORT));
         std::vector<uint16_t> cfa_dim;
         auto cfadim_or = cfa_pattern_dim_tag->GetAllDataValues<uint16_t>();
         if (cfadim_or.ok()) {
            // XXX: we should have a GetAllDataValues that returns a vector
            cfa_dim.insert(cfa_dim.end(), cfadim_or->get(),
                           cfadim_or->get() + 2);
         }
         image_meta.SetMulti(ImageMetaId::CFA_PATTERN_DIM, cfa_dim);
         VLOG(3) << "Fetch metadata CFA_PATTERN_DIM = (" << cfa_dim[0] << ","
                 << cfa_dim[1] << ")";
      } break;
      case ImageMetaId::CFA_PATTERN: {
         const TIFFTag* cfa_pattern_tag;
         ASSIGN_TAG_OR_RETURN(cfa_pattern_tag, TIFFTagId::CFA_PATTERN,
                              TIFF_IFD_TYPE_PRIMARY_IMAGE);

         CHECK_EQ(cfa_pattern_tag->GetDataCount(), 4U);
         CHECK_EQ(static_cast<int>(cfa_pattern_tag->GetDataType()),
                  static_cast<int>(TIFFTagDataType::BYTE));
         std::vector<uint16_t> cfa_pattern;
         auto cfa_pattern_or = cfa_pattern_tag->GetAllDataValues<uint16_t>();
         if (cfa_pattern_or.ok()) {
            // XXX: we should have a GetAllDataValues that returns a vector
            cfa_pattern.insert(cfa_pattern.end(), cfa_pattern_or->get(),
                               cfa_pattern_or->get() + 4);
         }
         image_meta.SetMulti(ImageMetaId::CFA_PATTERN, cfa_pattern);
         VLOG(3) << "Fetch metadata CFA_PATTERN = (" << cfa_pattern[0] << ","
                 << cfa_pattern[1] << "," << cfa_pattern[2] << ","
                 << cfa_pattern[3] << ")";
      } break;
      case ImageMetaId::CROP_SIZE: {
         const TIFFTag* default_crop_size_tag;
         ASSIGN_TAG_OR_RETURN(default_crop_size_tag,
                              TIFFTagId::DEFAULT_CROP_SIZE,
                              TIFF_IFD_TYPE_PRIMARY_IMAGE);

         CHECK_EQ(default_crop_size_tag->GetDataCount(), 2U);

         std::unique_ptr<uint32_t[]> crop_size;
         ASSIGN_OR_RETURN(crop_size,
                          default_crop_size_tag->GetAllDataValues<uint32_t>(),
                          VLOG(2) << "Failed to get default crop size values");

         std::vector<uint32_t> default_crop_size = {crop_size[0], crop_size[1]};
         image_meta.SetMulti(ImageMetaId::CROP_SIZE, default_crop_size);
         VLOG(3) << "Fetch metadata CROP_SIZE = (" << default_crop_size[0]
                 << "," << default_crop_size[1] << ")";
      } break;
      case ImageMetaId::CROP_ORIGIN: {
         const TIFFTag* default_crop_origin_tag;
         ASSIGN_TAG_OR_RETURN(default_crop_origin_tag,
                              TIFFTagId::DEFAULT_CROP_ORIGIN,
                              TIFF_IFD_TYPE_PRIMARY_IMAGE);

         CHECK_EQ(default_crop_origin_tag->GetDataCount(), 2U);

         std::unique_ptr<uint32_t[]> crop_origin;
         ASSIGN_OR_RETURN(
             crop_origin, default_crop_origin_tag->GetAllDataValues<uint32_t>(),
             VLOG(2) << "Failed to get default crop origin values");

         std::vector<uint32_t> default_crop_origin = {crop_origin[0],
                                                      crop_origin[1]};
         image_meta.SetMulti(ImageMetaId::CROP_ORIGIN, default_crop_origin);
         VLOG(3) << "Fetch metadata CROP_ORIGIN = (" << default_crop_origin[0]
                 << "," << default_crop_origin[1] << ")";
      } break;
      case ImageMetaId::COMPRESSION: {
         CHECK_EQ(compression_, TIFF_COMPRESSION_UNCOMPRESSED);
         image_meta.SetOne<ImageMetaCompression>(
             ImageMetaId::COMPRESSION, ImageMetaCompression::UNCOMPRESSED);
         VLOG(3) << "Set metadata COMPRESSION = UNCOMPRESSED";
      } break;
      case ImageMetaId::PHOTOMETRIC_INTERPRETATION: {
         switch (photo_int_) {
            case TIFF_PHOTOMETRIC_INT_GRAY_BLACK_ZERO:
               image_meta.SetOne<ImageMetaPhotoInt>(
                   ImageMetaId::PHOTOMETRIC_INTERPRETATION,
                   ImageMetaPhotoInt::GRAYSCALE);
               VLOG(3) << "Fetch metadata PHOTOMETRIC_INT = Grayscale";
               break;
            case TIFF_PHOTOMETRIC_INT_RGB:
               image_meta.SetOne<ImageMetaPhotoInt>(
                   ImageMetaId::PHOTOMETRIC_INTERPRETATION,
                   ImageMetaPhotoInt::RGB);
               VLOG(3) << "Fetch metadata PHOTOMETRIC_INT = RGB";
               break;
            case TIFF_PHOTOMETRIC_INT_CFA:
               image_meta.SetOne<ImageMetaPhotoInt>(
                   ImageMetaId::PHOTOMETRIC_INTERPRETATION,
                   ImageMetaPhotoInt::CFA);
               VLOG(3) << "Fetch metadata PHOTOMETRIC_INT = CFA";
               break;
            case TIFF_PHOTOMETRIC_INT_YCBCR:
               image_meta.SetOne<ImageMetaPhotoInt>(
                   ImageMetaId::PHOTOMETRIC_INTERPRETATION,
                   ImageMetaPhotoInt::YCBCR);
               VLOG(3) << "Fetch metadata PHOTOMETRIC_INT = YCbCr";
               break;
            case TIFF_PHOTOMETRIC_INT_LINEAR_RAW:
               image_meta.SetOne<ImageMetaPhotoInt>(
                   ImageMetaId::PHOTOMETRIC_INTERPRETATION,
                   ImageMetaPhotoInt::LINEAR_RAW);
               VLOG(3) << "Fetch metadata PHOTOMETRIC_INT = Linear RAW";
               break;
            default:
               VLOG(2) << "Unsupported photometric interpretation: "
                       << photo_int_;
               return absl::UnimplementedError(
                   "Unsupported photometric interpretation");
               break;
         }
      } break;
      case ImageMetaId::COLOR_SPACE: {
         uint16_t photo_int;
         ASSIGN_TAG_VAL_OR_RETURN(uint16_t, photo_int,
                                  TIFFTagId::PHOTOMETRIC_INT);
         switch (photo_int) {
            case TIFF_PHOTOMETRIC_INT_CFA:
            case TIFF_PHOTOMETRIC_INT_YCBCR:
            case TIFF_PHOTOMETRIC_INT_LINEAR_RAW:
               image_meta.SetOne<Colorspace>(ImageMetaId::COLOR_SPACE,
                                             Colorspace::CAMERA);
               VLOG(3) << "Fetch metadata COLOR_SPACE = CAMERA";
               break;
            default:
               image_meta.SetOne(ImageMetaId::COLOR_SPACE, Colorspace::SRGB);
               VLOG(3) << "Fetch metadata COLOR_SPACE = sRGB";
               break;
         }
      } break;
      case ImageMetaId::CAMERA_MAKE: {
         const TIFFIFD* first_ifd;
         ASSIGN_OR_RETURN(first_ifd, file_parser_->GetIFDAtIndex(0),
                          VLOG(2) << "Failed to retrieve first IFD");
         const TIFFTag* make_tag;
         ASSIGN_OR_RETURN(make_tag, first_ifd->GetTag(TIFFTagId::MAKE),
                          VLOG(2) << "Failed to get camera make tag");
         const std::string make = make_tag->DataToString();
         image_meta.SetOne(ImageMetaId::CAMERA_MAKE, make);
         VLOG(3) << "Fetch metadata CAMERA_MAKE = " << make;
      } break;
      case ImageMetaId::CAMERA_MODEL: {
         const TIFFIFD* first_ifd;
         ASSIGN_OR_RETURN(first_ifd, file_parser_->GetIFDAtIndex(0),
                          VLOG(2) << "Failed to retrieve first IFD");
         const TIFFTag* model_tag;
         ASSIGN_OR_RETURN(model_tag, first_ifd->GetTag(TIFFTagId::MODEL),
                          VLOG(2) << "Failed to get camera model tag");
         const std::string model = model_tag->DataToString();
         image_meta.SetOne(ImageMetaId::CAMERA_MODEL, model);
         VLOG(3) << "Fetch metadata CAMERA_MODEL = " << model;
      } break;
      default: {
         VLOG(2) << "Asked to fetch unsupported metadata with id: "
                 << static_cast<int>(id);
         return absl::UnimplementedError("Asked to fetch unsupported metadata");
      }
   }
   return absl::OkStatus();
}

absl::StatusOr<std::unique_ptr<ImageMeta>>
TIFFPipelineSource::GetMetadata(std::unordered_set<ImageMetaId> required,
                                std::unordered_set<ImageMetaId> optional) {
   auto image_meta = std::make_unique<ImageMeta>();

   for (auto id : required) {
      RETURN_IF_ERROR(FetchAndSetMetadata(id, *image_meta),
                      VLOG(2) << "Failed to fetch and set metadata id: "
                              << static_cast<int>(id));
   }

   for (auto id : optional) {
      absl::Status status = FetchAndSetMetadata(id, *image_meta);
      if (!status.ok()) {
         if (status.code() == absl::StatusCode::kNotFound) {
            VLOG(3) << "Optional metadata not found id: "
                    << static_cast<int>(id);
         } else {
            VLOG(2) << "Failed to fetch and set metadata id: "
                    << static_cast<int>(id);
            return status;
         }
      }
   }

   return image_meta;
}

absl::StatusOr<std::vector<std::unique_ptr<PipelineSourceChunk>>>
TIFFPipelineSource::GetChunks() {
   if (!initialized_) {
      return absl::FailedPreconditionError("Pipeline source not initialized.");
   }

   std::vector<std::unique_ptr<PipelineSourceChunk>> chunks;

   ChunkConfiguration chunk_config;
   ASSIGN_OR_RETURN(chunk_config, GetChunkConfiguration(),
                    VLOG(2) << "Failed to get chunk configuration");
   LOG(INFO) << "Decoding " << chunk_config.data_type << " data";

   bool has_strip_offsets = false;
   bool has_tile_offsets = false;

   const TIFFIFD* primary_ifd;
   ASSIGN_OR_RETURN(primary_ifd,
                    file_parser_->GetIFD(TIFF_IFD_TYPE_PRIMARY_IMAGE),
                    VLOG(2) << "Failed to get primary image IFD");

   const TIFFTag* strip_offsets_tag;
   auto strip_offsets_tag_or = primary_ifd->GetTag(TIFFTagId::STRIP_OFFSETS);
   if (!strip_offsets_tag_or.ok()) {
      if (strip_offsets_tag_or.status().code() != absl::StatusCode::kNotFound) {
         VLOG(2) << "Failed to get strip offsets tag";
         return strip_offsets_tag_or.status();
      }
   } else {
      has_strip_offsets = true;
      strip_offsets_tag = *strip_offsets_tag_or;
   }

   const TIFFTag* tile_offsets_tag = nullptr;
   auto tile_offsets_tag_or = primary_ifd->GetTag(TIFFTagId::TILE_OFFSETS);
   if (!tile_offsets_tag_or.ok()) {
      if (tile_offsets_tag_or.status().code() != absl::StatusCode::kNotFound) {
         VLOG(2) << "Failed to get tile offsets tag";
         return tile_offsets_tag_or.status();
      }
   } else {
      has_tile_offsets = true;
      tile_offsets_tag = *tile_offsets_tag_or;
   }

   if (chunk_config.allowed_data_layouts.find(
           ChunkConfiguration::DataLayout::SINGLE_STRIP) !=
           chunk_config.allowed_data_layouts.end() &&
       has_strip_offsets) {
      if (strip_offsets_tag->GetDataCount() > 1) {
         VLOG(2) << "Multiple strips not implemented";
         return absl::UnimplementedError("Multiple strips not implemented.");
      }
      uint32_t strip_offset;
      ASSIGN_OR_RETURN(strip_offset,
                       strip_offsets_tag->GetDataValue<uint32_t>(),
                       VLOG(2) << "Failed to get strip offset value.");

      uint32_t strip_byte_count;
      ASSIGN_TAG_VAL_OR_RETURN(uint32_t, strip_byte_count,
                               TIFFTagId::STRIP_BYTE_COUNTS);

      if (chunk_config.expected_strip_size > 0) {
         if (strip_byte_count != chunk_config.expected_strip_size) {
            VLOG(2) << "Expected " << chunk_config.expected_strip_size
                    << " bytes but got " << strip_byte_count
                    << " bytes in strip";
            return absl::FailedPreconditionError("Strip byte count mismatch");
         }
      }

      VLOG(2) << "Single strip num_bytes: " << strip_byte_count;

      auto chunk = std::make_unique<PipelineSourceChunk>();
      auto raw_data = std::make_unique<uint8_t[]>(strip_byte_count);
      RETURN_IF_ERROR(data_accessor_->CopyBytes(strip_offset, strip_byte_count,
                                                raw_data.get()),
                      VLOG(2) << "Failed to copy strip data from file.");

      chunk->owned_chunk_data.reset(raw_data.get());
      chunk->chunk_data_ptr = raw_data.get();
      raw_data.release();

      chunk->chunk_data_bytes = strip_byte_count;
      chunk->needs_processing = chunk_config.needs_processing;
      chunk->sub_chunk_byte_alignment =
          (chunk_config.sub_chunk_byte_alignment == 0)
              ? strip_byte_count
              : chunk_config.sub_chunk_byte_alignment;
      chunk->processed_height = image_height_;
      chunk->processed_width = image_width_;
      chunk->x_offset = 0;
      chunk->y_offset = 0;

      chunks.push_back(std::move(chunk));

      return chunks;
   } else if (chunk_config.allowed_data_layouts.find(
                  ChunkConfiguration::DataLayout::TILES) !=
                  chunk_config.allowed_data_layouts.end() &&
              has_tile_offsets) {
      uint32_t tile_width, tile_length;
      ASSIGN_TAG_VAL_OR_RETURN(uint32_t, tile_width, TIFFTagId::TILE_WIDTH);
      ASSIGN_TAG_VAL_OR_RETURN(uint32_t, tile_length, TIFFTagId::TILE_LENGTH);

      const TIFFTag* tile_bytes_tag;
      ASSIGN_TAG_OR_RETURN(tile_bytes_tag, TIFFTagId::TILE_BYTE_COUNTS,
                           TIFF_IFD_TYPE_PRIMARY_IMAGE);

      size_t num_tiles = tile_offsets_tag->GetDataCount();
      if (num_tiles != tile_bytes_tag->GetDataCount()) {
         VLOG(2) << "Unequal tile offset count: " << num_tiles
                 << " and tile bytes count: " << tile_bytes_tag->GetDataCount();
         return absl::FailedPreconditionError(
             "Tile offset count and byte count differs.");
      }
      CHECK_GT(num_tiles, 0U);

      VLOG(2) << "Tiled layout tile width: " << tile_width
              << " tile length: " << tile_length << " num tiles: " << num_tiles;
      chunks.reserve(num_tiles);

      // Index of the current tile to get offsets/bytes
      uint16_t tile_idx = 0;
      // tile_row and tile_col represent the top left position of the
      // current tile in the decoded image
      for (uint16_t tile_row = 0; tile_row < image_height_;
           tile_row += tile_length) {
         for (uint16_t tile_col = 0; tile_col < image_width_;
              tile_col += tile_width) {
            // XXX: Make ASSIGN_TAG_VAL_AT_IDX_OR_RETURN macro
            uint32_t tile_offset;
            ASSIGN_OR_RETURN(
                tile_offset,
                tile_offsets_tag->GetDataValueAtIdx<uint32_t>(tile_idx),
                VLOG(2) << "Failed to get tile offset");

            uint32_t tile_bytes;
            ASSIGN_OR_RETURN(
                tile_bytes,
                tile_bytes_tag->GetDataValueAtIdx<uint32_t>(tile_idx),
                VLOG(2) << "Failed to get tile bytes");

            auto chunk = std::make_unique<PipelineSourceChunk>();
            auto tile_data = std::make_unique<uint8_t[]>(tile_bytes);
            RETURN_IF_ERROR(data_accessor_->CopyBytes(tile_offset, tile_bytes,
                                                      tile_data.get()),
                            VLOG(2) << "Failed to copy tile data from file.");

            chunk->owned_chunk_data.reset(tile_data.get());
            chunk->chunk_data_ptr = tile_data.get();
            tile_data.release();

            chunk->chunk_data_bytes = tile_bytes;
            chunk->needs_processing = chunk_config.needs_processing;
            chunk->sub_chunk_byte_alignment =
                (chunk_config.sub_chunk_byte_alignment == 0)
                    ? tile_bytes
                    : chunk_config.sub_chunk_byte_alignment;
            /* Some DNG tiles overflow the image height/width - to account for
             * such tiles we reduce the processed height/width to not cause
             * overflows.
             */
            if (tile_row + tile_length > image_height_) {
               chunk->processed_height = image_height_ - tile_row;
            } else {
               chunk->processed_height = tile_length;
            }

            if (tile_col + tile_width > image_width_) {
               chunk->processed_width = image_width_ - tile_col;
            } else {
               chunk->processed_width = tile_width;
            }
            chunk->y_offset = tile_row;
            chunk->x_offset = tile_col;

            chunks.push_back(std::move(chunk));

            tile_idx++;
         }
      }

      return chunks;
   }

   return absl::UnimplementedError("Unimplemented data layout");
}

// TIFFPipelineSource::ProcessChunk is just for changing endianness if needed
absl::Status
TIFFPipelineSource::ProcessChunk(const PipelineSourceChunk& chunk,
                                 ImageBuf<uint16_t>& output) {
   Endianness file_endianness = file_parser_->GetEndianness();
   CHECK(chunk.needs_processing);
   CHECK_EQ(compression_, TIFF_COMPRESSION_UNCOMPRESSED);
   CHECK(NOT_SAME_ENDIANNESS_AS_ARCH(file_endianness));

   if (!initialized_) {
      return absl::FailedPreconditionError("Pipeline source not initialized");
   }

   // Shallow imagebuf to help with the math below
   ImageBuf<uint16_t> input(reinterpret_cast<uint16_t*>(chunk.chunk_data_ptr),
                            /*take ownership*/ false, chunk.processed_height,
                            chunk.processed_width, samples_per_pixel_);

   for (uint32_t row = 0; row < chunk.processed_height; row++) {
      for (uint32_t col = 0; col < chunk.processed_width; col++) {
         for (uint32_t comp = 0; comp < samples_per_pixel_; comp++) {
            if (file_endianness == ENDIANNESS_LITTLE) {
               output(row, col, comp) =
                   little_endian<uint16_t>(input(row, col, comp));
            } else {
               output(row, col, comp) =
                   big_endian<uint16_t>(input(row, col, comp));
            }
         }
      }
   }

   return absl::OkStatus();
}

}  // namespace cmrw
