#include <algorithm>
#include <cassert>
#include <cctype>
#include <filesystem>
#include <memory>

#include "glog/logging.h"

#include "camraw/file_type_classifier.h"
#include "camraw/status_macros.h"

namespace cmrw {

absl::Status
FileTypeClassifier::ClassifyFile(DataAccessor& data_accessor,
                                 std::string file_path) {
   if (classify_called) {
      return absl::FailedPreconditionError("ClassifyFile() already called");
   }
   classify_called = true;

   VLOG(1) << "Classifying file at path: " << file_path;

   RETURN_IF_ERROR(BuildTree());

   RETURN_IF_ERROR(ApplyExtensionHintsToTree(file_path));
   /*
    * We employ a breadth-first search to find the first child of the root
    * node which validates against the given file. We repeat the same process
    * for children of the validated node until a leaf node validates the file.
    */
   FileTypeClassifierTreeNode* cur_root = tree_.get();
   const FileTypeRegistryElement* prev_classified_element = nullptr;
   assert(classified_type_ == nullptr);
   do {
      bool a_child_validated = false;

      for (auto& cur_node : cur_root->children) {
         auto element = cur_node->element;
         std::unique_ptr<FileParser> file_parser =
             element->create_parser_fn(data_accessor);

         bool validated = false;
         ASSIGN_OR_RETURN(validated, file_parser->ValidateFile());

         if (validated) {
            VLOG(4) << "Node validated file as type: " << element->type_name;
            a_child_validated = true;
            saved_parser_ = std::move(file_parser);
            if (cur_node->children.size() == 0) {
               // Validated node is a leaf node, end of search
               classified_type_ = element;
               break;
            } else {
               // Validated node has children, move search down a level
               VLOG(4) << "Validated node has children, searching down.";
               prev_classified_element = element;
               cur_root = cur_node.get();
               break;
            }
         }
      }
      // If we ran out of all children of the current root without any of
      // them validating we terminate the search. If there was a parent node
      // that validated then we return it, otherwise we conclude that the
      // file didn't classify at all.
      if (!a_child_validated) {
         VLOG(4) << "No children nodes validated.";
         if (prev_classified_element != nullptr) {
            classified_type_ = prev_classified_element;
            break;
         } else {
            return absl::NotFoundError(
                "No known file types matched the given file.");
         }
      }
   } while (classified_type_ == nullptr);

   VLOG(1) << "File classified as: " << classified_type_->type_name;
   data_accessor_ = &data_accessor;
   return absl::OkStatus();
}

absl::StatusOr<std::string>
FileTypeClassifier::GetFileTypeString() {
   if (!classify_called || classified_type_ == nullptr) {
      return absl::FailedPreconditionError(
          "ClassifyFile() not called or failed.");
   }

   return classified_type_->type_name;
}

absl::StatusOr<std::unique_ptr<FileParser>>
FileTypeClassifier::GetFileParser() {
   if (!classify_called || classified_type_ == nullptr) {
      return absl::FailedPreconditionError(
          "ClassifyFile() not called or failed.");
   }

   if (saved_parser_ != nullptr) {
      return std::move(saved_parser_);
   }

   return classified_type_->create_parser_fn(*data_accessor_);
}

absl::StatusOr<std::unique_ptr<PipelineSource>>
FileTypeClassifier::GetPipelineSource(FileParser* file_parser) {
   if (!classify_called || classified_type_ == nullptr) {
      return absl::FailedPreconditionError(
          "ClassifyFile() not called or failed.");
   }

   if (file_parser == nullptr) {
      return absl::FailedPreconditionError("Invalid file parser argument.");
   }

   return classified_type_->create_pipeline_src_fn(file_parser);
}

absl::Status
FileTypeClassifier::BuildTree() {
   if (tree_ != nullptr) {
      return absl::FailedPreconditionError("Tree already exists");
   }

   // Allocate root node
   tree_ = std::make_unique<FileTypeClassifierTreeNode>();

   const FileTypeRegistry& registry = FileTypeRegistry::GetRegistry();
   auto ordered_type_names = registry.GetOrderedTypeNames();
   for (std::string type_name : ordered_type_names) {
      const FileTypeRegistryElement* element;
      ASSIGN_OR_RETURN(element, registry.GetElement(type_name));

      auto node_map_it = node_map_.find(type_name);
      if (node_map_it != node_map_.end()) {
         return absl::FailedPreconditionError("Node already exists in tree.");
      }

      auto tree_node = std::make_unique<FileTypeClassifierTreeNode>();
      FileTypeClassifierTreeNode* tree_node_ptr = tree_node.get();
      tree_node->element = element;

      // Insert into the root node OR parent node's children list
      if (element->parent_type_name == "") {
         VLOG(4) << "Adding type: " << element->type_name << " to root node.";
         tree_->children.push_back(std::move(tree_node));
      } else {
         auto parent_node_it = node_map_.find(element->parent_type_name);
         if (parent_node_it == node_map_.end()) {
            return absl::NotFoundError("Parent type not found.");
         }

         auto parent_node = parent_node_it->second;
         VLOG(4) << "Adding type: " << element->type_name
                 << " to parent node: " << parent_node->element->type_name;
         parent_node->children.push_back(std::move(tree_node));
      }

      // Insert into the node lookup table
      node_map_.insert({type_name, tree_node_ptr});
   }

   return absl::OkStatus();
}

namespace {
bool
MoveWholeBranchToFirstSpot(FileTypeClassifierTreeNode* node,
                           std::string extension) {
   if (node == nullptr) {
      return false;
   }

   auto element = node->element;
   if (element && std::find(element->extension_hints.begin(),
                            element->extension_hints.end(),
                            extension) != element->extension_hints.end()) {
      // Found the element - signal up through the recursion to fix the ordering
      return true;
   }

   // Recurse through children
   int child_idx = 0;
   for (auto& child : node->children) {
      bool found = MoveWholeBranchToFirstSpot(child.get(), extension);
      if (found) {
         // Move child to the first spot
         std::rotate(node->children.begin(), node->children.begin() + child_idx,
                     node->children.end());
         return true;
      }
      child_idx++;
   }

   return false;
}
}  // namespace

absl::Status
FileTypeClassifier::ApplyExtensionHintsToTree(std::string file_path) {
   if (file_path == "") {
      return absl::OkStatus();
   }

   std::filesystem::path path(file_path);

   std::string extension = path.extension().string();
   std::transform(extension.begin(), extension.end(), extension.begin(),
                  [](unsigned char c) { return std::toupper(c); });

   VLOG(1) << "Applying extension hints for file extension: " << extension;

   bool found = MoveWholeBranchToFirstSpot(tree_.get(), extension);
   VLOG(4) << (found ? "Hints applied." : "Hints not applied");

   return absl::OkStatus();
}

}  // namespace cmrw
