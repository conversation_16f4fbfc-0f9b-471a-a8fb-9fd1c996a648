#include <unordered_set>

#include "absl/container/flat_hash_map.h"
#include "absl/status/status.h"
#include "absl/status/statusor.h"

#include "camraw/endian.h"
#include "camraw/status_macros.h"
#include "camraw/tiff_ifd.h"
#include "camraw/tiff_tag.h"

namespace cmrw {

// Read from offset into 'type varName' while applying endianness conversion
#define IFD_DATA_ACCESSOR_GET_OR_RETURN(offset, type, varName)                \
   do {                                                                       \
      type tmp;                                                               \
      RETURN_IF_ERROR(data_accessor_->CopyBytes(offset, sizeof(type), &tmp)); \
      varName = (endianness_ == ENDIANNESS_LITTLE) ? little_endian<type>(tmp) \
                                                   : big_endian<type>(tmp);   \
   } while (0)

absl::Status
TIFFIFD::Parse() {
   if (is_parsed_) return absl::OkStatus();

   uint32_t data_offset = ifd_offset_;
   IFD_DATA_ACCESSOR_GET_OR_RETURN(data_offset, uint16_t, num_tags_);
   data_offset += sizeof(num_tags_);

   tags_.reserve(num_tags_);

   for (size_t tag_idx = 0; tag_idx < num_tags_; tag_idx++) {
      TIFFTagWireFormat tag_data;
      RETURN_IF_ERROR(data_accessor_->CopyBytes(
          data_offset, sizeof(TIFFTagWireFormat), &tag_data));

      tags_.emplace_back(&tag_data, data_accessor_, endianness_);
      tag_id_to_idx_map_.insert({tags_[tag_idx].GetId(), tag_idx});

      data_offset += sizeof(TIFFTagWireFormat);
   }

   IFD_DATA_ACCESSOR_GET_OR_RETURN(data_offset, uint32_t, next_ifd_offset_);

   is_parsed_ = true;
   return absl::OkStatus();
}

absl::StatusOr<std::list<const TIFFTag*>>
TIFFIFD::Search(const std::unordered_set<TIFFTagId>& search_mask) const {
   if (!is_parsed_) {
      return absl::FailedPreconditionError("IFD is not parsed.");
   }

   std::list<const TIFFTag*> result_list;

   for (auto search_tag : search_mask) {
      auto tag_or = GetTag(search_tag);
      if (tag_or.ok()) {
         result_list.push_back(tag_or.value());
      }
   }

   return result_list;
}

absl::StatusOr<TIFFIFDScanResult>
TIFFIFD::Scan(const std::unordered_set<TIFFTagId>* search_mask) {
   TIFFIFDScanResult result;

   if (is_parsed_) {
      if (search_mask != nullptr) {
         std::list<const TIFFTag*> search_result;
         ASSIGN_OR_RETURN(search_result, Search(*search_mask));

         // Copy results
         for (auto tag_ptr : search_result) {
            result.tags.push_back(TIFFTag(*tag_ptr));
         }
      }

      result.num_ifd_tags = num_tags_;
      result.next_ifd_offset = next_ifd_offset_;
      return result;
   }

   // IFD is not parsed
   uint32_t data_offset = ifd_offset_;
   IFD_DATA_ACCESSOR_GET_OR_RETURN(data_offset, uint16_t, num_tags_);
   data_offset += sizeof(num_tags_);

   if (search_mask != nullptr) {
      for (size_t tag_idx = 0; tag_idx < num_tags_; tag_idx++) {
         uint16_t tag_id;
         IFD_DATA_ACCESSOR_GET_OR_RETURN(data_offset, uint16_t, tag_id);

         if (search_mask->find(static_cast<TIFFTagId>(tag_id)) !=
             search_mask->end()) {
            TIFFTagWireFormat tag_data;
            RETURN_IF_ERROR(data_accessor_->CopyBytes(
                data_offset, sizeof(tag_data), &tag_data));
            result.tags.push_back(
                TIFFTag(&tag_data, data_accessor_, endianness_));
         }

         data_offset += sizeof(TIFFTagWireFormat);
      }
   } else {
      data_offset += num_tags_ * sizeof(TIFFTagWireFormat);
   }

   IFD_DATA_ACCESSOR_GET_OR_RETURN(data_offset, uint32_t, next_ifd_offset_);

   result.num_ifd_tags = num_tags_;
   result.next_ifd_offset = next_ifd_offset_;
   return result;
}

absl::StatusOr<const TIFFTag*>
TIFFIFD::GetTag(TIFFTagId tag_id) const {
   auto it = tag_id_to_idx_map_.find(tag_id);
   if (it == tag_id_to_idx_map_.end()) {
      return absl::NotFoundError("Tag not found in IFD.");
   }

   size_t tag_idx = it->second;
   return &tags_[tag_idx];
}

}  // namespace cmrw
