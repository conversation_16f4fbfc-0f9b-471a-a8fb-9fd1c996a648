#include <cmath>

#include "glog/logging.h"

#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/status_macros.h"
#include "camraw/tone_curve_processor.h"

namespace cmrw {

absl::Status
ToneCurveProcessor::Init(const ImageMeta& image_meta) {
   uint16_t bits_per_sample;
   ASSIGN_OR_RETURN(bits_per_sample,
                    image_meta.GetOne<uint16_t>(ImageMetaId::BITS_PER_SAMPLE));
   CHECK_EQ(bits_per_sample, 16);  // Only support 16 bit inputs

   max_uint16_val_ = static_cast<float>(pow(2, 16) - 1);
   wl_multiplier_ = max_uint16_val_ / static_cast<float>(wl_target_);

   if (gamma_target_ != 1.0f) {
      gamma_divisor_ = 1.0f / gamma_target_;
   }

   return absl::OkStatus();
}

void
ToneCurveProcessor::LogStart() {
   LOG(INFO) << "Scaling data to white level: " << wl_target_
             << " and gamma: " << gamma_target_;
}

absl::Status
ToneCurveProcessor::Run(const ImageBuf<uint16_t>& input_buffer,
                        ImageBuf<uint16_t>& output_buffer) {
   CHECK_EQ(input_buffer.get_height(), output_buffer.get_height());
   CHECK_EQ(input_buffer.get_width(), output_buffer.get_width());

   uint32_t clipped_samples = 0;

   for (uint32_t row = 0; row < input_buffer.get_height(); row++) {
      for (uint32_t col = 0; col < input_buffer.get_width(); col++) {
         for (uint32_t comp = 0; comp < input_buffer.get_components(); comp++) {
            uint16_t orig_val = input_buffer(row, col, comp);

            float normalized = static_cast<float>(orig_val) / 65535.0f;

            float wl_adjusted = normalized * wl_multiplier_;
            if (wl_adjusted > 1.0f) {
               wl_adjusted = 1.0f;
               clipped_samples++;
            }

            float gamma_adjusted = wl_adjusted;
            if (gamma_target_ != 1.0f) {
               gamma_adjusted = std::pow(wl_adjusted, gamma_divisor_);
               if (gamma_adjusted > 1.0f) {
                  gamma_adjusted = 1.0f;
                  clipped_samples++;
               }
            }

            output_buffer(row, col, comp) =
                static_cast<uint16_t>(gamma_adjusted * 65535.0f);
         }
      }
   }

   CommitStats([&] { clipped_samples_ += clipped_samples; });

   return absl::OkStatus();
}

void
ToneCurveProcessor::LogStats() {
   LOG(INFO) << clipped_samples_
             << " samples were clipped during tone curve adjustment.";
}

}  // namespace cmrw
