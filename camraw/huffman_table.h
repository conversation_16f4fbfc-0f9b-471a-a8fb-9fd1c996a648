#ifndef _HUFFMAN_TABLE_H_
#define _HUFFMAN_TABLE_H_

#include <vector>

#include "absl/status/status.h"
#include "absl/status/statusor.h"

#include "camraw/data_accessor.h"
#include "camraw/limits.h"

namespace cmrw {

enum class HuffmanTableClass : uint8_t { DC = 0, AC = 1 };

class HuffmanTable {
   public:
      HuffmanTable(DataAccessor* data_accessor, uint32_t accessor_offset,
                   uint32_t bytes_available)
          : data_accessor_(data_accessor),
            accessor_offset_(accessor_offset),
            accessor_bytes_available_(bytes_available) {
         for (int i = 0; i < 16; i++) {
            max_code_val_of_len_[i] = MAX_UINT16;
         }
      };
      absl::Status Init();
      absl::StatusOr<uint8_t> Decode(uint8_t code_len, uint16_t code_val) const;
      absl::Status SetTableClass(HuffmanTableClass table_class);
      HuffmanTableClass GetTableClass() const { return table_class_; }
      absl::Status SetTableId(uint8_t table_id);
      uint8_t GetTableId() const { return table_id_; }
      uint32_t GetAccessorBytesUsed() const { return accessor_bytes_used_; }
      uint8_t GetShortestCodeLen() const { return shortest_code_len_; }
      uint8_t GetLongestCodeLen() const { return longest_code_len_; }

   private:
      absl::Status ConstructDecodeTable();
      absl::Status InsertToDecodeTable(uint8_t code_len, uint16_t code_val,
                                       uint8_t symbol_val);
      absl::Status ParseTable();

      DataAccessor* data_accessor_;
      uint32_t accessor_offset_;
      uint32_t accessor_bytes_available_;
      uint32_t accessor_bytes_used_ = 0;
      bool initialized_ = false;
      HuffmanTableClass table_class_;
      uint8_t table_id_;

      // ith element of this represents the number of codes that have the
      // length i+1. This is read directly from the file. This is the HUFFBITS
      // array in the spec.
      uint8_t num_codes_of_len_[16];
      // total number of Huffman codes
      uint32_t num_total_codes_ = 0;
      // ordered symbol values for each code, directly read from file (HUFFVALS)
      std::vector<uint8_t> symbol_values_;

      // Maximum code value at given code length - used to optimize the size
      // of the decode table.
      uint16_t max_code_val_of_len_[16];
      // Length of the shortest/longest codes, used by callers to skip checking
      // code lengths that don't exist in the table.
      uint8_t shortest_code_len_ = 17;
      uint8_t longest_code_len_ = 0;

      /*
       * The decode table is an array of 16 vectors, whethere the vector at
       * index i represents all codes with code length i+1. Each vector is sized
       * to contain all possible 2^(i+1) code values representable by i+1 bits.
       *
       * The table is a sparse matrix, where only cells corresponding to codes
       * that exist are populated, and all other cells are marked as
       * populated = false.
       */
      struct DecodeTableEntry {
            bool populated = false;
            uint8_t symbol_value = 0;
      };
      std::vector<DecodeTableEntry> decode_table_[16];
};

}  // namespace cmrw

#endif
