#ifndef PIPELINE_TASK_H_
#define PIPELINE_TASK_H_

#include <functional>

#include "absl/status/status.h"

#include "camraw/imagebuf.h"
#include "camraw/pipeline_source.h"

namespace cmrw {

class PipelineTask {
   public:
      PipelineTask(uint32_t y_offset, uint32_t x_offset,
                   std::function<absl::Status()> task_func,
                   std::unique_ptr<ImageBuf<uint16_t>> input_buf,
                   std::unique_ptr<ImageBuf<uint16_t>> output_buf,
                   std::unique_ptr<PipelineSourceChunk> source_chunk = nullptr,
                   std::shared_ptr<ImageBuf<uint16_t>> shared_buf = nullptr,
                   std::shared_ptr<uint8_t[]> shared_data = nullptr)
          : y_offset_(y_offset),
            x_offset_(x_offset),
            task_func_(task_func),
            input_buf_(std::move(input_buf)),
            output_buf_(std::move(output_buf)),
            source_chunk_(std::move(source_chunk)),
            shared_buf_(shared_buf),
            shared_data_(shared_data) {}

      uint32_t y_offset() { return y_offset_; }
      uint32_t x_offset() { return x_offset_; }

      absl::Status Run() { return task_func_(); }

      ImageBuf<uint16_t>* input_buf() { return input_buf_.get(); }
      ImageBuf<uint16_t>* ReleaseInputBuf() { return input_buf_.release(); }
      ImageBuf<uint16_t>* output_buf() { return output_buf_.get(); }
      ImageBuf<uint16_t>* ReleaseOutputBuf() { return output_buf_.release(); }
      PipelineSourceChunk* source_chunk() { return source_chunk_.get(); }
      PipelineSourceChunk* ReleaseSourceChunk() {
         return source_chunk_.release();
      }

   private:
      uint32_t y_offset_;
      uint32_t x_offset_;
      std::function<absl::Status()> task_func_;
      std::unique_ptr<ImageBuf<uint16_t>> input_buf_;
      std::unique_ptr<ImageBuf<uint16_t>> output_buf_;
      std::unique_ptr<PipelineSourceChunk> source_chunk_;
      std::shared_ptr<ImageBuf<uint16_t>> shared_buf_;
      std::shared_ptr<uint8_t[]> shared_data_;
};

}  // namespace cmrw

#endif /* PIPELINE_TASK_H_ */
