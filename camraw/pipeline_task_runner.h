#ifndef PIPELINE_TASK_RUNNER_H_
#define PIPELINE_TASK_RUNNER_H_

#include <condition_variable>
#include <map>
#include <memory>
#include <mutex>
#include <queue>
#include <set>
#include <thread>
#include <vector>

#include "absl/status/status.h"

#include "camraw/pipeline_task.h"

namespace cmrw {

class PipelineTaskRunner {
   public:
      PipelineTaskRunner(unsigned int num_threads);
      ~PipelineTaskRunner();

      // Enqueue a task for a specific stage
      void EnqueueRunnableTask(std::unique_ptr<PipelineTask> task,
                               int stage_id = 0);

      // Dequeue a completed task from a specific stage
      std::pair<absl::Status, std::unique_ptr<PipelineTask>>
      DequeueCompletedTask(int stage_id = 0);

      // Mark a stage as complete (no more tasks will be enqueued for this
      // stage)
      void MarkStageAsComplete(int stage_id);

      // Check if a stage has any pending tasks (runnable, running, or completed
      // but not dequeued)
      bool StageHasPendingTasks(int stage_id);

   private:
      unsigned int num_threads_;
      std::vector<std::thread> threads_;

      // Bool to make threads stop picking up new tasks and exit
      bool stop_running_;

      // Track the highest stage_id seen so far to enforce monotonicity
      int highest_stage_id_;

      // Set of stages that have been marked as complete
      std::set<int> completed_stages_;

      // Track the number of tasks per stage that are currently running
      std::map<int, int> running_tasks_count_;

      // runnable task queues (one per stage)
      std::map<int, std::queue<std::unique_ptr<PipelineTask>>> runnable_tasks_;
      std::mutex runnable_tasks_lock_;
      std::condition_variable runnable_tasks_cond_;

      // completed task queues (one per stage)
      std::map<int, std::queue<
                        std::pair<absl::Status, std::unique_ptr<PipelineTask>>>>
          completed_tasks_;
      std::mutex completed_tasks_lock_;
      std::condition_variable completed_tasks_cond_;

      // Get the earliest stage that has runnable tasks
      int GetEarliestRunnableStage();

      // Thread function
      void PipelineTaskRunnerWorker();
};

}  // namespace cmrw

#endif /* PIPELINE_TASK_RUNNER_H_ */
