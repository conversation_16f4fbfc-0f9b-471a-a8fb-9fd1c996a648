#include <gtest/gtest.h>

#include "camraw/bit_pump.h"
#include "camraw/memory_data_accessor.h"

using cmrw::BitPump;
using cmrw::ENDIANNESS_BIG;
using cmrw::ENDIANNESS_LITTLE;
using cmrw::MemoryDataAccessor;

class BitPumpTest : public ::testing::Test {
   protected:
      void SetUp() override {
         // Test data: 0xA5 (10100101), 0x3C (00111100), 0xF0 (11110000)
         test_data_ = {0xA5, 0x3C, 0xF0};
         data_accessor_ = std::make_unique<MemoryDataAccessor>(
             test_data_.data(), test_data_.size());
      }

      std::vector<uint8_t> test_data_;
      std::unique_ptr<MemoryDataAccessor> data_accessor_;
};

TEST_F(BitPumpTest, GetBitsBigEndian) {
   BitPump pump(data_accessor_.get(), 0, ENDIANNESS_BIG);

   // First byte: 0xA5 (10100101)
   // Get 3 bits (101) from first byte
   auto bits_or = pump.GetBits(3);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0x5);  // 101 binary

   // Get 5 bits (00101) from remaining first byte
   bits_or = pump.GetBits(5);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0x5);  // 00101 binary

   // Second byte: 0x3C (00111100)
   // Get 8 bits (00111100) from second byte
   bits_or = pump.GetBits(8);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0x3C);  // 00111100 binary
}

TEST_F(BitPumpTest, GetBitsLittleEndian) {
   BitPump pump(data_accessor_.get(), 0, ENDIANNESS_LITTLE);

   // First byte: 0xA5 (10100101)

   // Get 3 bits (101) from first byte
   auto bits_or = pump.GetBits(3);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0x5);  // 101 binary

   // Get 5 bits from remaining first byte
   bits_or = pump.GetBits(5);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0x14);  // 10100 binary = 20 decimal

   // Second byte: 0x3C (00111100)
   // Get 8 bits from second byte
   bits_or = pump.GetBits(8);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0x3C);  // 00111100 binary = 60 decimal
}

TEST_F(BitPumpTest, GetBitsAcrossBoundaries) {
   BitPump pump(data_accessor_.get(), 0, ENDIANNESS_BIG);

   // First byte: 0xA5 (10100101)
   // Get 4 bits (1010) from first byte
   auto bits_or = pump.GetBits(4);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0xA);  // 1010 binary

   // Get 8 bits (0101 0011) spanning first and second byte
   bits_or = pump.GetBits(8);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0x53);  // 01010011 binary
}

TEST_F(BitPumpTest, GetBitsAcrossBoundariesLittleEndian) {
   BitPump pump(data_accessor_.get(), 0, ENDIANNESS_LITTLE);

   // First byte: 0xA5 (10100101)
   // Get 4 bits (0101) from first byte
   auto bits_or = pump.GetBits(4);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0x5);  // 0101 binary

   // Get 8 bits spanning first and second byte
   // Remaining 4 bits from first byte: 1010
   // First 4 bits from second byte: 1100
   bits_or = pump.GetBits(8);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0xCA);  // 11001010 binary = 202 decimal
}

TEST_F(BitPumpTest, GetBitsMaximum) {
   BitPump pump(data_accessor_.get(), 0, ENDIANNESS_BIG);

   // Get 16 bits (maximum allowed)
   auto bits_or = pump.GetBits(16);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0xA53C);  // 10100101 00111100 binary
}

TEST_F(BitPumpTest, GetBytesRead) {
   BitPump pump(data_accessor_.get(), 0);

   // Initially no bytes read
   EXPECT_EQ(pump.GetNumBytesRead(), 0);

   // Read 8 bits (1 byte)
   auto bits_or = pump.GetBits(8);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(pump.GetNumBytesRead(), 1);

   // Read 8 more bits (another byte)
   bits_or = pump.GetBits(8);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(pump.GetNumBytesRead(), 2);
}

TEST_F(BitPumpTest, DataOffset) {
   // Start at offset 1 (skip first byte)
   BitPump pump(data_accessor_.get(), 1);

   auto bits_or = pump.GetBits(8);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0x3C);  // Second byte
}
