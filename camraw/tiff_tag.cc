#include <string>
#include <string_view>

#include "absl/strings/str_cat.h"
#include "absl/strings/str_format.h"

#include "camraw/endian.h"
#include "camraw/tiff_tag.h"

namespace cmrw {

std::function<const std::string(TIFFTagId)> TIFFTag::TagIdToStringFn =
    TIFFTag::TagIdToStringDefault;
size_t TIFFTag::MaxDataValuesToPrint = TIFF_TAG_MAX_DATA_VAL_TO_PRINT_DEFAULT;

const std::string
TIFFTag::TagIdToStringDefault(TIFFTagId tag_id) {
   return absl::StrFormat("[%04X]", tag_id);
}

size_t TIFFTagDataTypeSizeInBytes[] = {
    /*(unused)*/ 0,
    /*BYTE*/ 1,
    /*STRING*/ 1,
    /*SHORT*/ 2,
    /*LONG*/ 4,
    /*RATIONAL*/ 8,  // 2 32-bit unsigned integers
    /*SINT*/ 1,
    /*UNDEF*/ 1,
    /*SSHORT*/ 2,
    /*SLONG*/ 4,
    /*SRATIONAL*/ 8,  // 2 32-bit signed integers
    /*FLOAT*/ 4,
    /*DOUBLE*/ 8,
};

// clang-format off
std::string TIFFTagDataTypeStringNames[] = {
   "0", // unused
   "byte",
   "string",
   "short",
   "long",
   "rational",
   "signed int",
   "undefined",
   "signed short",
   "signed long",
   "signed rational",
   "float",
   "double",
};
// clang-format on

size_t
TIFFTagDataTypeByteLength(TIFFTagDataType type) {
   return TIFFTagDataTypeSizeInBytes[static_cast<size_t>(type)];
}

std::string_view
TIFFTagDataTypeToString(TIFFTagDataType type) {
   if (type >= TIFFTagDataType::MAX) {
      return "INVALID";
   }

   return TIFFTagDataTypeStringNames[static_cast<size_t>(type)];
}

absl::StatusOr<uint32_t>
TIFFTag::GetDataOffsetAsValue() const {
   if (!IsTagDataInOffsetField()) {
      return absl::FailedPreconditionError(
          "Tag value not contained in offset field.");
   }

   if (endianness_ == ENDIANNESS_LITTLE) {
      return little_endian<uint32_t>(data_offset_);
   } else {
      return big_endian<uint32_t>(data_offset_);
   }
}

const std::string
TIFFTag::DebugString() const {
   return absl::StrFormat(
       "[TIFFTag|id=0x%04X|data_type=%s|data_count=%d|data_offset=%d]", id_,
       TIFFTagDataTypeToString(data_type_), data_count_, data_offset_);
}

// Doesn't support TIFFTagDataType::STRING, use DataToString() instead.
const std::string
TIFFTag::DataValueAtIdxToString(uint16_t idx) const {
   switch (data_type_) {
      case TIFFTagDataType::BYTE:
      case TIFFTagDataType::UNDEF: {
         auto val_or = GetDataValueAtIdx<uint8_t>(idx);
         if (val_or.ok()) {
            return absl::StrFormat("%hhu", *val_or);
         }
      } break;
      case TIFFTagDataType::SHORT: {
         auto val_or = GetDataValueAtIdx<uint16_t>(idx);
         if (val_or.ok()) {
            return absl::StrFormat("%hu", *val_or);
         }
      }
      case TIFFTagDataType::LONG: {
         auto val_or = GetDataValueAtIdx<uint32_t>(idx);
         if (val_or.ok()) {
            return absl::StrFormat("%u", *val_or);
         }
      } break;
      case TIFFTagDataType::RATIONAL:
      case TIFFTagDataType::SRATIONAL: {
         auto val_or = GetDataValueAtIdx<float>(idx);
         if (val_or.ok()) {
            return absl::StrFormat("%f", *val_or);
         }
      } break;
      case TIFFTagDataType::SINT: {
         auto val_or = GetDataValueAtIdx<int8_t>(idx);
         if (val_or.ok()) {
            return absl::StrFormat("%hhd", *val_or);
         }
      } break;
      case TIFFTagDataType::SSHORT: {
         auto val_or = GetDataValueAtIdx<int16_t>(idx);
         if (val_or.ok()) {
            return absl::StrFormat("%hd", *val_or);
         }
      } break;
      case TIFFTagDataType::SLONG: {
         auto val_or = GetDataValueAtIdx<int32_t>(idx);
         if (val_or.ok()) {
            return absl::StrFormat("%d", *val_or);
         }
      } break;
      case TIFFTagDataType::FLOAT: {
         auto val_or = GetDataValueAtIdx<float>(idx);
         if (val_or.ok()) {
            return absl::StrFormat("%f", *val_or);
         }
      } break;
      case TIFFTagDataType::DOUBLE: {
         auto val_or = GetDataValueAtIdx<double>(idx);
         if (val_or.ok()) {
            return absl::StrFormat("%f", *val_or);
         }
      } break;
      default:
         return "?";
   }

   return "?";
}

const std::string
TIFFTag::DataToString() const {
   std::string val_str;

   if (data_type_ == TIFFTagDataType::STRING) {
      std::unique_ptr<char[]> chars = std::make_unique<char[]>(data_count_);
      absl::Status status = CopyAllDataValues<char>(chars.get());
      val_str = chars.get();
      if (!status.ok()) {
         val_str = "?";
      }
      return val_str;
   }

   if (data_count_ == 1) {
      val_str = DataValueAtIdxToString(0);
   } else {
      absl::StrAppend(&val_str, "(");
      uint16_t idx = 0;
      for (;
           idx < std::min<uint16_t>(data_count_, TIFFTag::MaxDataValuesToPrint);
           idx++) {
         if (idx != 0) {
            absl::StrAppend(&val_str, ",");
         }
         absl::StrAppend(&val_str, DataValueAtIdxToString(idx));
      }

      if (data_count_ > idx) {
         absl::StrAppend(&val_str, absl::StrFormat(",...{%d more values}",
                                                   data_count_ - idx));
      }

      absl::StrAppend(&val_str, ")");
   }
   return val_str;
}

const std::string
TIFFTag::PrettyPrint() const {
   return absl::StrCat(TagIdToStringFn(id_), ":", DataToString());
}

}  // namespace cmrw
