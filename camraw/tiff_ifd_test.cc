#include <unordered_set>

#include "absl/status/status.h"
#include "absl/status/statusor.h"
#include "gtest/gtest.h"

#include "camraw/memory_data_accessor.h"
#include "camraw/tiff_ifd.h"
#include "camraw/tiff_tag.h"

using cmrw::MemoryDataAccessor;
using cmrw::TIFFIFD;
using cmrw::TIFFTag;
using cmrw::TIFFTagDataType;
using cmrw::TIFFTagId;
using cmrw::TIFFTagWireFormat;

#define NUM_TEST_TAGS 3

struct __attribute__((packed)) IFDWireFormat {
      uint16_t num_tags;
      TIFFTagWireFormat tags[NUM_TEST_TAGS];
      uint32_t next_ifd_offset;
};

IFDWireFormat test_data = {
    .num_tags = NUM_TEST_TAGS,
    .tags = {{/*id*/ 45, /*type*/ static_cast<uint16_t>(TIFFTagDataType::LONG),
              /*count*/ 1,
              /*data*/ 9999},
             {/*id*/ 67, /*type*/ static_cast<uint16_t>(TIFFTagDataType::LONG),
              /*count*/ 1,
              /*data*/ 2222},
             {/*id*/ 78, /*type*/ static_cast<uint16_t>(TIFFTagDataType::LONG),
              /*count*/ 1,
              /*data*/ 5454}},
    .next_ifd_offset = 125,
};

TEST(TiffIfdTest, ParseIfd) {
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   TIFFIFD ifd(/*ifd_offset*/ 0, &mda);

   auto status = ifd.Parse();
   EXPECT_TRUE(status.ok());

   EXPECT_TRUE(ifd.IsParsed());
   EXPECT_EQ(ifd.GetIFDOffset(), 0);
   EXPECT_EQ(ifd.GetNextIFDOffset(), 125);
   EXPECT_EQ(ifd.GetNumTags(), 3);
   EXPECT_EQ(ifd.GetDataAccessor(), &mda);
}

TEST(TiffIfdTest, GetTag) {
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   TIFFIFD ifd(/*ifd_offset*/ 0, &mda);

   auto status = ifd.Parse();
   EXPECT_TRUE(status.ok());

   auto result = ifd.GetTag(static_cast<TIFFTagId>(67));
   EXPECT_TRUE(result.ok());

   const TIFFTag* tag = *result;
   EXPECT_EQ(tag->GetId(), static_cast<TIFFTagId>(67));
   EXPECT_EQ(tag->GetDataType(), TIFFTagDataType::LONG);
   EXPECT_EQ(tag->GetDataCount(), 1);

   auto data_result = tag->GetDataValue<uint32_t>();
   EXPECT_TRUE(data_result.ok());
   EXPECT_EQ(*data_result, 2222);

   result = ifd.GetTag(static_cast<TIFFTagId>(45));
   EXPECT_TRUE(result.ok());

   tag = *result;
   EXPECT_EQ(tag->GetId(), static_cast<TIFFTagId>(45));
   EXPECT_EQ(tag->GetDataType(), TIFFTagDataType::LONG);
   EXPECT_EQ(tag->GetDataCount(), 1);

   data_result = (*result)->GetDataValue<uint32_t>();
   EXPECT_TRUE(data_result.ok());
   EXPECT_EQ(*data_result, 9999);

   result = ifd.GetTag(static_cast<TIFFTagId>(78));
   EXPECT_TRUE(result.ok());

   tag = *result;
   EXPECT_EQ(tag->GetId(), static_cast<TIFFTagId>(78));
   EXPECT_EQ(tag->GetDataType(), TIFFTagDataType::LONG);
   EXPECT_EQ(tag->GetDataCount(), 1);

   data_result = (*result)->GetDataValue<uint32_t>();
   EXPECT_TRUE(data_result.ok());
   EXPECT_EQ(*data_result, 5454);
}

TEST(TiffIfdTest, GetTagNotFound) {
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   TIFFIFD ifd(/*ifd_offset*/ 0, &mda);

   auto status = ifd.Parse();
   EXPECT_TRUE(status.ok());

   auto result = ifd.GetTag(static_cast<TIFFTagId>(99));
   EXPECT_TRUE(absl::IsNotFound(result.status()));
}

TEST(TiffIfdTest, ScanNotParsed) {
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   TIFFIFD ifd(/*ifd_offset*/ 0, &mda);

   std::unordered_set<TIFFTagId> search_mask{static_cast<TIFFTagId>(67),
                                             static_cast<TIFFTagId>(78)};
   auto scan_result_or = ifd.Scan(&search_mask);
   EXPECT_TRUE(scan_result_or.ok());
   EXPECT_EQ(scan_result_or->num_ifd_tags, 3);
   EXPECT_EQ(scan_result_or->next_ifd_offset, 125);

   EXPECT_EQ(scan_result_or->tags.size(), 2);
   for (auto tag : scan_result_or->tags) {
      EXPECT_TRUE(tag.GetId() == static_cast<TIFFTagId>(67) ||
                  tag.GetId() == static_cast<TIFFTagId>(78));
   }
}

TEST(TiffIfdTest, ScanParsed) {
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   TIFFIFD ifd(/*ifd_offset*/ 0, &mda);

   auto status = ifd.Parse();
   EXPECT_TRUE(status.ok());

   std::unordered_set<TIFFTagId> search_mask{static_cast<TIFFTagId>(67),
                                             static_cast<TIFFTagId>(78)};
   auto scan_result_or = ifd.Scan(&search_mask);
   EXPECT_TRUE(scan_result_or.ok());
   EXPECT_EQ(scan_result_or->num_ifd_tags, 3);
   EXPECT_EQ(scan_result_or->next_ifd_offset, 125);

   EXPECT_EQ(scan_result_or->tags.size(), 2);
   for (auto tag : scan_result_or->tags) {
      EXPECT_TRUE(tag.GetId() == static_cast<TIFFTagId>(67) ||
                  tag.GetId() == static_cast<TIFFTagId>(78));
   }
}

TEST(TiffIfdTest, ScanNotFoundNotParsed) {
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   TIFFIFD ifd(/*ifd_offset*/ 0, &mda);

   std::unordered_set<TIFFTagId> search_mask{static_cast<TIFFTagId>(96)};
   auto scan_result_or = ifd.Scan(&search_mask);
   EXPECT_TRUE(scan_result_or.ok());
   EXPECT_EQ(scan_result_or->num_ifd_tags, 3);
   EXPECT_EQ(scan_result_or->next_ifd_offset, 125);

   EXPECT_EQ(scan_result_or->tags.size(), 0);
}

TEST(TiffIfdTest, ScanNotFoundParsed) {
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   TIFFIFD ifd(/*ifd_offset*/ 0, &mda);

   auto status = ifd.Parse();
   EXPECT_TRUE(status.ok());

   std::unordered_set<TIFFTagId> search_mask{static_cast<TIFFTagId>(96)};
   auto scan_result_or = ifd.Scan(&search_mask);
   EXPECT_TRUE(scan_result_or.ok());
   EXPECT_EQ(scan_result_or->num_ifd_tags, 3);
   EXPECT_EQ(scan_result_or->next_ifd_offset, 125);

   EXPECT_EQ(scan_result_or->tags.size(), 0);
}

TEST(TiffIfdTest, ScanNoTagsToSearchNotParsed) {
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   TIFFIFD ifd(/*ifd_offset*/ 0, &mda);

   auto scan_result_or = ifd.Scan(nullptr);
   EXPECT_TRUE(scan_result_or.ok());
   EXPECT_EQ(scan_result_or->num_ifd_tags, 3);
   EXPECT_EQ(scan_result_or->next_ifd_offset, 125);
}

TEST(TiffIfdTest, ScanNoTagsToSearchParsed) {
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   TIFFIFD ifd(/*ifd_offset*/ 0, &mda);

   auto status = ifd.Parse();
   EXPECT_TRUE(status.ok());

   auto scan_result_or = ifd.Scan(nullptr);
   EXPECT_TRUE(scan_result_or.ok());
   EXPECT_EQ(scan_result_or->num_ifd_tags, 3);
   EXPECT_EQ(scan_result_or->next_ifd_offset, 125);
}

TEST(TiffIfdTest, SearchParsed) {
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   TIFFIFD ifd(/*ifd_offset*/ 0, &mda);

   auto status = ifd.Parse();
   EXPECT_TRUE(status.ok());

   const std::unordered_set<TIFFTagId> search_mask{static_cast<TIFFTagId>(67),
                                                   static_cast<TIFFTagId>(78)};
   auto result_list_or = ifd.Search(search_mask);
   EXPECT_TRUE(result_list_or.ok());

   EXPECT_EQ(result_list_or->size(), 2);
   for (auto tag : *result_list_or) {
      EXPECT_TRUE(tag->GetId() == static_cast<TIFFTagId>(67) ||
                  tag->GetId() == static_cast<TIFFTagId>(78));
   }
}

TEST(TiffIfdTest, SearchNotParsed) {
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   TIFFIFD ifd(/*ifd_offset*/ 0, &mda);

   const std::unordered_set<TIFFTagId> search_mask{static_cast<TIFFTagId>(67),
                                                   static_cast<TIFFTagId>(78)};
   auto result_list_or = ifd.Search(search_mask);
   EXPECT_FALSE(result_list_or.ok());
   EXPECT_TRUE(absl::IsFailedPrecondition(result_list_or.status()));
}

TEST(TiffIfdTest, SearchEmptyMask) {
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   TIFFIFD ifd(/*ifd_offset*/ 0, &mda);

   auto status = ifd.Parse();
   EXPECT_TRUE(status.ok());

   const std::unordered_set<TIFFTagId> search_mask{};
   auto result_list_or = ifd.Search(search_mask);
   EXPECT_TRUE(result_list_or.ok());

   EXPECT_EQ(result_list_or->size(), 0);
}

TEST(TiffIfdTest, SearchNotFound) {
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   TIFFIFD ifd(/*ifd_offset*/ 0, &mda);

   auto status = ifd.Parse();
   EXPECT_TRUE(status.ok());

   const std::unordered_set<TIFFTagId> search_mask{static_cast<TIFFTagId>(96)};
   auto result_list_or = ifd.Search(search_mask);
   EXPECT_TRUE(result_list_or.ok());

   EXPECT_EQ(result_list_or->size(), 0);
}

TEST(TiffIfdTest, TagIteration) {
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   TIFFIFD ifd(/*ifd_offset*/ 0, &mda);

   auto status = ifd.Parse();
   EXPECT_TRUE(status.ok());

   for (const TIFFTag& tag : ifd) {
      EXPECT_TRUE(tag.GetId() == static_cast<TIFFTagId>(45) ||
                  tag.GetId() == static_cast<TIFFTagId>(67) ||
                  tag.GetId() == static_cast<TIFFTagId>(78));
   }
}
