#ifndef _JPEG_BIT_PUMP_H_
#define _JPEG_BIT_PUMP_H_

#include <cstdint>

#include "absl/status/status.h"
#include "absl/status/statusor.h"

#include "camraw/bit_pump.h"
#include "camraw/data_accessor.h"

namespace cmrw {

class JPEGBitPump : public BitPump {
   public:
      JPEGBitPump(DataAccessor* data_accessor, uint32_t data_offset)
          : BitPump(data_accessor, data_offset) {}
      absl::StatusOr<uint16_t> SkipMarker();

   private:
      absl::Status CopyNextByte() override;
      absl::Status CheckAndHandleByteStuffing();
};

}  // namespace cmrw

#endif
