#include <algorithm>
#include <unordered_set>
#include <utility>
#include <vector>

#include "absl/status/status.h"
#include "absl/status/statusor.h"
#include "glog/logging.h"

#include "camraw/data_accessor.h"
#include "camraw/endian.h"
#include "camraw/status_macros.h"
#include "camraw/tiff.h"
#include "camraw/tiff_file_parser.h"
#include "camraw/tiff_ifd.h"
#include "camraw/tiff_tag.h"
#include "camraw/tiff_tag_id.h"

namespace cmrw {

#define VALIDATE_RETURN(status, val) \
   if (status.ok()) {                \
      validated_ = val;              \
      return val;                    \
   } else {                          \
      validated_ = false;            \
      return status;                 \
   }

absl::StatusOr<bool>
TIFFFileParser::ValidateFile() {
   TIFFHeader header;
   VLOG(2) << "Validating TIFF file.";
   auto status = data_accessor_->CopyBytes(0, sizeof(TIFFHeader), &header);
   if (!status.ok()) {
      VALIDATE_RETURN(status, false);
   }

   if (header.order == TIFF_HEADER_ORDER_BIG_ENDIAN) {
      endianness_ = ENDIANNESS_BIG;
   } else if (header.order == TIFF_HEADER_ORDER_LITTLE_ENDIAN) {
      endianness_ = ENDIANNESS_LITTLE;
   } else {
      VLOG(2) << "Unexpected byte order identifier in TIFF header "
              << header.order;
      VALIDATE_RETURN(absl::OkStatus(), false);
   }

   uint16_t header_version;
   if (endianness_ == ENDIANNESS_LITTLE) {
      header_version = little_endian<uint16_t>(header.version);
   } else {
      header_version = big_endian<uint16_t>(header.version);
   }
   if (header_version != TIFF_HEADER_VERSION) {
      VLOG(2) << "Invalid TIFF version in header: " << header.version;
      VALIDATE_RETURN(absl::OkStatus(), false);
   }

   uint32_t ifd_offset;
   if (endianness_ == ENDIANNESS_LITTLE) {
      ifd_offset = little_endian<uint32_t>(header.ifd_offset);
   } else {
      ifd_offset = big_endian<uint32_t>(header.ifd_offset);
   }
   if (ifd_offset == 0) {
      VLOG(2) << "TIFF file contains no IFDs.";
      VALIDATE_RETURN(absl::OkStatus(), false);
   }

   TIFFIFD first_ifd(ifd_offset, data_accessor_, endianness_);

   auto scan_result_or = first_ifd.Scan(nullptr);
   if (!scan_result_or.ok()) {
      VALIDATE_RETURN(scan_result_or.status(), false);
   }

   InsertNewIFDListEntry(ifd_offset, TIFFIFDListEntryStatus::SCANNED,
                         TIFF_IFD_TYPE_UNKNOWN, first_ifd);

   if (scan_result_or->num_ifd_tags == 0) {
      VLOG(2) << "First IFD in TIFF file has no tags.";
      VALIDATE_RETURN(absl::OkStatus(), false);
   }

   VLOG(1) << "TIFF file ("
           << ((endianness_ == ENDIANNESS_LITTLE) ? "little endian"
                                                  : "big endian")
           << ") validated.";
   VALIDATE_RETURN(absl::OkStatus(), true);
}

void
TIFFFileParser::InsertNewIFDListEntry(uint32_t offset,
                                      TIFFIFDListEntryStatus status,
                                      TIFFIFDType type, TIFFIFD& ifd) {
   // Only insert if the element doesn't exist yet
   auto it = std::find_if(ifd_list_.begin(), ifd_list_.end(),
                          [&offset](const TIFFIFDListEntry& entry) {
                             return entry.ifd_offset == offset;
                          });
   if (it != ifd_list_.end()) {
      VLOG(2) << "Skipped inserting existing IFD to parser list (offset = "
              << offset << ")";
   } else {
      ifd_list_.emplace_back(
          TIFFIFDListEntry{offset, status, type, std::move(ifd)});
      VLOG(2) << "Inserting new IFD to parser list (offset = " << offset << ")";
   }
}

void
TIFFFileParser::BuildIFDListIndex() {
   VLOG(2) << "(Re)building the IFD list index.";
   ifd_list_index_.clear();
   size_t list_idx = 0;
   for (auto& list_entry : ifd_list_) {
      auto it = ifd_list_index_.find(list_entry.type);
      if (it == ifd_list_index_.end()) {
         ifd_list_index_.insert({list_entry.type, {list_idx}});
      } else {
         it->second.push_back(list_idx);
      }
      list_idx++;
   }
}

TIFFIFDType
TIFFFileParser::GetIFDTypeFromTag(const TIFFTag& tag) {
   if (tag.GetId() == TIFFTagId::EXIF_IFD) {
      return TIFF_IFD_TYPE_EXIF;
   }

   return TIFF_IFD_TYPE_UNKNOWN;
}

absl::Status
TIFFFileParser::Init() {
   if (initialized_) {
      return absl::OkStatus();
   }

   VLOG(2) << "Initializing TIFF parser.";

   if (!validated_) {
      bool validated;
      ASSIGN_OR_RETURN(validated, ValidateFile());
      if (!validated) {
         return absl::FailedPreconditionError("Not a valid TIFF file.");
      }
   }
   CHECK(validated_);

   for (size_t cur_idx = 0; cur_idx < ifd_list_.size(); cur_idx++) {
      TIFFIFD& cur_ifd = ifd_list_[cur_idx].ifd;
      // Scan for these tags:
      // * new subfile type - to learn this IFD's type
      // * all "new IFD tag ids" to discover new IFDs
      std::unordered_set<TIFFTagId> tags_to_search = {
          TIFFTagId::NEW_SUBFILE_TYPE};
      tags_to_search.insert(TIFFNewIFDTagIDs.begin(), TIFFNewIFDTagIDs.end());

      TIFFIFDScanResult scan_result;
      ASSIGN_OR_RETURN(scan_result, cur_ifd.Scan(&tags_to_search));
      ifd_list_[cur_idx].status = TIFFIFDListEntryStatus::SCANNED;

      if (scan_result.next_ifd_offset != 0) {
         TIFFIFD new_ifd(scan_result.next_ifd_offset, data_accessor_,
                         endianness_);
         InsertNewIFDListEntry(scan_result.next_ifd_offset,
                               TIFFIFDListEntryStatus::DISCOVERED,
                               TIFF_IFD_TYPE_UNKNOWN, new_ifd);
      }

      for (auto& scanned_tag : scan_result.tags) {
         if (TIFFNewIFDTagIDs.find(scanned_tag.GetId()) !=
             TIFFNewIFDTagIDs.end()) {
            // Found a new IFD, add to the list
            TIFFIFDType ifd_type = GetIFDTypeFromTag(scanned_tag);

            std::vector<uint32_t> ifd_offsets;
            if (scanned_tag.GetDataCount() == 4 &&
                (scanned_tag.GetDataType() == TIFFTagDataType::BYTE ||
                 scanned_tag.GetDataType() == TIFFTagDataType::UNDEF)) {
               /* Sony RAW DNG_PRIVATE_DATA tag is marked as data count=4
                * with type = <1 byte type> but it actually needs to be taken
                * as a full uint32_t value pointing at the IFD offset.
                */
               uint32_t offset;
               ASSIGN_OR_RETURN(offset, scanned_tag.GetDataOffsetAsValue());
               ifd_offsets.push_back(offset);
            } else {
               for (size_t i = 0; i < scanned_tag.GetDataCount(); i++) {
                  uint32_t offset;
                  ASSIGN_OR_RETURN(offset,
                                   scanned_tag.GetDataValueAtIdx<uint32_t>(i));
                  ifd_offsets.push_back(offset);
               }
            }

            for (uint32_t ifd_offset : ifd_offsets) {
               TIFFIFD new_ifd(ifd_offset, data_accessor_, endianness_);
               InsertNewIFDListEntry(ifd_offset,
                                     TIFFIFDListEntryStatus::DISCOVERED,
                                     ifd_type, new_ifd);
            }
         } else if (scanned_tag.GetId() == TIFFTagId::NEW_SUBFILE_TYPE) {
            // Figure out the current IFD's type
            uint32_t subfile_type;
            ASSIGN_OR_RETURN(subfile_type,
                             scanned_tag.GetDataValue<uint32_t>());
            if (subfile_type % 2 == 1) {
               ifd_list_[cur_idx].type = TIFF_IFD_TYPE_REDUCED_RES_IMAGE;
            } else {
               ifd_list_[cur_idx].type = TIFF_IFD_TYPE_PRIMARY_IMAGE;
            }
         }
      }
   }

   if (VLOG_IS_ON(3)) {
      size_t idx = 0;
      VLOG(3) << "IFD list:";
      for (auto& list_entry : ifd_list_) {
         // XXX: functions to make status/type readable
         VLOG(2) << "[" << idx << "]: offset: " << list_entry.ifd_offset
                 << " status: " << static_cast<int>(list_entry.status)
                 << " type: " << list_entry.type;
         idx++;
      }
   }

   BuildIFDListIndex();

   VLOG(1) << "TIFF parser initialized.";
   initialized_ = true;
   return absl::OkStatus();
}

absl::StatusOr<const TIFFIFD*>
TIFFFileParser::GetIFDAtIndex(size_t idx) {
   if (!initialized_) {
      return absl::FailedPreconditionError("Parser not initialized.");
   }

   if (idx >= ifd_list_.size()) {
      return absl::OutOfRangeError("Index out of range.");
   }

   TIFFIFD* ifd = &ifd_list_[idx].ifd;

   if (!ifd->IsParsed()) {
      CHECK_NE(static_cast<int>(ifd_list_[idx].status),
               static_cast<int>(TIFFIFDListEntryStatus::PARSED));
      auto status = ifd->Parse();
      if (!status.ok()) {
         VLOG(2) << "Error while parsing IFD";
         ifd_list_[idx].status = TIFFIFDListEntryStatus::PARSING_FAILED;
         return status;
      }
      VLOG(2) << "Parsed IFD at index: " << idx
              << " offset: " << ifd->GetIFDOffset();
      ifd_list_[idx].status = TIFFIFDListEntryStatus::PARSED;
   }

   return ifd;
}

absl::StatusOr<std::vector<const TIFFIFD*>>
TIFFFileParser::GetAllIFDsOfType(TIFFIFDType ifd_type) {
   if (!initialized_) {
      return absl::FailedPreconditionError("Parser not initialized.");
   }

   auto it = ifd_list_index_.find(ifd_type);
   if (it == ifd_list_index_.end()) {
      return absl::NotFoundError("No IFD found with the given type.");
   }

   std::vector<size_t>& ifds_of_this_type = it->second;
   std::vector<const TIFFIFD*> result;
   for (auto ifd_idx : ifds_of_this_type) {
      const TIFFIFD* ifd;
      ASSIGN_OR_RETURN(ifd, GetIFDAtIndex(ifd_idx));
      result.push_back(ifd);
   }

   return result;
}

absl::StatusOr<const TIFFIFD*>
TIFFFileParser::GetIFD(TIFFIFDType ifd_type, size_t ifd_idx) {
   if (!initialized_) {
      return absl::FailedPreconditionError("Parser not initialized.");
   }

   auto it = ifd_list_index_.find(ifd_type);
   if (it == ifd_list_index_.end()) {
      return absl::NotFoundError("No IFD found with the given type.");
   }

   std::vector<size_t>& ifds_of_this_type = it->second;
   if (ifd_idx >= ifds_of_this_type.size()) {
      return absl::OutOfRangeError("ifd_idx out of bounds for this IFD type.");
   }

   return GetIFDAtIndex(ifds_of_this_type[ifd_idx]);
}

absl::StatusOr<const TIFFTag*>
TIFFFileParser::GetTagFromIFD(TIFFTagId tagId, TIFFIFDType ifd_type,
                              size_t ifd_idx) {
   if (!initialized_) {
      return absl::FailedPreconditionError("Parser not initialized.");
   }

   const TIFFIFD* ifd;
   ASSIGN_OR_RETURN(ifd, GetIFD(ifd_type, ifd_idx));

   return ifd->GetTag(tagId);
}

}  // namespace cmrw
