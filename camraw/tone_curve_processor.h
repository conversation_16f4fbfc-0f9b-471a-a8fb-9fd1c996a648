#ifndef TONE_CURVE_PROCESSOR_H_
#define TONE_CURVE_PROCESSOR_H_

#include "absl/status/status.h"

#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/pipeline_processor.h"

namespace cmrw {

/*
 * By default, scale the input values from white level 2^16-1 to the following
 * white level. This was found experimentally on Sony ARW files, it's 152
 * (out of 255) in Photoshop levels adjustment.
 */
#define TONE_CURVE_PROC_DEFAULT_WHITE_LEVEL_TARGET 39064

class ToneCurveProcessor : public PipelineProcessor {
   public:
      ToneCurveProcessor(uint16_t wl_target = 0, float gamma_target = 1.0f)
          : PipelineProcessor(
                "ToneCurveProcessor",
                /*input_spec*/
                {.one_sample_per_pixel = true, .three_samples_per_pixel = true},
                /*output_spec*/
                {.one_sample_per_pixel = true, .three_samples_per_pixel = true},
                /*required metadata*/
                {
                    ImageMetaId::BITS_PER_SAMPLE,
                },
                /*optional metadata*/ {}),
            gamma_target_(gamma_target) {
         if (wl_target != 0) {
            wl_target_ = wl_target;
         }
      }
      absl::Status Init(const ImageMeta& image_meta) override;
      void LogStart() override;
      absl::Status Run(const ImageBuf<uint16_t>& input_buffer,
                       ImageBuf<uint16_t>& output_buffer) override;
      void LogStats() override;

   private:
      uint16_t wl_target_ = TONE_CURVE_PROC_DEFAULT_WHITE_LEVEL_TARGET;
      float gamma_target_ = 1.0f;
      float wl_multiplier_ = 1.0f;
      float gamma_divisor_ = 1.0f;
      float max_uint16_val_ = 0;
      uint32_t clipped_samples_ = 0;
};

}  // namespace cmrw

#endif /* TONE_CURVE_PROCESSOR_H_ */
