#include <fstream>

#include "glog/logging.h"

#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/status_macros.h"
#include "camraw/tiff.h"
#include "camraw/tiff_export_sink.h"
#include "camraw/tiff_tag.h"
#include "camraw/tiff_tag_id.h"

namespace cmrw {

absl::Status
TiffExportSink::OutputImage(const ImageBuf<uint16_t>& img,
                            const ImageMeta& image_meta) {
   std::ofstream out_file;
   out_file.open(file_name_, std::ios::binary | std::ios::out);

   TIFFHeader header = {
       TIFF_HEADER_ORDER_LITTLE_ENDIAN, TIFF_HEADER_VERSION,
       sizeof(TIFFHeader),  // ifd_offset
   };

   LOG(INFO) << "Outputing TIFF image as '" << file_name_ << "'";

   out_file.write(reinterpret_cast<char*>(&header), sizeof(TIFFHeader));

   uint16_t image_width;
   ASSIGN_OR_RETURN(image_width,
                    image_meta.GetOne<uint16_t>(ImageMetaId::IMAGE_WIDTH));

   uint16_t image_height;
   ASSIGN_OR_RETURN(image_height,
                    image_meta.GetOne<uint16_t>(ImageMetaId::IMAGE_HEIGHT));

   uint16_t orientation;
   ASSIGN_OR_RETURN(orientation,
                    image_meta.GetOne<uint16_t>(ImageMetaId::ORIENTATION));

   uint16_t bits_per_sample;
   ASSIGN_OR_RETURN(bits_per_sample,
                    image_meta.GetOne<uint16_t>(ImageMetaId::BITS_PER_SAMPLE));
   // XXX: round up bits_per_sample
   if (bits_per_sample < 16) {
      bits_per_sample = 16;
   }

   ImageMetaCompression compression;
   ASSIGN_OR_RETURN(compression, image_meta.GetOne<ImageMetaCompression>(
                                     ImageMetaId::COMPRESSION));
   CHECK_EQ(static_cast<uint16_t>(compression),
            static_cast<uint16_t>(ImageMetaCompression::UNCOMPRESSED));

   // Writing IFD
   uint16_t num_tags = 12;  // XXX fix when you add tags below
   out_file.write(reinterpret_cast<char*>(&num_tags), sizeof(num_tags));

   uint32_t strip_offset = sizeof(TIFFHeader) +
                           sizeof(uint16_t) /* ifd num_tags */
                           + num_tags * sizeof(TIFFTagWireFormat) +
                           sizeof(uint32_t);  // next_ifd_offset

   uint16_t samples_per_pixel;
   ASSIGN_OR_RETURN(samples_per_pixel, image_meta.GetOne<uint16_t>(
                                           ImageMetaId::SAMPLES_PER_PIXEL));
   CHECK_EQ(samples_per_pixel, img.get_components());

   uint16_t photometric_int = 0;
   ImageMetaPhotoInt photo_int_meta;
   ASSIGN_OR_RETURN(photo_int_meta,
                    image_meta.GetOne<ImageMetaPhotoInt>(
                        ImageMetaId::PHOTOMETRIC_INTERPRETATION));
   switch (photo_int_meta) {
      case ImageMetaPhotoInt::RGB:
         photometric_int = TIFF_PHOTOMETRIC_INT_RGB;
         CHECK_EQ(samples_per_pixel, 3);
         break;
      case ImageMetaPhotoInt::GRAYSCALE:
         photometric_int = TIFF_PHOTOMETRIC_INT_GRAY_BLACK_ZERO;
         CHECK_EQ(samples_per_pixel, 1);
         break;
      case ImageMetaPhotoInt::CFA:
         // XXX: we need to emit TIFF_PHOTOMETRIC_INT_CFA and write the other
         //  CFA* tags if we want to be more precise
         photometric_int = TIFF_PHOTOMETRIC_INT_GRAY_BLACK_ZERO;
         CHECK_EQ(samples_per_pixel, 1);
         break;
      default:
         CHECK(0);
         break;
   }

   uint32_t crop_origin_x, crop_origin_y, crop_width, crop_height;
   if (crop_) {
      auto crop_origin_or =
          image_meta.GetMulti<uint32_t>(ImageMetaId::CROP_ORIGIN);
      auto crop_size_or = image_meta.GetMulti<uint32_t>(ImageMetaId::CROP_SIZE);

      if (!crop_origin_or.ok() || !crop_size_or.ok()) {
         VLOG(3) << "Skipping crop due to missing metadata";
         crop_origin_x = 0;
         crop_origin_y = 0;
         crop_width = image_width;
         crop_height = image_height;
      } else {
         std::vector<uint32_t>& crop_origin = *crop_origin_or;
         std::vector<uint32_t>& crop_size = *crop_size_or;

         crop_origin_x = crop_origin[0];
         crop_origin_y = crop_origin[1];
         crop_width = crop_size[0];
         crop_height = crop_size[1];

         LOG(INFO) << "Cropping image orig size " << image_width << "x"
                   << image_height << " crop origin (" << crop_origin_x << ","
                   << crop_origin_y << ") cropped size: " << crop_width << "x"
                   << crop_height;
      }
   } else {
      crop_origin_x = 0;
      crop_origin_y = 0;
      crop_width = image_width;
      crop_height = image_height;
      LOG(INFO) << "Skipping crop";
   }

   TIFFTagWireFormat tags[] = {
       {TIFFTagId::NEW_SUBFILE_TYPE, TIFFTagDataType::LONG, 1,
        0},  // primary image
       {TIFFTagId::IMAGE_WIDTH, TIFFTagDataType::SHORT, 1, crop_width},
       {TIFFTagId::IMAGE_LENGTH, TIFFTagDataType::SHORT, 1, crop_height},
       // Export 16-bit grayscale TIFF since most apps don't support bit depths
       // that aren't multiples of 8.
       {TIFFTagId::BITS_PER_SAMPLE, TIFFTagDataType::SHORT, 1, bits_per_sample},
       {TIFFTagId::COMPRESSION, TIFFTagDataType::SHORT, 1,
        TIFF_COMPRESSION_UNCOMPRESSED},
       {TIFFTagId::PHOTOMETRIC_INT, TIFFTagDataType::SHORT, 1, photometric_int},
       {TIFFTagId::STRIP_OFFSETS, TIFFTagDataType::LONG, 1, strip_offset},
       {TIFFTagId::ORIENTATION, TIFFTagDataType::SHORT, 1, orientation},
       {TIFFTagId::SAMPLES_PER_PIXEL, TIFFTagDataType::SHORT, 1,
        samples_per_pixel},
       {TIFFTagId::ROWS_PER_STRIP, TIFFTagDataType::LONG, 1, crop_height},
       {TIFFTagId::STRIP_BYTE_COUNTS, TIFFTagDataType::LONG, 1,
        static_cast<uint32_t>(crop_width * crop_height * sizeof(uint16_t) *
                              samples_per_pixel)},
       {TIFFTagId::PLANAR_CONFIG, TIFFTagDataType::SHORT, 1,
        1},  // planar_config = chunky
   };
   out_file.write(reinterpret_cast<char*>(&tags),
                  sizeof(TIFFTagWireFormat) * num_tags);

   uint32_t next_ifd_offset = 0;
   out_file.write(reinterpret_cast<char*>(&next_ifd_offset),
                  sizeof(next_ifd_offset));

   for (uint32_t row = crop_origin_y; row < (crop_origin_y + crop_height);
        row++) {
      out_file.write(
          reinterpret_cast<const char*>(img.get_ptr(row, crop_origin_x)),
          crop_width * sizeof(uint16_t) * samples_per_pixel);
   }

   out_file.close();

   return absl::OkStatus();
}

}  // namespace cmrw
