#include <gtest/gtest.h>

#include "camraw/jpeg_bit_pump.h"
#include "camraw/memory_data_accessor.h"

using cmrw::JPEGBitPump;
using cmrw::MemoryDataAccessor;

class JPEGBitPumpTest : public ::testing::Test {
   protected:
      void SetUp() override {
         // Test data with JPEG byte stuffing:
         // 0xA5, 0xFF, 0x00, 0x3C, 0xFF, 0xD9 (EOI marker)
         test_data_ = {0xA5, 0xFF, 0x00, 0x3C, 0xFF, 0xD9};
         data_accessor_ = std::make_unique<MemoryDataAccessor>(
             test_data_.data(), test_data_.size());
      }

      std::vector<uint8_t> test_data_;
      std::unique_ptr<MemoryDataAccessor> data_accessor_;
};

TEST_F(JPEGBitPumpTest, HandleByteStuffing) {
   JPEGBitPump pump(data_accessor_.get(), 0);

   // First byte: 0xA5
   auto bits_or = pump.GetBits(8);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0xA5);

   // Next byte is 0xFF followed by 0x00 (byte stuffing)
   // Should read as a single 0xFF byte
   bits_or = pump.GetBits(8);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0xFF);

   // Next byte is 0x3C
   bits_or = pump.GetBits(8);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0x3C);
}

TEST_F(JPEGBitPumpTest, DetectMarker) {
   JPEGBitPump pump(data_accessor_.get(), 0);

   // Skip to position before the marker (after 0x3C)
   auto bits_or = pump.GetBits(8);  // 0xA5
   ASSERT_TRUE(bits_or.ok());
   bits_or = pump.GetBits(8);  // 0xFF (stuffed)
   ASSERT_TRUE(bits_or.ok());
   bits_or = pump.GetBits(8);  // 0x3C
   ASSERT_TRUE(bits_or.ok());

   // Next is 0xFF, 0xD9 (EOI marker)
   // Attempting to read should fail with UnimplementedError
   bits_or = pump.GetBits(8);
   EXPECT_FALSE(bits_or.ok());
   EXPECT_EQ(bits_or.status().code(), absl::StatusCode::kUnimplemented);
}

TEST_F(JPEGBitPumpTest, SkipMarker) {
   // Create test data with two bytes followed by the EOI marker: 0xA5, 0x3C,
   // 0xFF, 0xD9
   std::vector<uint8_t> marker_data = {0xA5, 0x3C, 0xFF, 0xD9};
   auto marker_accessor = std::make_unique<MemoryDataAccessor>(
       marker_data.data(), marker_data.size());

   JPEGBitPump pump(marker_accessor.get(), 0);

   // Read the first two bytes before the marker
   auto bits_or = pump.GetBits(8);  // Read 0xA5
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0xA5);

   bits_or = pump.GetBits(8);  // Read 0x3C
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0x3C);

   // Now we're positioned right before the marker
   // Skip the EOI marker (0xFF, 0xD9)
   auto marker_or = pump.SkipMarker();
   std::cout << marker_or.status() << std::endl;
   ASSERT_TRUE(marker_or.ok());
   EXPECT_EQ(*marker_or, 0xFFD9);  // EOI marker
}

TEST_F(JPEGBitPumpTest, GetBitsAcrossBoundaries) {
   // Test data with byte stuffing: 0xA5, 0xFF, 0x00, 0x3C
   std::vector<uint8_t> data = {0xA5, 0xFF, 0x00, 0x3C};
   auto accessor =
       std::make_unique<MemoryDataAccessor>(data.data(), data.size());

   JPEGBitPump pump(accessor.get(), 0);

   // Get 4 bits (1010) from first byte
   auto bits_or = pump.GetBits(4);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0xA);

   // Get 8 bits spanning first byte and stuffed byte
   // Remaining 4 bits from first byte: 0101
   // First 4 bits from 0xFF: 1111
   bits_or = pump.GetBits(8);
   ASSERT_TRUE(bits_or.ok());
   EXPECT_EQ(*bits_or, 0x5F);
}

TEST_F(JPEGBitPumpTest, ByteCount) {
   JPEGBitPump pump(data_accessor_.get(), 0);

   // Read first byte
   auto bits_or = pump.GetBits(8);
   ASSERT_TRUE(bits_or.ok());

   // Read stuffed 0xFF byte
   bits_or = pump.GetBits(8);
   ASSERT_TRUE(bits_or.ok());

   // Should have read 3 bytes (0xA5, 0xFF, 0x00)
   EXPECT_EQ(pump.GetNumBytesRead(), 3);
}
