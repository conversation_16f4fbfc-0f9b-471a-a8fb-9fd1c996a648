#ifndef _JPEG_H_
#define _JPEG_H_

#define JPEG_FRAME_HEADER 0xFFD8

namespace cmrw {

// Start of Frame non-differential Hu<PERSON>man coding
#define JPEG_MARKER_SOF_HUFF_LOSSLESS 0xFFC3
// Define <PERSON>man Table
#define JPEG_MARKER_DEFINE_HUFF_TABLE 0xFFC4
// Start of Scan
#define JPEG_MARKER_START_OF_SCAN 0xFFDA
// Define Restart Interval
#define JPEG_MARKER_DEFINE_RESTART_INTERVAL 0xFFDD
// Restart markers
#define JPEG_MARKER_RESTART_0 0xFFD0
#define JPEG_MARKER_RESTART_1 0xFFD1
#define JPEG_MARKER_RESTART_2 0xFFD2
#define JPEG_MARKER_RESTART_3 0xFFD3
#define JPEG_MARKER_RESTART_4 0xFFD4
#define JPEG_MARKER_RESTART_5 0xFFD5
#define JPEG_MARKER_RESTART_6 0xFFD6
#define JPEG_MARKER_RESTART_7 0xFFD7
// Application markers
#define JPEG_MARKER_APP0 0xFFE0
#define JPEG_MARKER_APP1 0xFFE1
#define JPEG_MARKER_APP2 0xFFE2
#define JPEG_MARKER_APP3 0xFFE3
#define JPEG_MARKER_APP4 0xFFE4
#define JPEG_MARKER_APP5 0xFFE5
#define JPEG_MARKER_APP6 0xFFE6
#define JPEG_MARKER_APP7 0xFFE7
#define JPEG_MARKER_APP8 0xFFE8
#define JPEG_MARKER_APP9 0xFFE9
#define JPEG_MARKER_APP10 0xFFEA
#define JPEG_MARKER_APP11 0xFFEB
#define JPEG_MARKER_APP12 0xFFEC
#define JPEG_MARKER_APP13 0xFFED
#define JPEG_MARKER_APP14 0xFFEE
#define JPEG_MARKER_APP15 0xFFEF

}  // namespace cmrw

#endif
