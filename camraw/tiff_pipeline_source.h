#ifndef TIFF_PIPELINE_SOURCE_H_
#define TIFF_PIPELINE_SOURCE_H_

#include <memory>
#include <unordered_set>

#include "absl/status/statusor.h"

#include "camraw/data_accessor.h"
#include "camraw/imagemeta.h"
#include "camraw/pipeline_source.h"
#include "camraw/status_macros.h"
#include "camraw/tiff.h"
#include "camraw/tiff_file_parser.h"

namespace cmrw {

// XXX: these should be namespaced
#define ASSIGN_TAG_VAL_OR_RETURN(type, varName, tagId)               \
   TIFF_PARSER_ASSIGN_TAG_VAL_OR_RETURN(file_parser_, type, varName, \
                                        TIFF_IFD_TYPE_PRIMARY_IMAGE, tagId)

#define ASSIGN_TAG_OR_RETURN(varName, tagId, ifdType)                        \
   do {                                                                      \
      ASSIGN_OR_RETURN(varName, file_parser_->GetTagFromIFD(tagId, ifdType), \
                       VLOG(2) << "Failed to get tag id: "                   \
                               << static_cast<uint16_t>(tagId));             \
   } while (0)

class TIFFPipelineSource : public PipelineSource {
   public:
      TIFFPipelineSource(TIFFFileParser* file_parser,
                         std::string name = "TIFFPipelineSource")
          : PipelineSource(name, {.one_sample_per_pixel = true,
                                  .three_samples_per_pixel = false}),
            file_parser_(file_parser),
            data_accessor_(file_parser->GetDataAccessor()) {}
      absl::Status Init() override;
      absl::StatusOr<std::unique_ptr<ImageMeta>> GetMetadata(
          std::unordered_set<ImageMetaId> required,
          std::unordered_set<ImageMetaId> optional) override;
      absl::StatusOr<std::vector<std::unique_ptr<PipelineSourceChunk>>>
      GetChunks() override;
      absl::Status ProcessChunk(const PipelineSourceChunk& chunk,
                                ImageBuf<uint16_t>& output) override;

   protected:
      std::unordered_set<TIFFCompressionType> supported_compression_types = {
          TIFF_COMPRESSION_UNCOMPRESSED,
      };
      struct ChunkConfiguration {
            enum class DataLayout {
               SINGLE_STRIP,
               MULTI_STRIPS,
               TILES,
            };
            std::unordered_set<DataLayout> allowed_data_layouts;
            std::string data_type;
            size_t expected_strip_size;  // expected size of a single strip,
                                         // 0 if not known/applicable
            bool needs_processing;
            uint32_t sub_chunk_byte_alignment;  // 0 if chunk is not divisible
      };
      virtual absl::StatusOr<ChunkConfiguration> GetChunkConfiguration();
      virtual absl::Status FetchAndSetMetadata(ImageMetaId id,
                                               ImageMeta& image_meta);

      bool initialized_ = false;
      uint16_t image_width_ = 0;
      uint16_t image_height_ = 0;
      TIFFCompressionType compression_ = TIFF_COMPRESSION_UNKNOWN;
      TIFFPhotometricInterpretation photo_int_ = TIFF_PHOTOMETRIC_INT_UNKNOWN;
      uint16_t samples_per_pixel_ = 0;
      uint16_t bits_per_sample_ = 0;
      TIFFFileParser* file_parser_ = nullptr;

   private:
      DataAccessor* data_accessor_ = nullptr;
};

}  // namespace cmrw

#endif /* TIFF_PIPELINE_SOURCE_H_ */
