#ifndef PIPELINE_PROCESSOR_H_
#define PIPELINE_PROCESSOR_H_

#include <functional>
#include <mutex>
#include <unordered_set>

#include "absl/status/status.h"

#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/pipeline_element.h"

namespace cmrw {

class PipelineProcessor : public PipelineElement {
   public:
      PipelineProcessor(std::string name, PipelineElementIOSpec input_spec,
                        PipelineElementIOSpec output_spec,
                        std::unordered_set<ImageMetaId> required_metadata,
                        std::unordered_set<ImageMetaId> optional_metadata)
          : PipelineElement(name, PipelineElementType::PROCESSOR, input_spec,
                            output_spec, required_metadata, optional_metadata),
            commit_stats_lock_() {}
      virtual ~PipelineProcessor() = default;
      virtual absl::Status Init(const ImageMeta& image_meta) = 0;
      virtual void LogStart() {};
      virtual absl::Status Run(const ImageBuf<uint16_t>& input_buffer,
                               ImageBuf<uint16_t>& output_buffer) = 0;
      virtual void LogStats() {};
      virtual absl::Status MutateMetadata(ImageMeta& image_meta) {
         return absl::OkStatus();
      };

   protected:
      void CommitStats(std::function<void()> commit_stats_func) {
         {
            std::unique_lock<std::mutex> lock(commit_stats_lock_);
            commit_stats_func();
         }
      }

   private:
      std::mutex commit_stats_lock_;
};

}  // namespace cmrw

#endif /* PIPELINE_PROCESSOR_H_ */
