#ifndef TIFF_H_
#define TIFF_H_

#include <cstdint>

namespace cmrw {

struct TIFFHeader {
      uint16_t order;       // Endianness (0x4949 = little, 0x4D4D = big)
      uint16_t version;     // TIFF version (always 2A)
      uint32_t ifd_offset;  // Offset of the first image file directory
};
constexpr uint16_t TIFF_HEADER_ORDER_LITTLE_ENDIAN = 0x4949;
constexpr uint16_t TIFF_HEADER_ORDER_BIG_ENDIAN = 0x4D4D;
constexpr uint16_t TIFF_HEADER_VERSION = 0x2A;

using TIFFCompressionType = uint16_t;
constexpr TIFFCompressionType TIFF_COMPRESSION_UNKNOWN = 0;
constexpr TIFFCompressionType TIFF_COMPRESSION_UNCOMPRESSED = 1;
constexpr TIFFCompressionType TIFF_COMPRESSION_OLD_STYLE_JPEG = 6;
constexpr TIFFCompressionType TIFF_COMPRESSION_JPEG = 7;

using TIFFPhotometricInterpretation = uint16_t;
constexpr TIFFPhotometricInterpretation TIFF_PHOTOMETRIC_INT_UNKNOWN = 9999;
constexpr TIFFPhotometricInterpretation TIFF_PHOTOMETRIC_INT_GRAY_WHITE_ZERO =
    0;
constexpr TIFFPhotometricInterpretation TIFF_PHOTOMETRIC_INT_GRAY_BLACK_ZERO =
    1;
constexpr TIFFPhotometricInterpretation TIFF_PHOTOMETRIC_INT_RGB = 2;
constexpr TIFFPhotometricInterpretation TIFF_PHOTOMETRIC_INT_PALETTE = 3;
constexpr TIFFPhotometricInterpretation TIFF_PHOTOMETRIC_INT_TRANS_MASK = 4;
constexpr TIFFPhotometricInterpretation TIFF_PHOTOMETRIC_INT_CMYK = 5;
constexpr TIFFPhotometricInterpretation TIFF_PHOTOMETRIC_INT_YCBCR = 6;
constexpr TIFFPhotometricInterpretation TIFF_PHOTOMETRIC_INT_CFA = 32803;
constexpr TIFFPhotometricInterpretation TIFF_PHOTOMETRIC_INT_LINEAR_RAW = 34892;

}  // namespace cmrw

#endif  // TIFF_H_
