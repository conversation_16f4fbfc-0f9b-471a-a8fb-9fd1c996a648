#include <glog/logging.h>

#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/scale_data_processor.h"
#include "camraw/status_macros.h"

namespace cmrw {

absl::Status
ScaleDataProcessor::Init(const ImageMeta& image_meta) {
   ASSIGN_OR_RETURN(black_level_,
                    image_meta.GetOne<uint16_t>(ImageMetaId::BLACK_LEVEL),
                    VLOG(2) << "Failed to retrieve black level metadata");

   ASSIGN_OR_RETURN(white_level_,
                    image_meta.GetOne<uint16_t>(ImageMetaId::WHITE_LEVEL),
                    VLOG(2) << "Failed to retrieve white level metadata");

   ASSIGN_OR_RETURN(samples_per_pixel_,
                    image_meta.GetOne<uint16_t>(ImageMetaId::SAMPLES_PER_PIXEL),
                    VLOG(2) << "Failed to retrieve samples per pixel metadata");

   return absl::OkStatus();
}

void
ScaleDataProcessor::LogStart() {
   LOG(INFO) << "Scaling data to black level: " << black_level_
             << " white level: " << white_level_;
}

absl::Status
ScaleDataProcessor::Run(const ImageBuf<uint16_t>& input_buffer,
                        ImageBuf<uint16_t>& output_buffer) {
   CHECK_EQ(input_buffer.get_height(), output_buffer.get_height());
   CHECK_EQ(input_buffer.get_width(), output_buffer.get_width());
   CHECK_EQ(input_buffer.get_components(), samples_per_pixel_);
   CHECK_GT(white_level_, black_level_);

   // Scaling to 16 bits - divide by original white level after subtracting
   // black level, multiply by 2^16
   uint16_t orig_whitelevel = white_level_ - black_level_;
   uint16_t bit16_whitelevel = pow(2, 16) - 1;  // 2^16-1
   uint32_t black_underrun = 0, white_overrun = 0;

   for (uint32_t row = 0; row < input_buffer.get_height(); row++) {
      for (uint32_t col = 0; col < input_buffer.get_width(); col++) {
         for (uint32_t comp = 0; comp < input_buffer.get_components(); comp++) {
            uint16_t orig_val = input_buffer(row, col, comp);
            if (orig_val < black_level_) {
               orig_val = black_level_;
               black_underrun++;
            } else if (orig_val > white_level_) {
               orig_val = white_level_;
               white_overrun++;
            }

            // Scale from original bit depth to 16 bits
            uint16_t orig_bit_depth_val = orig_val - black_level_;
            output_buffer(row, col, comp) = static_cast<uint16_t>(
                (static_cast<float>(orig_bit_depth_val) * bit16_whitelevel) /
                orig_whitelevel);
         }
      }
   }

   CommitStats([&] {
      black_underrun_ += black_underrun;
      white_overrun_ += white_overrun;
   });

   return absl::OkStatus();
}

void
ScaleDataProcessor::LogStats() {
   LOG(INFO) << black_underrun_ << " pixels were below black level.";
   LOG(INFO) << white_overrun_ << " pixels were above white level.";
}

absl::Status
ScaleDataProcessor::MutateMetadata(ImageMeta& image_meta) {
   image_meta.SetOne<uint16_t>(ImageMetaId::BITS_PER_SAMPLE, 16);
   VLOG(3) << "Set metadata BITS_PER_SAMPLE = 16";

   return absl::OkStatus();
}

}  // namespace cmrw
