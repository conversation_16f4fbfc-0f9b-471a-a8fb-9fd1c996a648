#ifndef DNG_FILE_PARSER_H_
#define DNG_FILE_PARSER_H_

#include "camraw/data_accessor.h"
#include "camraw/tiff_file_parser.h"

namespace cmrw {

class DNGFileParser : public TIFFFileParser {
   public:
      DNGFileParser(DataAccessor& data_accessor)
          : TIFFFileParser(data_accessor) {}

      absl::StatusOr<bool> ValidateFile() override;
};

}  // namespace cmrw

#endif /* DNG_FILE_PARSER_H_ */
