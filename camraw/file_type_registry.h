#ifndef FILE_TYPE_REGISTRY_H_
#define FILE_TYPE_REGISTRY_H_

#include <functional>
#include <memory>
#include <string>
#include <vector>

#include "absl/container/flat_hash_map.h"
#include "absl/status/status.h"
#include "absl/status/statusor.h"

#include "camraw/data_accessor.h"
#include "camraw/dng_file_parser.h"
#include "camraw/dng_pipeline_source.h"
#include "camraw/file_parser.h"
#include "camraw/pipeline_source.h"
#include "camraw/sony_raw_file_parser.h"
#include "camraw/sony_raw_pipeline_source.h"
#include "camraw/tiff_file_parser.h"
#include "camraw/tiff_pipeline_source.h"

namespace cmrw {

#define REGISTER_FILE_TYPE(name, parent, parser_type, source_type, hints) \
   inline FileTypeRegistrationHelper registered_file_type_##parser_type(  \
       {.type_name = name,                                                \
        .parent_type_name = parent,                                       \
        .create_parser_fn =                                               \
            [](DataAccessor& accessor) {                                  \
               return std::make_unique<parser_type>(accessor);            \
            },                                                            \
        .create_pipeline_src_fn =                                         \
            [](FileParser* parser) {                                      \
               return std::make_unique<source_type>(                      \
                   dynamic_cast<parser_type*>(parser));                   \
            },                                                            \
        .extension_hints = std::vector<std::string>(hints)});

struct FileTypeRegistryElement {
      std::string type_name;
      std::string parent_type_name;
      std::function<std::unique_ptr<FileParser>(DataAccessor&)>
          create_parser_fn;
      std::function<std::unique_ptr<PipelineSource>(FileParser*)>
          create_pipeline_src_fn;
      std::vector<std::string> extension_hints;
};

class FileTypeRegistry {
   public:
      static FileTypeRegistry& GetRegistry();
      absl::Status InsertElement(FileTypeRegistryElement element);
      absl::StatusOr<const FileTypeRegistryElement*> GetElement(
          const std::string type_name) const;
      const std::vector<std::string>& GetOrderedTypeNames() const {
         return ordered_type_names_;
      }

      FileTypeRegistry(const FileTypeRegistry&) = delete;
      FileTypeRegistry& operator=(const FileTypeRegistry&) = delete;

   private:
      FileTypeRegistry() = default;
      ~FileTypeRegistry() = default;

      absl::flat_hash_map<std::string, FileTypeRegistryElement> element_map_;
      std::vector<std::string> ordered_type_names_;
};

class FileTypeRegistrationHelper {
   public:
      FileTypeRegistrationHelper(const FileTypeRegistryElement& element) {
         FileTypeRegistry& registry = FileTypeRegistry::GetRegistry();
         absl::Status status = registry.InsertElement(element);
         assert(status.ok());
      }
};

// Note that parent nodes need to appear *before* their children in this list.
REGISTER_FILE_TYPE("TIFF", "", TIFFFileParser, TIFFPipelineSource, {".TIFF"});
REGISTER_FILE_TYPE("Sony RAW", "TIFF", SonyRawFileParser, SonyRawPipelineSource,
                   {".ARW"});
REGISTER_FILE_TYPE("DNG", "TIFF", DNGFileParser, DNGPipelineSource, {".DNG"});

}  // namespace cmrw

#endif /* FILE_TYPE_REGISTRY_H_ */
