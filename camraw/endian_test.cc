#include <cstdint>
#include <gtest/gtest.h>

#include "camraw/endian.h"

using cmrw::big_endian;
using cmrw::little_endian;

TEST(EndianTest, EndianDataOrder8) {
   char data[] = "a";
   uint8_t* uint_ptr = reinterpret_cast<uint8_t*>(&data);
   uint8_t uint_val = little_endian<uint8_t>(*uint_ptr);
   char* val_ptr = reinterpret_cast<char*>(&uint_val);
   EXPECT_EQ(val_ptr[0], 'a');
}

TEST(EndianTest, LittleEndianDataOrder16) {
   char data[] = "ab";
   uint16_t* uint_ptr = reinterpret_cast<uint16_t*>(&data);
#if ARCH_IS_LITTLE_ENDIAN
   uint16_t uint_val = little_endian<uint16_t>(*uint_ptr);
   char* val_ptr = reinterpret_cast<char*>(&uint_val);
   EXPECT_EQ(val_ptr[0], 'a');
   EXPECT_EQ(val_ptr[1], 'b');
#else
   uint16_t uint_val = little_endian<uint16_t>(*uint_ptr);
   char* val_ptr = reinterpret_cast<char*>(&uint_val);
   EXPECT_EQ(val_ptr[0], 'a');
   EXPECT_EQ(val_ptr[1], 'b');
#endif
}

TEST(EndianTest, BigEndianDataOrder16) {
   char data[] = "ab";
   uint16_t* uint_ptr = reinterpret_cast<uint16_t*>(&data);
#if ARCH_IS_LITTLE_ENDIAN
   uint16_t uint_val = big_endian<uint16_t>(*uint_ptr);
   char* val_ptr = reinterpret_cast<char*>(&uint_val);
   EXPECT_EQ(val_ptr[0], 'b');
   EXPECT_EQ(val_ptr[1], 'a');
#else
   uint16_t uint_val = big_endian<uint16_t>(*uint_ptr);
   char* val_ptr = reinterpret_cast<char*>(&uint_val);
   EXPECT_EQ(val_ptr[0], 'a');
   EXPECT_EQ(val_ptr[1], 'b');
#endif
}

TEST(EndianTest, LittleEndianDataOrder32) {
   char data[] = "abcd";
   uint32_t* uint_ptr = reinterpret_cast<uint32_t*>(&data);
#if ARCH_IS_LITTLE_ENDIAN
   uint32_t uint_val = little_endian<uint32_t>(*uint_ptr);
   char* val_ptr = reinterpret_cast<char*>(&uint_val);
   EXPECT_EQ(val_ptr[0], 'a');
   EXPECT_EQ(val_ptr[1], 'b');
   EXPECT_EQ(val_ptr[2], 'c');
   EXPECT_EQ(val_ptr[3], 'd');
#else
   uint32_t uint_val = little_endian<uint32_t>(*uint_ptr);
   char* val_ptr = reinterpret_cast<char*>(&uint_val);
   EXPECT_EQ(val_ptr[0], 'd');
   EXPECT_EQ(val_ptr[1], 'c');
   EXPECT_EQ(val_ptr[2], 'b');
   EXPECT_EQ(val_ptr[3], 'a');
#endif
}

TEST(EndianTest, BigEndianDataOrder32) {
   char data[] = "abcd";
   uint32_t* uint_ptr = reinterpret_cast<uint32_t*>(&data);
#if ARCH_IS_LITTLE_ENDIAN
   uint32_t uint_val = big_endian<uint32_t>(*uint_ptr);
   char* val_ptr = reinterpret_cast<char*>(&uint_val);
   EXPECT_EQ(val_ptr[0], 'd');
   EXPECT_EQ(val_ptr[1], 'c');
   EXPECT_EQ(val_ptr[2], 'b');
   EXPECT_EQ(val_ptr[3], 'a');
#else
   uint32_t uint_val = big_endian<uint32_t>(*uint_ptr);
   char* val_ptr = reinterpret_cast<char*>(&uint_val);
   EXPECT_EQ(val_ptr[0], 'a');
   EXPECT_EQ(val_ptr[1], 'b');
   EXPECT_EQ(val_ptr[2], 'c');
   EXPECT_EQ(val_ptr[3], 'd');
#endif
}

TEST(EndianTest, LittleEndianDataOrder64) {
   char data[] = "abcdefgh";
   uint64_t* uint_ptr = reinterpret_cast<uint64_t*>(&data);
#if ARCH_IS_LITTLE_ENDIAN
   uint64_t uint_val = little_endian<uint64_t>(*uint_ptr);
   char* val_ptr = reinterpret_cast<char*>(&uint_val);
   EXPECT_EQ(val_ptr[0], 'a');
   EXPECT_EQ(val_ptr[1], 'b');
   EXPECT_EQ(val_ptr[2], 'c');
   EXPECT_EQ(val_ptr[3], 'd');
   EXPECT_EQ(val_ptr[4], 'e');
   EXPECT_EQ(val_ptr[5], 'f');
   EXPECT_EQ(val_ptr[6], 'g');
   EXPECT_EQ(val_ptr[7], 'h');
#else
   uint64_t uint_val = little_endian<uint64_t>(*uint_ptr);
   char* val_ptr = reinterpret_cast<char*>(&uint_val);
   EXPECT_EQ(val_ptr[0], 'h');
   EXPECT_EQ(val_ptr[1], 'g');
   EXPECT_EQ(val_ptr[2], 'f');
   EXPECT_EQ(val_ptr[3], 'e');
   EXPECT_EQ(val_ptr[4], 'd');
   EXPECT_EQ(val_ptr[5], 'c');
   EXPECT_EQ(val_ptr[6], 'b');
   EXPECT_EQ(val_ptr[7], 'a');
#endif
}

TEST(EndianTest, BigEndianDataOrder64) {
   char data[] = "abcdefgh";
   uint64_t* uint_ptr = reinterpret_cast<uint64_t*>(&data);
#if ARCH_IS_LITTLE_ENDIAN
   uint64_t uint_val = big_endian<uint64_t>(*uint_ptr);
   char* val_ptr = reinterpret_cast<char*>(&uint_val);
   EXPECT_EQ(val_ptr[0], 'h');
   EXPECT_EQ(val_ptr[1], 'g');
   EXPECT_EQ(val_ptr[2], 'f');
   EXPECT_EQ(val_ptr[3], 'e');
   EXPECT_EQ(val_ptr[4], 'd');
   EXPECT_EQ(val_ptr[5], 'c');
   EXPECT_EQ(val_ptr[6], 'b');
   EXPECT_EQ(val_ptr[7], 'a');
#else
   uint64_t uint_val = big_endian<uint64_t>(*uint_ptr);
   char* val_ptr = reinterpret_cast<char*>(&uint_val);
   EXPECT_EQ(val_ptr[0], 'a');
   EXPECT_EQ(val_ptr[1], 'b');
   EXPECT_EQ(val_ptr[2], 'c');
   EXPECT_EQ(val_ptr[3], 'd');
   EXPECT_EQ(val_ptr[4], 'e');
   EXPECT_EQ(val_ptr[5], 'f');
   EXPECT_EQ(val_ptr[6], 'g');
   EXPECT_EQ(val_ptr[7], 'h');
#endif
}

TEST(EndianTest, LittleEndianDataOrderSigned32) {
   char data[] = "abcd";
   int32_t* uint_ptr = reinterpret_cast<int32_t*>(&data);
#if ARCH_IS_LITTLE_ENDIAN
   int32_t uint_val = little_endian<int32_t>(*uint_ptr);
   char* val_ptr = reinterpret_cast<char*>(&uint_val);
   EXPECT_EQ(val_ptr[0], 'a');
   EXPECT_EQ(val_ptr[1], 'b');
   EXPECT_EQ(val_ptr[2], 'c');
   EXPECT_EQ(val_ptr[3], 'd');
#else
   int32_t uint_val = little_endian<int32_t>(*uint_ptr);
   char* val_ptr = reinterpret_cast<char*>(&uint_val);
   EXPECT_EQ(val_ptr[0], 'd');
   EXPECT_EQ(val_ptr[1], 'c');
   EXPECT_EQ(val_ptr[2], 'b');
   EXPECT_EQ(val_ptr[3], 'a');
#endif
}
