#include <gtest/gtest.h>

#include "camraw/imagemeta.h"

using cmrw::ImageMeta;
using cmrw::ImageMetaId;

TEST(ImageMetaTest, SingleValue) {
   ImageMeta meta;

   EXPECT_FALSE(meta.IsSet(ImageMetaId::IMAGE_WIDTH));

   meta.SetOne<uint16_t>(ImageMetaId::IMAGE_WIDTH, 2048);
   EXPECT_TRUE(meta.IsSet(ImageMetaId::IMAGE_WIDTH));

   auto width_or = meta.GetOne<uint16_t>(ImageMetaId::IMAGE_WIDTH);
   EXPECT_TRUE(width_or.ok());
   EXPECT_EQ(*width_or, 2048);

   meta.Clear(ImageMetaId::IMAGE_WIDTH);
   EXPECT_FALSE(meta.IsSet(ImageMetaId::IMAGE_WIDTH));

   width_or = meta.GetOne<uint16_t>(ImageMetaId::IMAGE_WIDTH);
   EXPECT_FALSE(width_or.ok());
}

TEST(ImageMetaTest, SetOneWrongType) {
   ImageMeta meta;

   EXPECT_DEATH({ meta.SetOne<uint32_t>(ImageMetaId::IMAGE_WIDTH, 2048); }, "");
}

TEST(ImageMetaTest, GetOneWrongType) {
   ImageMeta meta;

   meta.SetOne<uint16_t>(ImageMetaId::IMAGE_WIDTH, 2048);
   EXPECT_TRUE(meta.IsSet(ImageMetaId::IMAGE_WIDTH));

   EXPECT_DEATH(
       { auto width_or = meta.GetOne<uint32_t>(ImageMetaId::IMAGE_WIDTH); },
       "");
}

TEST(ImageMetaTest, MultiValue) {
   ImageMeta meta;

   EXPECT_FALSE(meta.IsSet(ImageMetaId::WHITE_BALANCE_MULTIPLIERS));

   std::vector<float> values = {1.2, 3.4, 5.6};
   meta.SetMulti<float>(ImageMetaId::WHITE_BALANCE_MULTIPLIERS, values);
   EXPECT_TRUE(meta.IsSet(ImageMetaId::WHITE_BALANCE_MULTIPLIERS));

   auto result_or =
       meta.GetMulti<float>(ImageMetaId::WHITE_BALANCE_MULTIPLIERS);
   EXPECT_TRUE(result_or.ok());
   int i = 0;
   for (auto result : *result_or) {
      EXPECT_EQ(result, values[i]);
      i++;
   }

   meta.Clear(ImageMetaId::WHITE_BALANCE_MULTIPLIERS);
   EXPECT_FALSE(meta.IsSet(ImageMetaId::WHITE_BALANCE_MULTIPLIERS));

   result_or = meta.GetMulti<float>(ImageMetaId::WHITE_BALANCE_MULTIPLIERS);
   EXPECT_FALSE(result_or.ok());
}

TEST(ImageMetaTest, StringType) {
   ImageMeta meta;

   EXPECT_FALSE(meta.IsSet(ImageMetaId::CAMERA_MAKE));

   meta.SetOne<std::string>(ImageMetaId::CAMERA_MAKE, "Sony");
   EXPECT_TRUE(meta.IsSet(ImageMetaId::CAMERA_MAKE));

   auto make_or = meta.GetOne<std::string>(ImageMetaId::CAMERA_MAKE);
   EXPECT_TRUE(make_or.ok());
   EXPECT_EQ(*make_or, "Sony");
}
