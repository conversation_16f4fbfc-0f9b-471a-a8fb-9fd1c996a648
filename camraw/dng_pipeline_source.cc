#include <glog/logging.h>

#include "camraw/dng_pipeline_source.h"
#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/lossless_jpeg_decoder.h"
#include "camraw/matrix.h"
#include "camraw/memory_data_accessor.h"
#include "camraw/status_macros.h"
#include "camraw/tiff.h"
#include "camraw/tiff_pipeline_source.h"
#include "camraw/tiff_tag_id.h"

namespace cmrw {

absl::Status
DNGPipelineSource::Init() {
   return TIFFPipelineSource::Init();
}

absl::Status
DNGPipelineSource::FetchAndSetMetadata(ImageMetaId id, ImageMeta& image_meta) {
   switch (id) {
      case ImageMetaId::BLACK_LEVEL: {
         const TIFFTag* black_levels_tag;
         ASSIGN_TAG_OR_RETURN(black_levels_tag, TIFFTagId::BLACK_LEVEL,
                              TIFF_IFD_TYPE_PRIMARY_IMAGE);

         if (black_levels_tag->GetDataType() == TIFFTagDataType::RATIONAL) {
            std::unique_ptr<float[]> black_levels;
            ASSIGN_OR_RETURN(black_levels,
                             black_levels_tag->GetAllDataValues<float>(),
                             VLOG(2) << "Failed to get black levels values");
            for (size_t i = 1; i < black_levels_tag->GetDataCount(); i++) {
               CHECK_EQ(black_levels[0], black_levels[i]);
            }
            image_meta.SetOne(ImageMetaId::BLACK_LEVEL,
                              static_cast<uint16_t>(black_levels[0]));
            VLOG(3) << "Fetch metadata BLACK_LEVEL = " << black_levels[0];
         } else {
            CHECK(black_levels_tag->GetDataType() == TIFFTagDataType::LONG ||
                  black_levels_tag->GetDataType() == TIFFTagDataType::SHORT);
            std::unique_ptr<uint32_t[]> black_levels;
            ASSIGN_OR_RETURN(black_levels,
                             black_levels_tag->GetAllDataValues<uint32_t>(),
                             VLOG(2) << "Failed to get black levels values");
            for (size_t i = 1; i < black_levels_tag->GetDataCount(); i++) {
               CHECK_EQ(black_levels[0], black_levels[i]);
            }
            image_meta.SetOne(ImageMetaId::BLACK_LEVEL,
                              static_cast<uint16_t>(black_levels[0]));
            VLOG(3) << "Fetch metadata BLACK_LEVEL = " << black_levels[0];
         }
      } break;
      case ImageMetaId::WHITE_LEVEL: {
         const TIFFTag* white_levels_tag;
         ASSIGN_TAG_OR_RETURN(white_levels_tag, TIFFTagId::WHITE_LEVEL,
                              TIFF_IFD_TYPE_PRIMARY_IMAGE);

         if (white_levels_tag->GetDataCount() == 1) {
            CHECK(white_levels_tag->GetDataType() == TIFFTagDataType::LONG ||
                  white_levels_tag->GetDataType() == TIFFTagDataType::SHORT);
            uint32_t white_level;
            ASSIGN_OR_RETURN(white_level,
                             white_levels_tag->GetDataValue<uint32_t>(),
                             VLOG(2) << "Failed to get white level value");
            image_meta.SetOne(ImageMetaId::WHITE_LEVEL,
                              static_cast<uint16_t>(white_level));
            VLOG(3) << "Fetch metadata WHITE_LEVEL = " << white_level;
         } else {
            CHECK(white_levels_tag->GetDataType() == TIFFTagDataType::LONG ||
                  white_levels_tag->GetDataType() == TIFFTagDataType::SHORT);
            std::unique_ptr<uint32_t[]> white_levels;
            ASSIGN_OR_RETURN(white_levels,
                             white_levels_tag->GetAllDataValues<uint32_t>(),
                             VLOG(2) << "Failed to get white levels values");
            for (size_t i = 1; i < white_levels_tag->GetDataCount(); i++) {
               CHECK_EQ(white_levels[0], white_levels[i]);
            }
            image_meta.SetOne(ImageMetaId::WHITE_LEVEL,
                              static_cast<uint16_t>(white_levels[0]));
            VLOG(3) << "Fetch metadata WHITE_LEVEL = " << white_levels[0];
         }
      } break;
      case ImageMetaId::CROP_SIZE: {
         const TIFFTag* default_crop_size_tag;
         ASSIGN_TAG_OR_RETURN(default_crop_size_tag,
                              TIFFTagId::DEFAULT_CROP_SIZE,
                              TIFF_IFD_TYPE_PRIMARY_IMAGE);
         CHECK_EQ(default_crop_size_tag->GetDataCount(), 2U);

         uint32_t crop_size[2];
         if (default_crop_size_tag->GetDataType() ==
             TIFFTagDataType::RATIONAL) {
            std::unique_ptr<float[]> default_crop_size;
            ASSIGN_OR_RETURN(default_crop_size,
                             default_crop_size_tag->GetAllDataValues<float>(),
                             VLOG(2)
                                 << "Failed to get default crop size values");
            crop_size[0] = static_cast<uint32_t>(default_crop_size[0]);
            crop_size[1] = static_cast<uint32_t>(default_crop_size[1]);
         } else {
            std::unique_ptr<uint32_t[]> default_crop_size;
            ASSIGN_OR_RETURN(
                default_crop_size,
                default_crop_size_tag->GetAllDataValues<uint32_t>(),
                VLOG(2) << "Failed to get default crop size values");
            crop_size[0] = default_crop_size[0];
            crop_size[1] = default_crop_size[1];
         }
         std::vector<uint32_t> default_crop_size = {crop_size[0], crop_size[1]};
         image_meta.SetMulti(ImageMetaId::CROP_SIZE, default_crop_size);
         VLOG(3) << "Fetch metadata CROP_SIZE = (" << default_crop_size[0]
                 << "," << default_crop_size[1] << ")";
      } break;
      case ImageMetaId::CROP_ORIGIN: {
         const TIFFTag* default_crop_origin_tag;
         ASSIGN_TAG_OR_RETURN(default_crop_origin_tag,
                              TIFFTagId::DEFAULT_CROP_ORIGIN,
                              TIFF_IFD_TYPE_PRIMARY_IMAGE);

         CHECK_EQ(default_crop_origin_tag->GetDataCount(), 2U);
         std::unique_ptr<float[]> default_crop_origin;
         ASSIGN_OR_RETURN(default_crop_origin,
                          default_crop_origin_tag->GetAllDataValues<float>(),
                          VLOG(2)
                              << "Failed to get default crop origin values");
         std::vector<uint32_t> crop_origin = {
             static_cast<uint32_t>(default_crop_origin[0]),
             static_cast<uint32_t>(default_crop_origin[1])};
         image_meta.SetMulti(ImageMetaId::CROP_ORIGIN, crop_origin);
         VLOG(3) << "Fetch metadata CROP_ORIGIN = (" << default_crop_origin[0]
                 << "," << default_crop_origin[1] << ")";
      } break;
      case ImageMetaId::WHITE_BALANCE_MULTIPLIERS: {
         if (wb_not_needed_) {
            std::vector<float> wb_multipliers = {1.0, 1.0, 1.0};
            image_meta.SetMulti(ImageMetaId::WHITE_BALANCE_MULTIPLIERS,
                                wb_multipliers);
            VLOG(4) << "Skipping white balancing.";
            VLOG(3) << "Set metadata WHITE_BALANCE_MULTIPLIERS = ("
                    << wb_multipliers[0] << "," << wb_multipliers[1] << ","
                    << wb_multipliers[2] << ")";
            break;
         }
         const TIFFIFD* first_ifd;
         ASSIGN_OR_RETURN(first_ifd, file_parser_->GetIFDAtIndex(0),
                          VLOG(2) << "Failed to retrieve first IFD");

         const TIFFTag* asn_tag;
         ASSIGN_OR_RETURN(asn_tag,
                          first_ifd->GetTag(TIFFTagId::AS_SHOT_NEUTRAL),
                          VLOG(2) << "Failed to get as shot neutral tag");

         CHECK_EQ(asn_tag->GetDataCount(), 3U);
         CHECK_EQ(static_cast<int>(asn_tag->GetDataType()),
                  static_cast<int>(TIFFTagDataType::RATIONAL));

         std::unique_ptr<float[]> asn;
         ASSIGN_OR_RETURN(asn, asn_tag->GetAllDataValues<float>(),
                          VLOG(2) << "Failed to get as shot neutral values");

         // DNG as_shot_neutral values are dividers, convert to multipliers
         float wb_mult_red = static_cast<float>(1) / asn[0];
         float wb_mult_green = static_cast<float>(1) / asn[1];
         float wb_mult_blue = static_cast<float>(1) / asn[2];

         std::vector<float> wb_multipliers = {wb_mult_red, wb_mult_green,
                                              wb_mult_blue};
         image_meta.SetMulti(ImageMetaId::WHITE_BALANCE_MULTIPLIERS,
                             wb_multipliers);
         VLOG(3) << "Set metadata WHITE_BALANCE_MULTIPLIERS = ("
                 << wb_multipliers[0] << "," << wb_multipliers[1] << ","
                 << wb_multipliers[2] << ")";
      } break;
      case ImageMetaId::COMPRESSION: {
         switch (compression_) {
            case TIFF_COMPRESSION_JPEG:
               image_meta.SetOne<ImageMetaCompression>(
                   ImageMetaId::COMPRESSION, ImageMetaCompression::JPEG);
               VLOG(3) << "Fetch metadata COMPRESSION = JPEG";
               break;
            case TIFF_COMPRESSION_UNCOMPRESSED:
               image_meta.SetOne<ImageMetaCompression>(
                   ImageMetaId::COMPRESSION,
                   ImageMetaCompression::UNCOMPRESSED);
               VLOG(3) << "Fetch metadata COMPRESSION = Uncompressed";
               break;
            default:
               VLOG(2) << "Unsupported compression scheme: " << compression_;
               return absl::UnimplementedError(
                   "Unsupported compression scheme");
               break;
         }
      } break;
      case ImageMetaId::CAMERA_COLOR_MATRIX: {
         const TIFFIFD* first_ifd;
         ASSIGN_OR_RETURN(first_ifd, file_parser_->GetIFDAtIndex(0),
                          VLOG(2) << "Failed to retrieve first IFD");
         /*
          * We're using the ForwardMatrix2 values because we mostly care
          * about SRGB conversion and hence the D65 illuminant. For future
          * versions of this we may need to look at potentially combining in
          * ForwardMatrix1 based on illuminant information.
          */
         bool invert_matrix = false;
         const TIFFTag* matrix_tag;
         auto forward_matrix1_tag_or =
             first_ifd->GetTag(TIFFTagId::FORWARD_MATRIX1);
         if (!forward_matrix1_tag_or.ok()) {
            if (forward_matrix1_tag_or.status().code() ==
                absl::StatusCode::kNotFound) {
               // Some DNG files don't have ForwardMatrix* embedded so we
               // need to compute it from ColorMatrix.
               ASSIGN_OR_RETURN(matrix_tag,
                                first_ifd->GetTag(TIFFTagId::COLOR_MATRIX1),
                                VLOG(2) << "Failed to get color matrix tag");
               invert_matrix = true;
            } else {
               VLOG(2) << "Failed to get forward matrix tag";
               return forward_matrix1_tag_or.status();
            }
         } else {
            matrix_tag = *forward_matrix1_tag_or;
         }

         CHECK_EQ(matrix_tag->GetDataCount(), 9U);
         std::unique_ptr<float[]> matrix_values;
         ASSIGN_OR_RETURN(matrix_values, matrix_tag->GetAllDataValues<float>(),
                          VLOG(2) << "Failed to get matrix values");
         std::vector<float> matrix(9);
         for (int i = 0; i < 9; i++) {
            matrix[i] = matrix_values[i];
         }

         if (invert_matrix) {
            VLOG(3) << "Inverting color matrix to compute forward matrix";
            Matrix color_matrix(3, 3, matrix);
            Matrix<float> inverted_matrix(3, 3);
            ASSIGN_OR_RETURN(inverted_matrix, color_matrix.invert(),
                             VLOG(2) << "Failed to invert color matrix");
            int idx = 0;
            for (int i = 0; i < 3; i++) {
               for (int j = 0; j < 3; j++) {
                  matrix[idx] = inverted_matrix(i, j);
                  idx++;
               }
            }
            /*
             * Since the inverted ColorMatrix needs to be applied on
             * non white balanced camera raw RGB values we disable the white
             * balance step by resetting the multipliers to 1.0.
             */
            std::vector<float> wb_multipliers = {1.0, 1.0, 1.0};
            image_meta.SetMulti(ImageMetaId::WHITE_BALANCE_MULTIPLIERS,
                                wb_multipliers);
            // This bool indicates to future FetchAndSetMetadata calls that
            // white balancing is not needed.
            wb_not_needed_ = true;
            VLOG(3)
                << "Reset metadata WHITE_BALANCE_MULTIPLIERS = (1.0, 1.0, 1.0)";
         }

         image_meta.SetMulti(ImageMetaId::CAMERA_COLOR_MATRIX, matrix);
         VLOG(3) << "Set metadata CAMERA_COLOR_MATRIX = [[" << matrix[0] << ","
                 << matrix[1] << "," << matrix[2] << "],[" << matrix[3] << ","
                 << matrix[4] << "," << matrix[5] << "],[" << matrix[6] << ","
                 << matrix[7] << "," << matrix[8] << "]]";
      } break;
      default: {
         return TIFFPipelineSource::FetchAndSetMetadata(id, image_meta);
      }
   }
   return absl::OkStatus();
}

absl::StatusOr<TIFFPipelineSource::ChunkConfiguration>
DNGPipelineSource::GetChunkConfiguration() {
   if (photo_int_ == TIFF_PHOTOMETRIC_INT_CFA) {
      if (compression_ == TIFF_COMPRESSION_JPEG) {
         return ChunkConfiguration{
             .allowed_data_layouts = {ChunkConfiguration::DataLayout::TILES},
             .data_type = "lossless compressed DNG RAW",
             .expected_strip_size = 0,
             .needs_processing = true,
             .sub_chunk_byte_alignment = 0,  // chunk is indivisible
         };
      } else if (compression_ == TIFF_COMPRESSION_UNCOMPRESSED) {
         return ChunkConfiguration{
             .allowed_data_layouts =
                 {ChunkConfiguration::DataLayout::SINGLE_STRIP},
             .data_type = "uncompressed DNG RAW",
             .expected_strip_size =
                 image_width_ * image_height_ * sizeof(uint16_t),
             .needs_processing =
                 NOT_SAME_ENDIANNESS_AS_ARCH(file_parser_->GetEndianness()),
             .sub_chunk_byte_alignment = sizeof(uint16_t),
         };
      }
   } else if (photo_int_ == TIFF_PHOTOMETRIC_INT_LINEAR_RAW) {
      if (compression_ == TIFF_COMPRESSION_JPEG) {
         return ChunkConfiguration{
             .allowed_data_layouts =
                 {ChunkConfiguration::DataLayout::SINGLE_STRIP,
                  ChunkConfiguration::DataLayout::TILES},
             .data_type = "lossless compressed DNG linear RAW",
             .expected_strip_size = 0,
             .needs_processing = true,
             .sub_chunk_byte_alignment = 0,  // chunk is indivisible
         };
      }
   }

   VLOG(2) << "Unsupported DNG file configuration photo_int: " << photo_int_
           << " compression: " << compression_;
   return absl::UnimplementedError("Unsupported DNG file configuration");
}

/*
 * Note that ProcessChunk uses processed height/width to determine output
 * dimensions. This is to be able to deal with tiles that overflow the true
 * image boundaries.
 */
absl::Status
DNGPipelineSource::ProcessChunk(const PipelineSourceChunk& chunk,
                                ImageBuf<uint16_t>& output) {
   if (!initialized_) {
      return absl::FailedPreconditionError("Pipeline source not initialized");
   }
   CHECK(chunk.needs_processing);

   if (compression_ == TIFF_COMPRESSION_UNCOMPRESSED) {
      // Needs processing for endianness
      return TIFFPipelineSource::ProcessChunk(chunk, output);
   }

   CHECK_EQ(compression_, TIFF_COMPRESSION_JPEG);

   MemoryDataAccessor data_accessor(chunk.chunk_data_ptr,
                                    chunk.chunk_data_bytes);
   LosslessJPEGDecoder decoder(data_accessor);
   bool validated;
   ASSIGN_OR_RETURN(validated, decoder.ValidateFile(),
                    VLOG(2) << "Failed to validate JPEG data");
   if (!validated) {
      VLOG(2) << "Invalid JPEG data stream";
      return absl::FailedPreconditionError("Invalid JPEG data stream");
   }

   RETURN_IF_ERROR(decoder.Init(), VLOG(2)
                                       << "Failed to initialize JPEG decoder");

   uint16_t height = decoder.GetImageHeight();
   uint16_t width = decoder.GetImageWidth();
   uint8_t components = decoder.GetComponents();
   uint8_t bits_per_sample = decoder.GetBitsPerSample();
   CHECK_EQ(bits_per_sample, bits_per_sample_);

   if (photo_int_ == TIFF_PHOTOMETRIC_INT_CFA) {
      // Encoded tiles are 128x256 with 2 components per pixel.
      //  Output tiles are 256x256 in RGGB CFA configuration
      CHECK_GE(width, static_cast<unsigned>(chunk.processed_width) / 2);
      CHECK_GE(height, static_cast<unsigned>(chunk.processed_height));
      CHECK_EQ(components, 2);

      ImageBuf<uint16_t> decoded_data(height, width, components);
      RETURN_IF_ERROR(decoder.Decode(decoded_data),
                      VLOG(2) << "Failed to decode JPEG image data");

      // Encoded tiles have half the width of output tiles. Each 2 rows in the
      // decoded data get combined to a single row in the output tile.
      CHECK_EQ(output.get_components(), static_cast<unsigned>(components / 2));
      output.copy_tile_in_from(decoded_data, /*local_row*/ 0, /*local_col*/ 0,
                               /*input_row*/ 0, /*input_col*/ 0,
                               /*copy_rows*/ chunk.processed_height,
                               /*copy_cols*/ chunk.processed_width / 2);
   } else {
      CHECK_EQ(photo_int_, TIFF_PHOTOMETRIC_INT_LINEAR_RAW);
      CHECK_GE(width, static_cast<unsigned>(chunk.processed_width));
      CHECK_GE(height, static_cast<unsigned>(chunk.processed_height));
      CHECK_EQ(components, 3);

      // Decode directly into the output
      RETURN_IF_ERROR(decoder.Decode(output),
                      VLOG(2) << "Failed to decode JPEG image data");
   }

   return absl::OkStatus();
}

absl::Status
DNGPipelineSource::MutateMetadata(ImageMeta& image_meta) {
   if (compression_ != TIFF_COMPRESSION_UNCOMPRESSED) {
      image_meta.SetOne<ImageMetaCompression>(
          ImageMetaId::COMPRESSION, ImageMetaCompression::UNCOMPRESSED);
      VLOG(3) << "Set metadata COMPRESSION = Uncompressed";
   }

   if (photo_int_ == TIFF_PHOTOMETRIC_INT_LINEAR_RAW) {
      image_meta.SetOne<ImageMetaPhotoInt>(
          ImageMetaId::PHOTOMETRIC_INTERPRETATION, ImageMetaPhotoInt::RGB);
      VLOG(3) << "Set metadata PHOTOMETRIC_INT = RGB";
   }

   return absl::OkStatus();
}

}  // namespace cmrw
