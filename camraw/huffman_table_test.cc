#include <gtest/gtest.h>

#include "absl/status/status.h"
#include "absl/status/statusor.h"

#include "camraw/huffman_table.h"
#include "camraw/memory_data_accessor.h"

using cmrw::HuffmanTable;
using cmrw::HuffmanTableClass;
using cmrw::MemoryDataAccessor;

TEST(HuffmanTableTest, SimpleTableInitAndDecode) {
   // Create a simple Huffman table with 10 codes
   uint8_t table_data[] = {
       // Table class (DC) and ID (0) - first 4 bits are class, last 4 are ID
       0x00,
       // Number of codes with lengths 1-16 (we'll have codes of lengths 2-5)
       0, 1, 2, 3, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
       // Symbol values in order
       0x01,                   // For length 2
       0x02, 0x03,             // For length 3
       0x04, 0x05, 0x06,       // For length 4
       0x07, 0x08, 0x09, 0x0A  // For length 5
   };

   MemoryDataAccessor accessor(table_data, sizeof(table_data));
   HuffmanTable table(&accessor, 0, sizeof(table_data));

   // Initialize the table
   auto status = table.Init();
   EXPECT_TRUE(status.ok()) << status.message();

   // Verify table properties
   EXPECT_EQ(table.GetTableClass(), HuffmanTableClass::DC);
   EXPECT_EQ(table.GetTableId(), 0);
   EXPECT_EQ(table.GetShortestCodeLen(), 2);
   EXPECT_EQ(table.GetLongestCodeLen(), 5);

   // Test decoding some values
   // For length 2, we should have code 0
   auto symbol_or = table.Decode(2, 0);
   EXPECT_TRUE(symbol_or.ok());
   EXPECT_EQ(*symbol_or, 0x01);

   // For length 3, we should have codes 2, 3
   symbol_or = table.Decode(3, 2);
   EXPECT_TRUE(symbol_or.ok());
   EXPECT_EQ(*symbol_or, 0x02);

   symbol_or = table.Decode(3, 3);
   EXPECT_TRUE(symbol_or.ok());
   EXPECT_EQ(*symbol_or, 0x03);

   // For length 4, we should have codes 8, 9, 10
   symbol_or = table.Decode(4, 8);
   EXPECT_TRUE(symbol_or.ok());
   EXPECT_EQ(*symbol_or, 0x04);

   symbol_or = table.Decode(4, 9);
   EXPECT_TRUE(symbol_or.ok());
   EXPECT_EQ(*symbol_or, 0x05);

   symbol_or = table.Decode(4, 10);
   EXPECT_TRUE(symbol_or.ok());
   EXPECT_EQ(*symbol_or, 0x06);

   // For length 5, we should have codes 22, 23, 24, 25
   symbol_or = table.Decode(5, 22);
   EXPECT_TRUE(symbol_or.ok());
   EXPECT_EQ(*symbol_or, 0x07);

   // Test a code that doesn't exist
   symbol_or = table.Decode(2, 1);
   EXPECT_FALSE(symbol_or.ok());
   EXPECT_EQ(symbol_or.status().code(), absl::StatusCode::kNotFound);
}
