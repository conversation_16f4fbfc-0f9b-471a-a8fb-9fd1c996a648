#include <cassert>

#include "glog/logging.h"

#include "camraw/bit_pump.h"
#include "camraw/colorspace.h"
#include "camraw/endian.h"
#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/lossless_jpeg_decoder.h"
#include "camraw/matrix.h"
#include "camraw/memory_data_accessor.h"
#include "camraw/sony_raw_pipeline_source.h"
#include "camraw/status_macros.h"
#include "camraw/tiff.h"
#include "camraw/tiff_pipeline_source.h"
#include "camraw/tiff_tag_id.h"

namespace cmrw {

absl::Status
SonyRawPipelineSource::Init() {
   RETURN_IF_ERROR(TIFFPipelineSource::Init());
   initialized_ = false;

   if (compression_ == TIFF_COMPRESSION_SONY_ARW) {
      const TIFFTag* sony_tone_curve_tag;
      ASSIGN_TAG_OR_RETURN(sony_tone_curve_tag, TIFFTagId::SONY_TONE_CURVE,
                           TIFF_IFD_TYPE_PRIMARY_IMAGE);
      if (sony_tone_curve_tag->GetDataCount() != 4) {
         VLOG(2) << "Expected 4 sony tone curve values, got ",
             sony_tone_curve_tag->GetDataCount();
         return absl::FailedPreconditionError(
             "Unexpected number of tone curve values");
      }
      ASSIGN_OR_RETURN(sony_tone_curve_,
                       sony_tone_curve_tag->GetAllDataValues<uint16_t>(),
                       VLOG(2) << "Failed to get sony tone curve values");
   }

   initialized_ = true;
   return absl::OkStatus();
}

absl::Status
SonyRawPipelineSource::FetchAndSetMetadata(ImageMetaId id,
                                           ImageMeta& image_meta) {
   switch (id) {
      case ImageMetaId::BLACK_LEVEL: {
         const TIFFTag* sony_black_levels_tag;
         ASSIGN_TAG_OR_RETURN(sony_black_levels_tag,
                              TIFFTagId::SONY_BLACK_LEVEL,
                              TIFF_IFD_TYPE_PRIMARY_IMAGE);

         CHECK_EQ(sony_black_levels_tag->GetDataCount(), 4U);
         std::unique_ptr<uint16_t[]> sony_black_levels;
         ASSIGN_OR_RETURN(sony_black_levels,
                          sony_black_levels_tag->GetAllDataValues<uint16_t>(),
                          VLOG(2) << "Failed to get sony black levels values");
         CHECK_EQ(sony_black_levels[0], sony_black_levels[1]);
         CHECK_EQ(sony_black_levels[0], sony_black_levels[2]);
         CHECK_EQ(sony_black_levels[0], sony_black_levels[3]);
         image_meta.SetOne(ImageMetaId::BLACK_LEVEL, sony_black_levels[0]);
         VLOG(3) << "Fetch metadata BLACK_LEVEL = " << sony_black_levels[0];
      } break;
      case ImageMetaId::WHITE_LEVEL: {
         const TIFFTag* sony_white_levels_tag;
         ASSIGN_TAG_OR_RETURN(sony_white_levels_tag,
                              TIFFTagId::SONY_WHITE_LEVEL,
                              TIFF_IFD_TYPE_SONY_SR2);

         CHECK_EQ(sony_white_levels_tag->GetDataCount(), 3U);
         std::unique_ptr<uint16_t[]> sony_white_levels;
         ASSIGN_OR_RETURN(sony_white_levels,
                          sony_white_levels_tag->GetAllDataValues<uint16_t>(),
                          VLOG(2) << "Failed to get sony white levels values");
         CHECK_EQ(sony_white_levels[0], sony_white_levels[1]);
         CHECK_EQ(sony_white_levels[0], sony_white_levels[2]);
         image_meta.SetOne(ImageMetaId::WHITE_LEVEL, sony_white_levels[0]);
         VLOG(3) << "Fetch metadata WHITE_LEVEL = " << sony_white_levels[0];
      } break;
      case ImageMetaId::WHITE_BALANCE_MULTIPLIERS: {
         const TIFFTag* sony_white_balance_tag;
         ASSIGN_TAG_OR_RETURN(sony_white_balance_tag,
                              TIFFTagId::SONY_WHITE_BALANCE,
                              TIFF_IFD_TYPE_PRIMARY_IMAGE);

         CHECK_EQ(sony_white_balance_tag->GetDataCount(), 4U);
         std::unique_ptr<int16_t[]> sony_white_balance;
         ASSIGN_OR_RETURN(sony_white_balance,
                          sony_white_balance_tag->GetAllDataValues<int16_t>(),
                          VLOG(2) << "Failed to get sony white balance values");

         uint16_t wb_red = sony_white_balance[0];
         // Sony white balance spec is RGGB, we assume G1 will always equal
         // G2 otherwise we would have to treat G1 and G2 pixels separately.
         CHECK_EQ(sony_white_balance[1], sony_white_balance[2]);
         uint16_t wb_green = sony_white_balance[1];
         uint16_t wb_blue = sony_white_balance[3];
         VLOG(3) << "RAW file white balance values: (" << wb_red << ","
                 << wb_green << "," << wb_blue << ")";

         uint16_t min_mult = std::min(wb_red, wb_green);
         min_mult = std::min(min_mult, wb_blue);

         // Compute white balance multipliers - normalize by the smallest
         // value in the raw white balance spec values
         float wb_mult_red = static_cast<float>(wb_red) / min_mult;
         float wb_mult_green = static_cast<float>(wb_green) / min_mult;
         float wb_mult_blue = static_cast<float>(wb_blue) / min_mult;

         std::vector<float> wb_multipliers = {wb_mult_red, wb_mult_green,
                                              wb_mult_blue};
         image_meta.SetMulti(ImageMetaId::WHITE_BALANCE_MULTIPLIERS,
                             wb_multipliers);
         VLOG(3) << "Set metadata WHITE_BALANCE_MULTIPLIERS = ("
                 << wb_multipliers[0] << "," << wb_multipliers[1] << ","
                 << wb_multipliers[2] << ")";
      } break;
      case ImageMetaId::COMPRESSION: {
         switch (compression_) {
            case TIFF_COMPRESSION_UNCOMPRESSED:
               image_meta.SetOne<ImageMetaCompression>(
                   ImageMetaId::COMPRESSION,
                   ImageMetaCompression::UNCOMPRESSED);
               VLOG(3) << "Fetch metadata COMPRESSION = Uncompressed";
               break;
            case TIFF_COMPRESSION_JPEG:
               image_meta.SetOne<ImageMetaCompression>(
                   ImageMetaId::COMPRESSION, ImageMetaCompression::JPEG);
               VLOG(3) << "Fetch metadata COMPRESSION = JPEG";
               break;
            case TIFF_COMPRESSION_SONY_ARW:
               image_meta.SetOne<ImageMetaCompression>(
                   ImageMetaId::COMPRESSION, ImageMetaCompression::PROPRIETARY);
               VLOG(3) << "Fetch metadata COMPRESSION = Sony ARW";
               break;
            default:
               LOG(FATAL) << "Unknown compression type.";
               break;
         }
      } break;
      case ImageMetaId::CAMERA_COLOR_MATRIX: {
         const TIFFIFD* first_ifd;
         ASSIGN_OR_RETURN(first_ifd, file_parser_->GetIFDAtIndex(0),
                          VLOG(2) << "Failed to retrieve first IFD");
         const TIFFTag* model_tag;
         ASSIGN_OR_RETURN(model_tag, first_ifd->GetTag(TIFFTagId::MODEL),
                          VLOG(2) << "Failed to get camera model tag");
         std::string model = model_tag->DataToString();
         /*
          * We use hardcoded forwarding matrices because the ColorMatrix tag
          * present in the SR2 IFD is not really a color conversion matrix
          * for the camera sensor -> XYZ. The following values were recovered
          * from DNG files converted using Adobe DNG converter.
          */
         std::vector<float> color_matrix(9);
         // clang-format off
            if (model == "ILCE-1") {
               color_matrix = {0.510000,0.333100,0.121300,
                               0.253900,0.711500,0.034500,
                               0.101000,0.000100,0.724100};
            } else if (model == "ILCE-7RM4") {
               color_matrix = {0.506300,0.342200,0.115900,
                               0.261400,0.704900,0.033700,
                               0.109600,0.001100,0.714400};
            } else if (model == "ILCE-7RM5") {
               color_matrix = {0.461600,0.377100,0.125600,
                               0.207900,0.756200,0.035900,
                               0.079800,0.001100,0.744200};
            // clang-format on
         } else {
            VLOG(2) << "Can't fetch camera color matrix "
                    << "for unsupported camera: " << model;
            return absl::UnimplementedError("Unsupported camera model.");
         }

         image_meta.SetMulti(ImageMetaId::CAMERA_COLOR_MATRIX, color_matrix);
         VLOG(3) << "Set metadata CAMERA_COLOR_MATRIX = [[" << color_matrix[0]
                 << "," << color_matrix[1] << "," << color_matrix[2] << "],["
                 << color_matrix[3] << "," << color_matrix[4] << ","
                 << color_matrix[5] << "],[" << color_matrix[6] << ","
                 << color_matrix[7] << "," << color_matrix[8] << "]]";
      } break;
      default: {
         return TIFFPipelineSource::FetchAndSetMetadata(id, image_meta);
      }
   }
   return absl::OkStatus();
}

absl::StatusOr<TIFFPipelineSource::ChunkConfiguration>
SonyRawPipelineSource::GetChunkConfiguration() {
   if (photo_int_ == TIFF_PHOTOMETRIC_INT_CFA) {
      if (compression_ == TIFF_COMPRESSION_UNCOMPRESSED) {
         return ChunkConfiguration{
             .allowed_data_layouts =
                 {ChunkConfiguration::DataLayout::SINGLE_STRIP},
             .data_type = "uncompressed Sony RAW",
             .expected_strip_size =
                 image_width_ * image_height_ * sizeof(uint16_t),
             .needs_processing = false,
             .sub_chunk_byte_alignment = sizeof(uint16_t),
         };
      } else if (compression_ == TIFF_COMPRESSION_SONY_ARW) {
         return ChunkConfiguration{
             .allowed_data_layouts =
                 {ChunkConfiguration::DataLayout::SINGLE_STRIP},
             .data_type = "compressed Sony RAW",
             .expected_strip_size =
                 static_cast<size_t>(image_width_ * image_height_),
             .needs_processing = true,
             .sub_chunk_byte_alignment = 32,  // 16 bytes from each color
         };
      } else if (compression_ == TIFF_COMPRESSION_JPEG) {
         return ChunkConfiguration{
             .allowed_data_layouts = {ChunkConfiguration::DataLayout::TILES},
             .data_type = "lossless compressed Sony RAW",
             .expected_strip_size = 0,
             .needs_processing = true,
             .sub_chunk_byte_alignment = 0,  // chunk is indivisible
         };
      }
   } else if (photo_int_ == TIFF_PHOTOMETRIC_INT_YCBCR) {
      if (compression_ == TIFF_COMPRESSION_JPEG) {
         return ChunkConfiguration{
             .allowed_data_layouts = {ChunkConfiguration::DataLayout::TILES},
             .data_type = "lossless compressed Sony linear RAW",
             .expected_strip_size = 0,
             .needs_processing = true,
             .sub_chunk_byte_alignment = 0,  // chunk is indivisible
         };
      }
   }

   VLOG(2) << "Unsupported Sony RAW file configuration photo_int: "
           << photo_int_ << " compression: " << compression_;
   return absl::UnimplementedError("Unsupported Sony RAW file configuration");
}

// XXX: This code is probably wrong if we ever encounter a big endian
//      Sony RAW file.
absl::Status
SonyRawPipelineSource::ProcessChunk(const PipelineSourceChunk& chunk,
                                    ImageBuf<uint16_t>& output) {
   CHECK(chunk.needs_processing);

   if (!initialized_) {
      return absl::FailedPreconditionError("Pipeline source not initialized");
   }

   VLOG(5) << "Chunk data bytes: " << chunk.chunk_data_bytes;

   if (compression_ == TIFF_COMPRESSION_SONY_ARW) {
      assert(chunk.processed_width % 16 == 0);
      assert(chunk.processed_width * chunk.processed_height ==
             chunk.chunk_data_bytes);

      /**
       * The tone curve specifies how the bits_per_sample_ bit input
       * values were compressed down to 11 bits used to encode the
       * delta values. Each interval in the tone curve has half
       * the precision of the previous interval. The input value
       * can be recovered by applying linear interpolation from the
       * 11 bit value using the step multiplier based on which
       * interval of the tone curve the value is in.
       */
      CHECK_GT(bits_per_sample_, 11);
      uint8_t shift = bits_per_sample_ - 11;

      // Preparing tone curve - first value 0 and last value is
      // 2^bits_per_sample_-1. The middle 4 values come from the RAW
      // file
      uint16_t tone_curve[6] = {
          0, 0, 0, 0, 0, static_cast<uint16_t>(pow(2, bits_per_sample_) - 1)};
      for (int t = 0; t < 4; t++) {
         CHECK_NE(sony_tone_curve_[t], 0);
         tone_curve[t + 1] = sony_tone_curve_[t];
      }

      // The camera specifies the tone curve in bits_per_sample_ bit
      // space so we have to shift it down to 11 bits to match the
      // values we are decoding
      uint16_t shifted_curve[6];
      for (int i = 0; i < 6; i++) {
         shifted_curve[i] = tone_curve[i] >> shift;
      }

      MemoryDataAccessor data_accessor(chunk.chunk_data_ptr,
                                       chunk.chunk_data_bytes);
      BitPump bit_pump(&data_accessor, 0, ENDIANNESS_LITTLE);

      for (uint32_t row = 0; row < chunk.processed_height; row++) {
         for (uint32_t col_block = 0; col_block < chunk.processed_width / 16;
              col_block++) {
            /**
             * Delta decoding takes in 16 bytes at a time and decodes that
             * to 16 pixel values. Each 16 byte block is structured as
             * follows:
             *
             * /----------------\
             * | max_val   : 11 |
             * | min_val   : 11 |
             * | max_offset: 4  |
             * | min_offset: 4  |
             * | delta1    : 7  |
             * | delta2    : 7  |
             * |  ....          |
             * | delta14   : 7  |
             * \----------------/
             *
             * The max and min values are stored directly as full 11 bit values.
             * The offset of the max and min values among the 16 output pixels
             * are stored as 4 bit values each. The remaining 14 pixels are
             * represented in ascending order as 7 bit deltas against the min
             * value.
             */
            uint16_t out_block[16];

            uint16_t max_val, min_val;
            ASSIGN_OR_RETURN(max_val, bit_pump.GetBits(11));
            ASSIGN_OR_RETURN(min_val, bit_pump.GetBits(11));
            assert(max_val >= min_val);
            uint16_t span = max_val - min_val;
            /**
             * Here span is the distance between the min and max value of
             * the block. For the smallest span (<128) delta values
             * correspond directly to the difference between the encoded
             * value and the minimum value. However for larger spans, the
             * precision is halved at each interval. The step value
             * defines the multiplier against the delta value.
             */
            uint16_t step;
            if (span < 128) {
               step = 1;
            } else if (span < 256) {
               step = 2;
            } else if (span < 512) {
               step = 4;
            } else if (span < 1024) {
               step = 8;
            } else {
               step = 16;
            }

            // Delta decoding into out_block
            uint16_t max_offset, min_offset;
            ASSIGN_OR_RETURN(max_offset, bit_pump.GetBits(4));
            ASSIGN_OR_RETURN(min_offset, bit_pump.GetBits(4));
            for (int out_idx = 0; out_idx < 16; out_idx++) {
               if (out_idx == min_offset) {
                  out_block[out_idx] = min_val;
               } else if (out_idx == max_offset) {
                  out_block[out_idx] = max_val;
               } else {
                  /**
                   *  Output value is min value + (delta * step)
                   */
                  uint16_t delta;
                  ASSIGN_OR_RETURN(delta, bit_pump.GetBits(7));
                  out_block[out_idx] = min_val + (delta * step);
               }
            }

            for (int o = 0; o < 16; o++) {
               uint16_t orig_val = out_block[o];
               // base_val is the max value of the previous tone curve
               // interval in bits_per_sample_ bit space
               uint16_t base_val = 0;
               uint16_t step = 2;

               for (int t = 1; t < 6; t++) {
                  if (orig_val <= shifted_curve[t]) {  // found the interval
                     out_block[o] =
                         base_val + ((orig_val - shifted_curve[t - 1]) * step);
                     break;
                  }
                  base_val +=
                      ((shifted_curve[t] - shifted_curve[t - 1]) * step);
                  step *= 2;  // step doubles for each interval
               }
            }
            /**
             * For writing out_block to decoded_raw_data we have to consider
             * that delta encoding operates on same-color pixels in the
             * CFA. For odd rows we have RGRGRGRG and for even rows we have
             * GBGBGBGB. The 16 byte out_block encodes 16 pixel values
             * for the same color (ex. RRRRRR or GGGGGG or BBBBBB) so we
             * have to spread the out_block values to every other pixel in
             * decoded_raw_data.
             */
            // Fancy math to find which column to start writing in
            // decoded_raw_data
            uint32_t col_offset = ((col_block / 2) * 32) + (col_block % 2);
            uint16_t out_block_idx = 0;
            for (uint32_t out_col = col_offset; out_col < col_offset + 32;
                 out_col += 2) {
               output(row, out_col) = out_block[out_block_idx];
               out_block_idx++;
            }
         }
      }

      return absl::OkStatus();
   } else if (compression_ == TIFF_COMPRESSION_JPEG) {
      MemoryDataAccessor data_accessor(chunk.chunk_data_ptr,
                                       chunk.chunk_data_bytes);

      LosslessJPEGDecoder decoder(data_accessor);

      bool validated;
      ASSIGN_OR_RETURN(validated, decoder.ValidateFile(),
                       VLOG(2) << "Failed to validate JPEG data");
      if (!validated) {
         VLOG(2) << "Failed to validate JPEG data";
         return absl::FailedPreconditionError("Failed to validate JPEG data");
      }

      RETURN_IF_ERROR(decoder.Init(),
                      VLOG(2) << "Failed to initialize JPEG decoder");

      uint16_t height = decoder.GetImageHeight();
      uint16_t width = decoder.GetImageWidth();
      uint8_t components = decoder.GetComponents();
      uint8_t bits_per_sample = decoder.GetBitsPerSample();

      if (photo_int_ == TIFF_PHOTOMETRIC_INT_CFA) {
         CHECK_EQ(bits_per_sample, bits_per_sample_);
         // Encoded tiles are 256x256 with 4 components per pixel.
         // Output tiles are 512x512 in RGGB CFA configuration
         CHECK_EQ(width, static_cast<unsigned>(chunk.processed_width) / 2);
         CHECK_EQ(height, static_cast<unsigned>(chunk.processed_height) / 2);
         CHECK_EQ(components, 4);

         ImageBuf<uint16_t> decoded_data(height, width, components);
         RETURN_IF_ERROR(decoder.Decode(decoded_data),
                         VLOG(2) << "Failed to decode JPEG image data");

         // Copy from decoded_data to output. The 4 components in each
         // decoded_data pixel corresponds to a 2x2 CFA block in the
         // output image.
         for (int drow = 0; drow < height; drow++) {
            for (int dcol = 0; dcol < width; dcol++) {
               output(drow * 2, dcol * 2) = decoded_data(drow, dcol, 0);
               output(drow * 2, (dcol * 2) + 1) = decoded_data(drow, dcol, 1);
               output((drow * 2) + 1, dcol * 2) = decoded_data(drow, dcol, 2);
               output((drow * 2) + 1, (dcol * 2) + 1) =
                   decoded_data(drow, dcol, 3);
            }
         }
      } else {
         CHECK_EQ(photo_int_, TIFF_PHOTOMETRIC_INT_YCBCR);
         CHECK_EQ(width, static_cast<unsigned>(chunk.processed_width));
         CHECK_EQ(height, static_cast<unsigned>(chunk.processed_height));
         CHECK_EQ(components, 3);

         // Directly decode the data into the output
         RETURN_IF_ERROR(decoder.DecodeSonyYUV(output),
                         VLOG(2) << "Failed to decode JPEG image data");

         ChromaSubsamplingMode subsampling_mode =
             decoder.GetChromaSubsamplingMode();

         if (subsampling_mode == ChromaSubsamplingMode::_4_2_2 ||
             subsampling_mode == ChromaSubsamplingMode::_4_2_0) {
            // Horizontal interpolation of Cb and Cr across adjacent columns:
            // input:  y1, Cb, Cr | y2, Cb,         Cr         | y3, Cb_, Cr_
            // output: y1, Cb, Cr | y2, (Cb+Cb_)/2, (Cr+Cr_)/2 | y3, Cb_, Cr_
            // column iteration is until processed_width-2 because there's no
            // more pixel to interpolate from for the last two columns.
            for (uint32_t orow = 0; orow < chunk.processed_height; orow++) {
               for (uint32_t ocol = 0; ocol < chunk.processed_width - 2;
                    ocol += 2) {
                  uint16_t Cb = output(orow, ocol, 1);
                  uint16_t Cb_ = output(orow, ocol + 2, 1);
                  uint16_t Cr = output(orow, ocol, 2);
                  uint16_t Cr_ = output(orow, ocol + 2, 2);

                  output(orow, ocol + 1, 1) = static_cast<uint16_t>(
                      (static_cast<uint32_t>(Cb) + Cb_) / 2);
                  output(orow, ocol + 1, 2) = static_cast<uint16_t>(
                      (static_cast<uint32_t>(Cr) + Cr_) / 2);
               }
            }
         }

         if (subsampling_mode == ChromaSubsamplingMode::_4_2_0) {
            // Vertical interpolation of Cb and Cr across adjacent rows:
            // input:   y1, Cb1,            Cr1
            //          y2, Cb1,            Cr1
            //          y3, Cb2,            Cr2
            // output:  y1, Cb1,            Cr1
            //          y2, (Cb1+Cb2)/2,    (Cr1+Cr2)/2
            //          y3, Cb2,            Cr2
            // row iteration is until processed_height-2 because there's no more
            // pixel to interpolate from for the last two rows.
            for (uint32_t orow = 0; orow < chunk.processed_height - 2;
                 orow += 2) {
               for (uint32_t ocol = 0; ocol < chunk.processed_width; ocol++) {
                  uint16_t Cb1 = output(orow, ocol, 1);
                  uint16_t Cr1 = output(orow, ocol, 2);
                  uint16_t Cb2 = output(orow + 2, ocol, 1);
                  uint16_t Cr2 = output(orow + 2, ocol, 2);

                  output(orow + 1, ocol, 1) = static_cast<uint16_t>(
                      (static_cast<uint32_t>(Cb1) + Cb2) / 2);
                  output(orow + 1, ocol, 2) = static_cast<uint16_t>(
                      (static_cast<uint32_t>(Cr1) + Cr2) / 2);
               }
            }
         }

         // Convert every output pixel from YCbCr to camera RGB color space
         for (uint32_t orow = 0; orow < chunk.processed_height; orow++) {
            for (uint32_t ocol = 0; ocol < chunk.processed_width; ocol++) {
               float Y = output(orow, ocol, 0);
               // Bring Cb and Cr back to being centered around 0
               float Cb = output(orow, ocol, 1) - 16383;
               float Cr = output(orow, ocol, 2) - 16383;

               Matrix<float> input =
                   Matrix<float>(/*rows=*/3, /*cols=*/1, {Y, Cb, Cr});
               Matrix<float> rgb_values =
                   COLORSPACE_YCBCR_TO_RGB_MATRIX.Multiply(input);
               // Clamp output values to unsigned 16 bit integers.
               output(orow, ocol, 0) = static_cast<uint16_t>(
                   std::max(std::min(rgb_values(0, 0), 65535.0f), 0.0f));
               output(orow, ocol, 1) = static_cast<uint16_t>(
                   std::max(std::min(rgb_values(1, 0), 65535.0f), 0.0f));
               output(orow, ocol, 2) = static_cast<uint16_t>(
                   std::max(std::min(rgb_values(2, 0), 65535.0f), 0.0f));
            }
         }
      }

      return absl::OkStatus();
   }

   return absl::UnimplementedError("Unknown Sony RAW compression type");
}

absl::Status
SonyRawPipelineSource::MutateMetadata(ImageMeta& image_meta) {
   if (compression_ != TIFF_COMPRESSION_UNCOMPRESSED) {
      image_meta.SetOne<ImageMetaCompression>(
          ImageMetaId::COMPRESSION, ImageMetaCompression::UNCOMPRESSED);
      VLOG(3) << "Set metadata COMPRESSION = Uncompressed";
   }

   if (photo_int_ == TIFF_PHOTOMETRIC_INT_YCBCR) {
      image_meta.SetOne<ImageMetaPhotoInt>(
          ImageMetaId::PHOTOMETRIC_INTERPRETATION, ImageMetaPhotoInt::RGB);
      VLOG(3) << "Set metadata PHOTOMETRIC_INT = RGB";
   }
   return absl::OkStatus();
}

}  // namespace cmrw
