#ifndef ENDIAN_H_
#define ENDIAN_H_

#include <cstdint>
#include <type_traits>

namespace cmrw {

#if defined(__GNUC__) || defined(__clang__)

using Endianness = uint16_t;
constexpr Endianness ENDIANNESS_LITTLE = 0;
constexpr Endianness ENDIANNESS_BIG = 1;

#if defined(__BYTE_ORDER__) && defined(__ORDER_LITTLE_ENDIAN__) && \
    defined(__ORDER_BIG_ENDIAN__)
#if __BYTE_ORDER__ == __ORDER_LITTLE_ENDIAN__
#define ARCH_IS_LITTLE_ENDIAN 1
#define ARCH_IS_BIG_ENDIAN 0
#elif __BYTE_ORDER__ == __ORDER_BIG_ENDIAN__
#define ARCH_IS_LITTLE_ENDIAN 0
#define ARCH_IS_BIG_ENDIAN 1
#else
#error "Unknown byte order"
#endif
#else
#error "Missing byte order definitions"
#endif

template <typename T>
inline typename std::enable_if<std::is_integral<T>::value, T>::type
byte_swap(T val) {
   if constexpr (sizeof(T) == 1) {
      return val;
   } else if constexpr (sizeof(T) == 2) {
      return __builtin_bswap16(val);
   } else if constexpr (sizeof(T) == 4) {
      return __builtin_bswap32(val);
   } else if constexpr (sizeof(T) == 8) {
      return __builtin_bswap64(val);
   }
}

template <typename T>
typename std::enable_if<std::is_integral<T>::value, T>::type
little_endian(T val) {
#if ARCH_IS_LITTLE_ENDIAN
   return val;
#else
   return byte_swap<T>(val);
#endif
}

template <typename T>
typename std::enable_if<std::is_integral<T>::value, T>::type
big_endian(T val) {
#if ARCH_IS_LITTLE_ENDIAN
   return byte_swap<T>(val);
#else
   return val;
#endif
}

#if ARCH_IS_LITTLE_ENDIAN
constexpr bool
SAME_ENDIANNESS_AS_ARCH(Endianness e) {
   return e == ENDIANNESS_LITTLE;
}
constexpr bool
NOT_SAME_ENDIANNESS_AS_ARCH(Endianness e) {
   return e != ENDIANNESS_LITTLE;
}
#else
constexpr bool
SAME_ENDIANNESS_AS_ARCH(Endianness e) {
   return e == ENDIANNESS_BIG;
}
constexpr bool
NOT_SAME_ENDIANNESS_AS_ARCH(Endianness e) {
   return e != ENDIANNESS_BIG;
}
#endif

#else
#error "Non GCC/Clang compilers not supported"
#endif /* __GNUC__ || __clang__ */

}  // namespace cmrw

#endif /* ENDIAN_H_ */
