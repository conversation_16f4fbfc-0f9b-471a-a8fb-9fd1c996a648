#ifndef DNG_PIPELINE_SOURCE_H_
#define DNG_PIPELINE_SOURCE_H_

#include "absl/status/status.h"
#include "absl/status/statusor.h"

#include "camraw/dng_file_parser.h"
#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/tiff.h"
#include "camraw/tiff_pipeline_source.h"

namespace cmrw {

class DNGPipelineSource : public TIFFPipelineSource {
   public:
      DNGPipelineSource(DNGFileParser* file_parser)
          : TIFFPipelineSource(file_parser, "DNGPipelineSource") {
         supported_compression_types.insert(TIFF_COMPRESSION_JPEG);
      }
      absl::Status Init() override;
      absl::Status ProcessChunk(const PipelineSourceChunk& chunk,
                                ImageBuf<uint16_t>& output) override;
      absl::Status MutateMetadata(ImageMeta& image_meta) override;

   protected:
      absl::Status FetchAndSetMetadata(ImageMetaId id,
                                       ImageMeta& image_meta) override;
      absl::StatusOr<ChunkConfiguration> GetChunkConfiguration() override;

   private:
      bool wb_not_needed_ = false;
};

}  // namespace cmrw

#endif /* DNG_PIPELINE_SOURCE_H_ */
