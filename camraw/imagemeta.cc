#include "glog/logging.h"

#include "camraw/imagemeta.h"

namespace cmrw {

bool
ImageMeta::IsSet(ImageMetaId id) const {
   CHECK_LT(static_cast<int>(id), static_cast<int>(ImageMetaId::MAX));
   return map_.find(id) != map_.end();
}

void
ImageMeta::Clear(ImageMetaId id) {
   CHECK_LT(static_cast<int>(id), static_cast<int>(ImageMetaId::MAX));
   auto it = map_.find(id);
   if (it != map_.end()) {
      map_.erase(it);
   }
}

}  // namespace cmrw
