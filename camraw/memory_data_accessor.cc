#include "absl/status/status.h"
#include "absl/strings/str_cat.h"

#include "camraw/memory_data_accessor.h"

namespace cmrw {

absl::Status
MemoryDataAccessor::CopyBytes(uint32_t read_offset, uint32_t num_bytes,
                              void* dst) {
   if (read_offset - relocation_offset_ >= size_) {
      return absl::OutOfRangeError(absl::StrCat(
          "Offset ", read_offset, " is out of range of memory buffer (size: ",
          size_, " reloc: ", relocation_offset_, ")"));
   }

   if (read_offset - relocation_offset_ + num_bytes > size_) {
      return absl::OutOfRangeError(absl::StrCat(
          "Requested range exceeds ", "memory buffer. Offset: ", read_offset,
          " num bytes: ", num_bytes, " buffer size: ", size_,
          " reloc: ", relocation_offset_));
   }

   memcpy(dst, src_ + read_offset - relocation_offset_, num_bytes);

   return absl::OkStatus();
}

}  // namespace cmrw
