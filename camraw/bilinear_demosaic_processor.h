#ifndef BILINEAR_DEMOSAIC_PROCESSOR_H_
#define BILINEAR_DEMOSAIC_PROCESSOR_H_

#include "absl/status/status.h"

#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/pipeline_processor.h"

namespace cmrw {

class BilinearDemosaicProcessor : public PipelineProcessor {
   public:
      BilinearDemosaicProcessor()
          : PipelineProcessor("BilinearDemosaicProcessor",
                              /*input_spec*/
                              {.one_sample_per_pixel = true,
                               .three_samples_per_pixel = false},
                              /*output_spec*/
                              {.one_sample_per_pixel = false,
                               .three_samples_per_pixel = true},
                              /*required metadata*/
                              {
                                  ImageMetaId::PHOTOMETRIC_INTERPRETATION,
                                  ImageMetaId::CFA_PATTERN_DIM,
                                  ImageMetaId::CFA_PATTERN,
                                  ImageMetaId::SAMPLES_PER_PIXEL,
                              },
                              /*optional metadata*/ {}) {}
      absl::Status Init(const ImageMeta& image_meta) override;
      void LogStart() override;
      absl::Status Run(const ImageBuf<uint16_t>& input_buffer,
                       ImageBuf<uint16_t>& output_buffer) override;
      void LogStats() override {};
      absl::Status MutateMetadata(ImageMeta& image_meta) override;

   private:
      enum class CFAFilterColor { RED, GREEN1, GREEN2, BLUE };
};

}  // namespace cmrw

#endif /* BILINEAR_DEMOSAIC_PROCESSOR_H_ */
