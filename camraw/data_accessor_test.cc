#include <fstream>

#include "absl/status/status.h"
#include "gtest/gtest.h"

#include "camraw/file_data_accessor.h"
#include "camraw/memory_data_accessor.h"

using cmrw::FileDataAccessor;
using cmrw::MemoryDataAccessor;

uint8_t test_data[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16};

TEST(DataAccessorTest, MemoryAccessorRead) {
   uint8_t target[16];
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   auto status = mda.CopyBytes(0, 16, &target);
   EXPECT_TRUE(status.ok());

   for (int i = 0; i < 16; i++) {
      EXPECT_EQ(target[i], i + 1);
   }
}

TEST(DataAccessorTest, MemoryAccessorOutOfBounds) {
   uint8_t target;
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   auto status = mda.CopyBytes(16, 1, &target);
   EXPECT_FALSE(status.ok());
   EXPECT_TRUE(absl::IsOutOfRange(status));
}

TEST(DataAccessorTest, MemoryAccessorTooMuchData) {
   uint32_t target;
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data));
   auto status = mda.CopyBytes(13, 4, &target);
   EXPECT_FALSE(status.ok());
   EXPECT_TRUE(absl::IsOutOfRange(status));
}

TEST(DataAccessorTest, MemoryAccessorRelocation) {
   uint8_t target[16];
   MemoryDataAccessor mda(reinterpret_cast<uint8_t*>(&test_data),
                          sizeof(test_data), /*relocation_offset*/ 3000);
   auto status = mda.CopyBytes(3000, 16, &target);
   EXPECT_TRUE(status.ok());

   for (int i = 0; i < 16; i++) {
      EXPECT_EQ(target[i], i + 1);
   }
}

TEST(DataAccessorTest, FileDataAccessorRead) {
   uint8_t target[16];
   std::fstream test_file("camraw/test_data/file_accessor_data.bin",
                          std::ios::in | std::ios::binary);
   FileDataAccessor fda(test_file);
   auto status = fda.CopyBytes(0, 16, &target);
   EXPECT_TRUE(status.ok());

   for (int i = 0; i < 16; i++) {
      EXPECT_EQ(target[i], i);
   }
}

TEST(DataAccessorTest, FileDataAccessorOutOfRange) {
   uint8_t target[16];
   std::fstream test_file("parseraw/test_data/file_accessor_data.bin",
                          std::ios::in | std::ios::binary);
   FileDataAccessor fda(test_file);
   auto status = fda.CopyBytes(16, 1, &target);
   EXPECT_FALSE(status.ok());
   EXPECT_TRUE(absl::IsOutOfRange(status));
}

TEST(DataAccessorTest, FileDataAccessorTooMuchData) {
   uint8_t target[16];
   std::fstream test_file("parseraw/test_data/file_accessor_data.bin",
                          std::ios::in | std::ios::binary);
   FileDataAccessor fda(test_file);
   auto status = fda.CopyBytes(13, 4, &target);
   EXPECT_FALSE(status.ok());
   EXPECT_TRUE(absl::IsOutOfRange(status));
}
