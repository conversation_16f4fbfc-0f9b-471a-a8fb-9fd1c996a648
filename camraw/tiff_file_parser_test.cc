#include <memory>
#include <vector>

#include "absl/status/statusor.h"
#include "gtest/gtest.h"

#include "camraw/memory_data_accessor.h"
#include "camraw/tiff.h"
#include "camraw/tiff_file_parser.h"
#include "camraw/tiff_ifd.h"
#include "camraw/tiff_tag.h"
#include "camraw/tiff_tag_id.h"

using cmrw::MemoryDataAccessor;
using cmrw::TIFF_HEADER_ORDER_LITTLE_ENDIAN;
using cmrw::TIFF_HEADER_VERSION;
using cmrw::TIFF_IFD_TYPE_PRIMARY_IMAGE;
using cmrw::TIFF_IFD_TYPE_REDUCED_RES_IMAGE;
using cmrw::TIFFFileParser;
using cmrw::TIFFHeader;
using cmrw::TIFFIFD;
using cmrw::TIFFTag;
using cmrw::TIFFTagDataType;
using cmrw::TIFFTagId;
using cmrw::TIFFTagWireFormat;

using IFDSpec = std::vector<TIFFTagWireFormat>;
struct IFDTree {
      IFDSpec ifd;
      std::vector<IFDSpec> sub_ifds;
};

size_t
ComputeRequiredSpace(std::vector<IFDTree> ifd_trees) {
   size_t num_ifds = 0;  // includes subifds
   size_t num_tags = 0;
   size_t num_subifds = 0;  // for knowing how many subifd tag values to write
   for (auto& ifd_tree : ifd_trees) {
      // The base IFD of the IFD tree
      num_ifds++;
      num_tags += ifd_tree.ifd.size();
      // Each sub IFD
      for (auto& sub_ifd : ifd_tree.sub_ifds) {
         num_subifds++;
         num_ifds++;
         num_tags += sub_ifd.size();
      }
   }

   return sizeof(TIFFHeader) +
          (num_ifds * (/*num_tags*/ sizeof(uint16_t) +
                       /* next_ifd_offset*/ sizeof(uint32_t))) +
          (num_tags * sizeof(TIFFTagWireFormat)) +
          (num_subifds * sizeof(uint32_t));
}

std::pair<std::unique_ptr<uint8_t[]>, size_t>
MakeTIFFFile(std::vector<IFDTree> ifd_trees) {
   size_t required_space = ComputeRequiredSpace(ifd_trees);
   auto result = std::make_unique<uint8_t[]>(required_space);
   size_t write_offset = 0;

   TIFFHeader header{
       TIFF_HEADER_ORDER_LITTLE_ENDIAN, TIFF_HEADER_VERSION,
       static_cast<uint32_t>((ifd_trees.size() == 0) ? 0 : sizeof(TIFFHeader))};
   memcpy(result.get(), &header, sizeof(TIFFHeader));
   write_offset += sizeof(TIFFHeader);

   size_t ifd_idx = 0,
          last_ifd_idx = ifd_trees.size() == 1 ? 0 : ifd_trees.size() - 1;
   for (auto& ifd_tree : ifd_trees) {
      uint16_t num_tags = ifd_tree.ifd.size();
      size_t num_subifds = ifd_tree.sub_ifds.size();
      size_t ifd_start_write_offset = write_offset;

      memcpy(result.get() + write_offset, &num_tags, sizeof(uint16_t));
      write_offset += sizeof(num_tags);

      for (auto& tag_data : ifd_tree.ifd) {
         // XXX: support non-offset tag data
         //  Substitute data offset for the SUBIFD tag
         if (tag_data.id == static_cast<uint16_t>(TIFFTagId::SUB_IFDS) &&
             num_subifds > 0) {
            tag_data.data_offset = ifd_start_write_offset + sizeof(uint16_t) +
                                   (num_tags * sizeof(TIFFTagWireFormat)) +
                                   sizeof(uint32_t);
         }
         memcpy(result.get() + write_offset, &tag_data,
                sizeof(TIFFTagWireFormat));
         write_offset += sizeof(TIFFTagWireFormat);
      }

      size_t next_ifd_offset_offset = write_offset;
      write_offset += sizeof(uint32_t);

      size_t subifd_offset_offset = write_offset;
      write_offset += num_subifds * sizeof(uint32_t);

      // Write SubIFD
      for (auto& sub_ifd : ifd_tree.sub_ifds) {
         memcpy(result.get() + subifd_offset_offset, &write_offset,
                sizeof(uint32_t));
         subifd_offset_offset += sizeof(uint32_t);

         uint16_t num_subifd_tags = sub_ifd.size();

         memcpy(result.get() + write_offset, &num_subifd_tags,
                sizeof(uint16_t));
         write_offset += sizeof(num_subifd_tags);

         for (auto& subifd_tag_data : sub_ifd) {
            memcpy(result.get() + write_offset, &subifd_tag_data,
                   sizeof(TIFFTagWireFormat));
            write_offset += sizeof(TIFFTagWireFormat);
         }

         uint32_t next_sub_ifd_offset = 0;
         memcpy(result.get() + write_offset, &next_sub_ifd_offset,
                sizeof(uint32_t));
         write_offset += sizeof(uint32_t);
      }

      uint32_t next_ifd_offset = ifd_idx == last_ifd_idx ? 0 : write_offset;
      memcpy(result.get() + next_ifd_offset_offset, &next_ifd_offset,
             sizeof(uint32_t));

      ifd_idx++;
   }

   return std::make_pair(std::move(result), required_space);
}

TEST(TIFFFileParserTest, ValidateFailNotTIFFFile) {
   uint8_t garbage_data[] = {1, 2, 3, 4, 5, 6, 7, 8, 9};
   MemoryDataAccessor accessor(garbage_data, 9);
   TIFFFileParser parser(accessor);
   auto validate_or = parser.ValidateFile();
   EXPECT_TRUE(validate_or.ok());
   EXPECT_FALSE(*validate_or);
}

TEST(TIFFFileParserTest, ValidateFailNoTagsOnFirstIFD) {
   auto&& [file_data, file_size] = MakeTIFFFile({
       {},
   });
   MemoryDataAccessor accessor(file_data.get(), file_size);
   TIFFFileParser parser(accessor);
   auto validate_or = parser.ValidateFile();
   EXPECT_TRUE(validate_or.ok());
   EXPECT_FALSE(*validate_or);
}

TEST(TIFFFileParserTest, ValidateFailNoIFDs) {
   auto&& [file_data, file_size] = MakeTIFFFile({});
   MemoryDataAccessor accessor(file_data.get(), file_size);
   TIFFFileParser parser(accessor);
   auto validate_or = parser.ValidateFile();
   EXPECT_TRUE(validate_or.ok());
   EXPECT_FALSE(*validate_or);
}

TEST(TIFFFileParserTest, ValidateSimpleFile) {
   auto&& [file_data, file_size] =
       MakeTIFFFile({{.ifd = {
                          TIFFTagWireFormat{TIFFTagId::NEW_SUBFILE_TYPE,
                                            TIFFTagDataType::LONG, 1, 0},
                      }}});
   MemoryDataAccessor accessor(file_data.get(), file_size);
   TIFFFileParser parser(accessor);
   auto validate_or = parser.ValidateFile();
   EXPECT_TRUE(validate_or.ok());
   EXPECT_TRUE(*validate_or);
}

TEST(TIFFFileParserTest, ParseSimpleFile) {
   auto&& [file_data, file_size] =
       MakeTIFFFile({{.ifd = {
                          TIFFTagWireFormat{TIFFTagId::NEW_SUBFILE_TYPE,
                                            TIFFTagDataType::LONG, 1, 0},
                      }}});
   MemoryDataAccessor accessor(file_data.get(), file_size);
   TIFFFileParser parser(accessor);
   auto init_status = parser.Init();
   EXPECT_TRUE(init_status.ok());

   auto tag_or = parser.GetTagFromIFD(TIFFTagId::NEW_SUBFILE_TYPE,
                                      TIFF_IFD_TYPE_PRIMARY_IMAGE);
   EXPECT_TRUE(tag_or.ok());

   const TIFFTag* tag = *tag_or;
   EXPECT_EQ(tag->GetId(), TIFFTagId::NEW_SUBFILE_TYPE);
   EXPECT_EQ(tag->GetDataType(), TIFFTagDataType::LONG);
   EXPECT_EQ(tag->GetDataCount(), 1);
   auto val_or = tag->GetDataValue<uint32_t>();
   EXPECT_TRUE(val_or.ok());
   EXPECT_EQ(*val_or, 0);
}

TEST(TIFFFileParserTest, ParseMultiIFDFile) {
   auto&& [file_data, file_size] = MakeTIFFFile({
       {.ifd =
            {
                TIFFTagWireFormat{TIFFTagId::NEW_SUBFILE_TYPE,
                                  TIFFTagDataType::LONG, 1, 0},
                TIFFTagWireFormat{TIFFTagId::COMPRESSION, TIFFTagDataType::LONG,
                                  1, 42},
                TIFFTagWireFormat{TIFFTagId::PLANAR_CONFIG,
                                  TIFFTagDataType::LONG, 1, 0},
            }},
       {.ifd =
            {
                TIFFTagWireFormat{TIFFTagId::NEW_SUBFILE_TYPE,
                                  TIFFTagDataType::LONG, 1, 1},
                TIFFTagWireFormat{TIFFTagId::COMPRESSION, TIFFTagDataType::LONG,
                                  1, 84},
                TIFFTagWireFormat{TIFFTagId::PLANAR_CONFIG,
                                  TIFFTagDataType::LONG, 1, 0},
            }},
   });
   MemoryDataAccessor accessor(file_data.get(), file_size);
   TIFFFileParser parser(accessor);
   auto init_status = parser.Init();
   EXPECT_TRUE(init_status.ok());

   auto tag_or = parser.GetTagFromIFD(TIFFTagId::COMPRESSION,
                                      TIFF_IFD_TYPE_PRIMARY_IMAGE);
   EXPECT_TRUE(tag_or.ok());

   const TIFFTag* tag = *tag_or;
   EXPECT_EQ(tag->GetId(), TIFFTagId::COMPRESSION);
   EXPECT_EQ(tag->GetDataType(), TIFFTagDataType::LONG);
   EXPECT_EQ(tag->GetDataCount(), 1);
   auto val_or = tag->GetDataValue<uint32_t>();
   EXPECT_TRUE(val_or.ok());
   EXPECT_EQ(*val_or, 42);

   tag_or = parser.GetTagFromIFD(TIFFTagId::COMPRESSION,
                                 TIFF_IFD_TYPE_REDUCED_RES_IMAGE);
   EXPECT_TRUE(tag_or.ok());

   tag = *tag_or;
   EXPECT_EQ(tag->GetId(), TIFFTagId::COMPRESSION);
   EXPECT_EQ(tag->GetDataType(), TIFFTagDataType::LONG);
   EXPECT_EQ(tag->GetDataCount(), 1);
   val_or = tag->GetDataValue<uint32_t>();
   EXPECT_TRUE(val_or.ok());
   EXPECT_EQ(*val_or, 84);
}

TEST(TIFFFileParserTest, ParseSubIFDFile) {
   auto&& [file_data, file_size] = MakeTIFFFile({
       {
           .ifd =
               {
                   TIFFTagWireFormat{TIFFTagId::NEW_SUBFILE_TYPE,
                                     TIFFTagDataType::LONG, 1, 1},
                   TIFFTagWireFormat{TIFFTagId::COMPRESSION,
                                     TIFFTagDataType::LONG, 1, 42},
                   TIFFTagWireFormat{TIFFTagId::SUB_IFDS, TIFFTagDataType::LONG,
                                     2, 0},
                   TIFFTagWireFormat{TIFFTagId::PLANAR_CONFIG,
                                     TIFFTagDataType::LONG, 1, 0},
               },
           .sub_ifds =
               {
                   {
                       TIFFTagWireFormat{TIFFTagId::NEW_SUBFILE_TYPE,
                                         TIFFTagDataType::LONG, 1, 1},
                       TIFFTagWireFormat{TIFFTagId::COMPRESSION,
                                         TIFFTagDataType::LONG, 1, 84},
                       TIFFTagWireFormat{TIFFTagId::PLANAR_CONFIG,
                                         TIFFTagDataType::LONG, 1, 0},
                   },
                   {
                       TIFFTagWireFormat{TIFFTagId::NEW_SUBFILE_TYPE,
                                         TIFFTagDataType::LONG, 1, 0},
                       TIFFTagWireFormat{TIFFTagId::COMPRESSION,
                                         TIFFTagDataType::LONG, 1, 126},
                       TIFFTagWireFormat{TIFFTagId::PLANAR_CONFIG,
                                         TIFFTagDataType::LONG, 1, 0},
                   },
               },
       },
       {.ifd =
            {
                TIFFTagWireFormat{TIFFTagId::NEW_SUBFILE_TYPE,
                                  TIFFTagDataType::LONG, 1, 1},
                TIFFTagWireFormat{TIFFTagId::COMPRESSION, TIFFTagDataType::LONG,
                                  1, 168},
                TIFFTagWireFormat{TIFFTagId::PLANAR_CONFIG,
                                  TIFFTagDataType::LONG, 1, 0},
            }},
   });
   MemoryDataAccessor accessor(file_data.get(), file_size);
   TIFFFileParser parser(accessor);
   auto init_status = parser.Init();
   EXPECT_TRUE(init_status.ok());

   auto tag_or = parser.GetTagFromIFD(TIFFTagId::COMPRESSION,
                                      TIFF_IFD_TYPE_PRIMARY_IMAGE);
   EXPECT_TRUE(tag_or.ok());

   const TIFFTag* tag = *tag_or;
   EXPECT_EQ(tag->GetId(), TIFFTagId::COMPRESSION);
   EXPECT_EQ(tag->GetDataType(), TIFFTagDataType::LONG);
   EXPECT_EQ(tag->GetDataCount(), 1);
   auto val_or = tag->GetDataValue<uint32_t>();
   EXPECT_TRUE(val_or.ok());
   EXPECT_EQ(*val_or, 126);

   tag_or = parser.GetTagFromIFD(TIFFTagId::COMPRESSION,
                                 TIFF_IFD_TYPE_REDUCED_RES_IMAGE, 0);
   EXPECT_TRUE(tag_or.ok());

   tag = *tag_or;
   EXPECT_EQ(tag->GetId(), TIFFTagId::COMPRESSION);
   EXPECT_EQ(tag->GetDataType(), TIFFTagDataType::LONG);
   EXPECT_EQ(tag->GetDataCount(), 1);
   val_or = tag->GetDataValue<uint32_t>();
   EXPECT_TRUE(val_or.ok());
   EXPECT_EQ(*val_or, 42);

   tag_or = parser.GetTagFromIFD(TIFFTagId::COMPRESSION,
                                 TIFF_IFD_TYPE_REDUCED_RES_IMAGE, 1);
   EXPECT_TRUE(tag_or.ok());

   tag = *tag_or;
   EXPECT_EQ(tag->GetId(), TIFFTagId::COMPRESSION);
   EXPECT_EQ(tag->GetDataType(), TIFFTagDataType::LONG);
   EXPECT_EQ(tag->GetDataCount(), 1);
   val_or = tag->GetDataValue<uint32_t>();
   EXPECT_TRUE(val_or.ok());
   EXPECT_EQ(*val_or, 168);

   tag_or = parser.GetTagFromIFD(TIFFTagId::COMPRESSION,
                                 TIFF_IFD_TYPE_REDUCED_RES_IMAGE, 2);
   EXPECT_TRUE(tag_or.ok());

   tag = *tag_or;
   EXPECT_EQ(tag->GetId(), TIFFTagId::COMPRESSION);
   EXPECT_EQ(tag->GetDataType(), TIFFTagDataType::LONG);
   EXPECT_EQ(tag->GetDataCount(), 1);
   val_or = tag->GetDataValue<uint32_t>();
   EXPECT_TRUE(val_or.ok());
   EXPECT_EQ(*val_or, 84);
}

TEST(TIFFFileParserTest, GetAllIFDsOfType) {
   auto&& [file_data, file_size] = MakeTIFFFile({
       {.ifd =
            {
                TIFFTagWireFormat{TIFFTagId::NEW_SUBFILE_TYPE,
                                  TIFFTagDataType::LONG, 1, 0},
            }},
       {.ifd =
            {
                TIFFTagWireFormat{TIFFTagId::NEW_SUBFILE_TYPE,
                                  TIFFTagDataType::LONG, 1, 1},
            }},
       {.ifd =
            {
                TIFFTagWireFormat{TIFFTagId::NEW_SUBFILE_TYPE,
                                  TIFFTagDataType::LONG, 1, 1},
            }},
       {.ifd =
            {
                TIFFTagWireFormat{TIFFTagId::NEW_SUBFILE_TYPE,
                                  TIFFTagDataType::LONG, 1, 1},
            }},
   });
   MemoryDataAccessor accessor(file_data.get(), file_size);
   TIFFFileParser parser(accessor);
   auto init_status = parser.Init();
   EXPECT_TRUE(init_status.ok());

   auto ifds_or = parser.GetAllIFDsOfType(TIFF_IFD_TYPE_REDUCED_RES_IMAGE);
   EXPECT_TRUE(ifds_or.ok());
   std::vector<const TIFFIFD*> ifds = *ifds_or;
   EXPECT_EQ(ifds.size(), 3);
}
