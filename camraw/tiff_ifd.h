#ifndef TIFF_IFD_H_
#define TIFF_IFD_H_

#include <list>
#include <unordered_set>
#include <vector>

#include "absl/container/flat_hash_map.h"
#include "absl/status/status.h"
#include "absl/status/statusor.h"

#include "camraw/endian.h"
#include "camraw/tiff_tag.h"

namespace cmrw {

struct TIFFIFDScanResult {
      uint16_t num_ifd_tags;
      uint32_t next_ifd_offset;
      std::list<TIFFTag> tags;
};

class TIFFIFD {
   public:
      TIFFIFD(uint32_t ifd_offset, DataAccessor* data_accessor,
              Endianness endianness = ENDIANNESS_LITTLE)
          : ifd_offset_(ifd_offset),
            data_accessor_(data_accessor),
            endianness_(endianness) {};

      bool IsParsed() const { return is_parsed_; }
      uint32_t GetIFDOffset() const { return ifd_offset_; }
      uint32_t GetNextIFDOffset() const { return next_ifd_offset_; }
      uint16_t GetNumTags() const { return num_tags_; }
      DataAccessor* GetDataAccessor() const { return data_accessor_; }

      absl::Status Parse();
      absl::StatusOr<std::list<const TIFFTag*>> Search(
          const std::unordered_set<TIFFTagId>& search_mask) const;
      absl::StatusOr<TIFFIFDScanResult> Scan(
          const std::unordered_set<TIFFTagId>* search_mask);
      absl::StatusOr<const TIFFTag*> GetTag(TIFFTagId tag_id) const;

      std::vector<TIFFTag>::const_iterator begin() const {
         return tags_.begin();
      };
      std::vector<TIFFTag>::const_iterator end() const { return tags_.end(); };

   private:
      bool is_parsed_ = false;
      uint32_t ifd_offset_ = 0;
      uint32_t next_ifd_offset_ = 0;
      uint16_t num_tags_ = 0;
      DataAccessor* data_accessor_;
      Endianness endianness_;

      // hash map from tag id -> index into vector
      absl::flat_hash_map<TIFFTagId, size_t> tag_id_to_idx_map_;
      std::vector<TIFFTag> tags_;
};

}  // namespace cmrw

#endif  // TIFF_IFD_H_
