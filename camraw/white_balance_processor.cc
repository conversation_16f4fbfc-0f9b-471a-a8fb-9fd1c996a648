#include "glog/logging.h"

#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/limits.h"
#include "camraw/status_macros.h"
#include "camraw/white_balance_processor.h"

namespace cmrw {

absl::Status
WhiteBalanceProcessor::Init(const ImageMeta& image_meta) {
   std::vector<float> wb_mults;
   ASSIGN_OR_RETURN(
       wb_mults,
       image_meta.GetMulti<float>(ImageMetaId::WHITE_BALANCE_MULTIPLIERS),
       VLOG(2) << "Failed to get white balance multiplier metadata");

   wb_mult_red_ = wb_mults[0];
   wb_mult_green_ = wb_mults[1];
   wb_mult_blue_ = wb_mults[2];

   return absl::OkStatus();
}

void
WhiteBalanceProcessor::LogStart() {
   LOG(INFO) << "Applying white balance multipliers (" << wb_mult_red_ << ","
             << wb_mult_green_ << "," << wb_mult_blue_ << ")";
}

absl::Status
WhiteBalanceProcessor::Run(const ImageBuf<uint16_t>& input_buffer,
                           ImageBuf<uint16_t>& output_buffer) {
   CHECK_EQ(input_buffer.get_height(), output_buffer.get_height());
   CHECK_EQ(input_buffer.get_width(), output_buffer.get_width());
   CHECK_EQ(output_buffer.get_components(), input_buffer.get_components());

   uint32_t clipped_red = 0, clipped_green = 0, clipped_blue = 0;

   if (input_buffer.get_components() == 3) {
      for (uint32_t row = 0; row < input_buffer.get_height(); row++) {
         for (uint32_t col = 0; col < input_buffer.get_width(); col++) {
            uint16_t orig_red = input_buffer(row, col, 0);
            uint16_t orig_green = input_buffer(row, col, 1);
            uint16_t orig_blue = input_buffer(row, col, 2);

            uint32_t orig_luminance = orig_red + orig_green + orig_blue;

            // Step 1 - multiply original RGB values with the WB multipliers
            uint32_t new_red = static_cast<uint32_t>(orig_red * wb_mult_red_);
            uint32_t new_green =
                static_cast<uint32_t>(orig_green * wb_mult_green_);
            uint32_t new_blue =
                static_cast<uint32_t>(orig_blue * wb_mult_blue_);

            // Calculate the luminance multiplier to bring the total luminance
            // of this pixel back to its original value
            uint32_t new_luminance = new_red + new_green + new_blue;
            float luminance_mult =
                static_cast<float>(orig_luminance) / new_luminance;

            // Step 2 - scale the RGB values by the luminance multiplier
            new_red = static_cast<uint32_t>(new_red * luminance_mult);
            new_green = static_cast<uint32_t>(new_green * luminance_mult);
            new_blue = static_cast<uint32_t>(new_blue * luminance_mult);

            // Handle values that are clipped as a result of the WB adjustment
            if (new_red > MAX_UINT16) {
               clipped_red++;
               new_red = MAX_UINT16;
            }
            if (new_green > MAX_UINT16) {
               clipped_green++;
               new_green = MAX_UINT16;
            }
            if (new_blue > MAX_UINT16) {
               clipped_blue++;
               new_blue = MAX_UINT16;
            }

            output_buffer(row, col, 0) = static_cast<uint16_t>(new_red);
            output_buffer(row, col, 1) = static_cast<uint16_t>(new_green);
            output_buffer(row, col, 2) = static_cast<uint16_t>(new_blue);
         }
      }
   } else {  // single component per pixel, CFA

      enum class CFAFilterColor { RED, GREEN1, GREEN2, BLUE };

      for (uint32_t row = 0; row < input_buffer.get_height(); row++) {
         for (uint32_t col = 0; col < input_buffer.get_width(); col++) {
            // Determine the CFA color of the current pixel
            CFAFilterColor cur_pixel_color;
            if (row % 2 == 0) {     // odd row (starts from 0)
               if (col % 2 == 0) {  // odd col (starts from 0)
                  cur_pixel_color = CFAFilterColor::RED;
               } else {
                  cur_pixel_color = CFAFilterColor::GREEN1;
               }
            } else {
               if (col % 2 == 0) {  // odd col (starts from 0)
                  cur_pixel_color = CFAFilterColor::GREEN2;
               } else {
                  cur_pixel_color = CFAFilterColor::BLUE;
               }
            }

            uint16_t orig_val = input_buffer(row, col);
            uint16_t new_val = 0;

            switch (cur_pixel_color) {
               case CFAFilterColor::RED: {
                  new_val = static_cast<uint32_t>(orig_val * wb_mult_red_);
                  if (new_val > MAX_UINT16) {
                     clipped_red++;
                     new_val = MAX_UINT16;
                  }
               } break;
               case CFAFilterColor::GREEN1:
               case CFAFilterColor::GREEN2: {
                  new_val = static_cast<uint32_t>(orig_val * wb_mult_green_);
                  if (new_val > MAX_UINT16) {
                     clipped_green++;
                     new_val = MAX_UINT16;
                  }
               } break;
               case CFAFilterColor::BLUE: {
                  new_val = static_cast<uint32_t>(orig_val * wb_mult_blue_);
                  if (new_val > MAX_UINT16) {
                     clipped_blue++;
                     new_val = MAX_UINT16;
                  }
               } break;
            }

            output_buffer(row, col) = static_cast<uint16_t>(new_val);
         }
      }
   }

   CommitStats([&] {
      clipped_red_ += clipped_red;
      clipped_green_ += clipped_green;
      clipped_blue += clipped_blue;
   });

   return absl::OkStatus();
}

void
WhiteBalanceProcessor::LogStats() {
   LOG(INFO) << "White balance clipped pixels - Red: " << clipped_red_
             << " Green: " << clipped_green_ << " Blue: " << clipped_blue_;
}

absl::Status
WhiteBalanceProcessor::MutateMetadata(ImageMeta& image_meta) {
   std::vector<float> wb_mults = {1.0, 1.0, 1.0};

   image_meta.SetMulti(ImageMetaId::WHITE_BALANCE_MULTIPLIERS, wb_mults);
   VLOG(3) << "Set metadata WHITE_BALANCE_MULTIPLIERS = (1.0,1.0,1.0)";

   return absl::OkStatus();
}

}  // namespace cmrw
