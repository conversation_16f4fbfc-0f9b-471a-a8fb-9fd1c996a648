#ifndef SONY_RAW_FILE_PARSER_H_
#define SONY_RAW_FILE_PARSER_H_

#include "camraw/data_accessor.h"
#include "camraw/memory_data_accessor.h"
#include "camraw/tiff_file_parser.h"
#include "camraw/tiff_tag_id.h"

namespace cmrw {

const TIFFIFDType TIFF_IFD_TYPE_DNG_PRIVATE_DATA = TIFF_IFD_TYPE_BASE_MAX + 1;
const TIFFIFDType TIFF_IFD_TYPE_SONY_SR2 = TIFF_IFD_TYPE_BASE_MAX + 2;

class SonyRawFileParser : public TIFFFileParser {
   public:
      SonyRawFileParser(DataAccessor& data_accessor)
          : TIFFFileParser(data_accessor) {
         // Add DNG private data to the list of tags that discover new IFDs
         TIFFNewIFDTagIDs.insert(TIFFTagId::DNG_PRIVATE_DATA);
      }

      absl::StatusOr<bool> ValidateFile() override;
      absl::Status Init() override;

      absl::StatusOr<const TIFFIFD*> GetIFDAtIndex(size_t idx) override;

   protected:
      TIFFIFDType GetIFDTypeFromTag(const TIFFTag& tag) override;

   private:
      static absl::StatusOr<std::unique_ptr<uint32_t[]>> decrypt_sr2(
          DataAccessor& accessor, uint32_t sr2_offset, uint32_t sr2_length,
          uint32_t sr2_key);

      bool sr2_decrypted_ = false;
      std::unique_ptr<uint32_t[]> decrypted_sr2_data_ = nullptr;
      std::unique_ptr<MemoryDataAccessor> sr2_data_accessor_ = nullptr;
};

}  // namespace cmrw

#endif /* SONY_RAW_FILE_PARSER_H_ */
