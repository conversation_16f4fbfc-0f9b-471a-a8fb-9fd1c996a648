#include <cassert>
#include <cstdint>

#include "absl/status/status.h"
#include "absl/status/statusor.h"
#include "glog/logging.h"

#include "camraw/bit_pump.h"
#include "camraw/status_macros.h"

namespace cmrw {

absl::Status
BitPump::CopyNextByte() {
   uint8_t next_byte;
   RETURN_IF_ERROR(data_accessor_->CopyBytes(next_byte_offset_, 1, &next_byte),
                   VLOG(5) << "Failed to read next byte");
   next_byte_offset_++;

   cur_byte_ = next_byte;
   cur_bits_left_ = 8;

   return absl::OkStatus();
}

absl::StatusOr<uint8_t>
BitPump::PeekNextByte() {
   uint8_t next_byte;
   RETURN_IF_ERROR(data_accessor_->CopyBytes(next_byte_offset_, 1, &next_byte));
   return next_byte;
}

absl::StatusOr<uint16_t>
BitPump::GetBits(size_t num_bits) {
   assert(num_bits <= 16);

   uint16_t output = 0;
   size_t total_bits_left = num_bits;

   while (total_bits_left > 0) {
      if (cur_bits_left_ > 0) {
         size_t bits_to_copy = (cur_bits_left_ > total_bits_left)
                                   ? total_bits_left
                                   : cur_bits_left_;
         if (data_endianness_ == ENDIANNESS_BIG) {
            // Construct a mask for the bit positions that we want to keep
            uint8_t mask = ~(0xFF << bits_to_copy);
            // Right shift to isolate the bits we want to keep and apply the
            // mask to clear out more significant bits that aren't wanted.
            uint8_t bits =
                (cur_byte_ >> (cur_bits_left_ - bits_to_copy)) & mask;
            // Shift output and OR with the kept bits to append them.
            output <<= bits_to_copy;
            output |= bits;
         } else {  // Little endian data ordering
            // Construct a mask for the bit positions that we want to keep
            uint8_t mask = 0xFF >> (8 - bits_to_copy);
            // Isolate the bits we want to keep
            uint8_t bits = (cur_byte_ >> (8 - cur_bits_left_)) & mask;
            // Shift bits and prepend to output
            output |= static_cast<uint16_t>(bits)
                      << (num_bits - total_bits_left);
         }
         // Update counters
         cur_bits_left_ -= bits_to_copy;
         total_bits_left -= bits_to_copy;
      } else {
         // Replace cur_byte_ with the data from the next byte
         RETURN_IF_ERROR(CopyNextByte());
      }
   }

   return output;
}

}  // namespace cmrw
