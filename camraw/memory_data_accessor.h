#ifndef MEMORY_DATA_ACCESSOR_H_
#define MEMORY_DATA_ACCESSOR_H_

#include "absl/status/status.h"

#include "camraw/data_accessor.h"

namespace cmrw {

class MemoryDataAccessor : public DataAccessor {
   public:
      MemoryDataAccessor(uint8_t* src, size_t size,
                         size_t relocation_offset = 0)
          : src_(src), size_(size), relocation_offset_(relocation_offset) {}
      absl::Status CopyBytes(uint32_t read_offset, uint32_t num_bytes,
                             void* dst) override;

   private:
      uint8_t* src_;
      size_t size_;
      size_t relocation_offset_;
};

}  // namespace cmrw

#endif  // MEMORY_DATA_ACCESSOR_H_
