#ifndef SCALE_DATA_PROCESSOR_H_
#define SCALE_DATA_PROCESSOR_H_

#include "absl/status/status.h"

#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/pipeline_processor.h"

namespace cmrw {

class ScaleDataProcessor : public PipelineProcessor {
   public:
      ScaleDataProcessor()
          : PipelineProcessor(
                "ScaleDataProcessor",
                /*input_spec*/
                {.one_sample_per_pixel = true, .three_samples_per_pixel = true},
                /*output_spec*/
                {.one_sample_per_pixel = true, .three_samples_per_pixel = true},
                /*required metadata*/
                {
                    ImageMetaId::BLACK_LEVEL,
                    ImageMetaId::WHITE_LEVEL,
                    ImageMetaId::BITS_PER_SAMPLE,
                    ImageMetaId::SAMPLES_PER_PIXEL,
                },
                /*optional metadata*/ {}),
            black_underrun_(0),
            white_overrun_(0) {}
      absl::Status Init(const ImageMeta& image_meta) override;
      void LogStart() override;
      absl::Status Run(const ImageBuf<uint16_t>& input_buffer,
                       ImageBuf<uint16_t>& output_buffer) override;
      void LogStats() override;
      absl::Status MutateMetadata(ImageMeta& image_meta) override;

   private:
      uint16_t black_level_;
      uint16_t white_level_;
      uint16_t samples_per_pixel_;
      uint32_t black_underrun_;
      uint32_t white_overrun_;
};

}  // namespace cmrw

#endif /* SCALE_DATA_PROCESSOR_H_ */
