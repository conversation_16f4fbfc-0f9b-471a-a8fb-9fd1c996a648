#ifndef _LOSSLESS_JPEG_DECODER_H_
#define _LOSSLESS_JPEG_DECODER_H_

#include <memory>
#include <vector>

#include "absl/status/status.h"
#include "absl/status/statusor.h"

#include "camraw/file_parser.h"
#include "camraw/huffman_table.h"
#include "camraw/imagebuf.h"

namespace cmrw {

enum class ChromaSubsamplingMode : uint16_t {
   _4_4_4 = 0,
   _4_2_2 = 1,
   _4_2_0 = 2,
};

class LosslessJPEGDecoder : public FileParser {
   public:
      LosslessJPEGDecoder(DataAccessor& data_accessor)
          : FileParser(data_accessor) {}
      absl::StatusOr<bool> ValidateFile() override;
      absl::Status Init() override;
      uint16_t GetImageHeight() { return image_height_; }
      uint16_t GetImageWidth() { return image_width_; }
      uint8_t GetComponents() { return components_; }
      uint8_t GetBitsPerSample() { return bits_per_sample_; }
      ChromaSubsamplingMode GetChromaSubsamplingMode() {
         return chroma_subsampling_mode_;
      }
      absl::Status Decode(ImageBuf<uint16_t>& output);
      absl::Status DecodeSonyYUV(ImageBuf<uint16_t>& output);

   private:
      absl::Status ParseStartOfFrame(uint32_t sof_offset, uint16_t segment_len);
      absl::StatusOr<std::unique_ptr<HuffmanTable>> ParseHuffmanTable(
          uint32_t table_offset, uint32_t bytes_available);
      absl::Status ParseScanHeader(uint32_t scan_offset, uint16_t segment_len);

      struct ComponentInfo {
            uint8_t component_id;
            uint8_t horizontal_sampling;
            uint8_t vertical_sampling;
            uint8_t quant_table_id;
      };

      bool sof_parsed_ = false;
      bool initialized_ = false;
      bool validated_ = false;

      uint8_t bits_per_sample_ = 0;
      uint16_t image_height_ = 0;
      uint16_t image_width_ = 0;
      uint8_t components_ = 0;
      std::vector<ComponentInfo> component_infos_;
      ChromaSubsamplingMode chroma_subsampling_mode_ =
          ChromaSubsamplingMode::_4_4_4;
      /*
       * The spec allows for 4 AC/DC tables each, but some broken encoders
       * use ids 0..3 instead of 1..4 so we just make size these arrays at 5
       * elements so we can just index directly into them regardless of the
       * encoder behavior.
       */
      std::unique_ptr<HuffmanTable> ac_huff_tables[5];
      std::unique_ptr<HuffmanTable> dc_huff_tables[5];

      struct ScanComponent {
            uint8_t component_id;
            uint8_t dc_table_id;
            uint8_t ac_table_id;
      };
      std::vector<ScanComponent> scan_components_;

      uint8_t predictor_ = 0;
      uint8_t point_transform_ = 0;
      bool scan_parsed_ = false;
      uint32_t image_data_offset_ = 0;
      uint16_t restart_interval_ = 0;
};

}  // namespace cmrw

#endif
