#include <cassert>
#include <memory>
#include <vector>

#include "absl/status/status.h"
#include "absl/status/statusor.h"
#include "absl/strings/str_format.h"
#include "glog/logging.h"

#include "camraw/endian.h"
#include "camraw/huffman_table.h"
#include "camraw/jpeg.h"
#include "camraw/jpeg_bit_pump.h"
#include "camraw/lossless_jpeg_decoder.h"
#include "camraw/status_macros.h"

namespace cmrw {

absl::StatusOr<bool>
LosslessJPEGDecoder::ValidateFile() {
   VLOG(4) << "Validating JPEG file.";
   uint16_t hdr_bytes;
   RETURN_IF_ERROR(data_accessor_->CopyBytes(
                       /*offset*/ 0, /*len*/ sizeof(uint16_t), &hdr_bytes),
                   VLOG(4) << "Failed to copy data.");

   if (big_endian<uint16_t>(hdr_bytes) != JPEG_FRAME_HEADER) {
      VLOG(4) << "No JPEG frame header detected.";
      return absl::OkStatus();
   }

   VLOG(4) << "JPEG file validated.";
   validated_ = true;
   return true;
}

absl::Status
LosslessJPEGDecoder::ParseStartOfFrame(uint32_t sof_offset,
                                       uint16_t segment_len) {
   uint32_t data_offset = sof_offset + 4;  // skip marker and length
   RETURN_IF_ERROR(
       data_accessor_->CopyBytes(data_offset, sizeof(bits_per_sample_),
                                 &bits_per_sample_),
       VLOG(4) << "Failed to copy bits_per_sample data.");
   data_offset += sizeof(bits_per_sample_);
   VLOG(5) << "Bits per sample: " << absl::StrFormat("%d", bits_per_sample_);

   RETURN_IF_ERROR(data_accessor_->CopyBytes(data_offset, sizeof(image_height_),
                                             &image_height_),
                   VLOG(4) << "Failed to copy image height data.");
   image_height_ = big_endian<uint16_t>(image_height_);
   data_offset += sizeof(image_height_);
   VLOG(5) << "Image height: " << image_height_;

   RETURN_IF_ERROR(data_accessor_->CopyBytes(data_offset, sizeof(image_width_),
                                             &image_width_),
                   VLOG(4) << "Failed to copy image width data.");
   image_width_ = big_endian<uint16_t>(image_width_);
   data_offset += sizeof(image_width_);
   VLOG(5) << "Image width: " << image_width_;

   RETURN_IF_ERROR(data_accessor_->CopyBytes(data_offset, sizeof(components_),
                                             &components_),
                   VLOG(4) << "Failed to copy components data.");
   data_offset += sizeof(components_);
   VLOG(5) << "Components: " << absl::StrFormat("%d", components_);

   CHECK_EQ(segment_len - (data_offset - sof_offset - 2),
            static_cast<unsigned>(components_ * 3));
   component_infos_.resize(components_);
   for (int comp_idx = 0; comp_idx < components_; comp_idx++) {
      uint8_t comp_info[3];
      RETURN_IF_ERROR(data_accessor_->CopyBytes(data_offset, 3, &comp_info),
                      VLOG(4) << "Failed to copy component info data.");
      data_offset += 3;

      VLOG(5) << "Component id: " << absl::StrFormat("%d", comp_info[0])
              << " horiz sampling: " << absl::StrFormat("%d", comp_info[1] >> 4)
              << " vert sampling: "
              << absl::StrFormat("%d", comp_info[1] & 0x0F)
              << " quant table id: " << absl::StrFormat("%d", comp_info[2]);

      component_infos_[comp_idx] = {
          .component_id = comp_info[0],
          .horizontal_sampling = static_cast<uint8_t>(comp_info[1] >> 4),
          .vertical_sampling = static_cast<uint8_t>(comp_info[1] & 0x0F),
          .quant_table_id = comp_info[2],
      };
   }

   // Make sure we read the entire header
   CHECK_EQ(data_offset, sof_offset + 2 + segment_len);

   sof_parsed_ = true;
   return absl::OkStatus();
}

absl::StatusOr<std::unique_ptr<HuffmanTable>>
LosslessJPEGDecoder::ParseHuffmanTable(uint32_t table_offset,
                                       uint32_t bytes_available) {
   auto table = std::make_unique<HuffmanTable>(data_accessor_, table_offset,
                                               bytes_available);
   RETURN_IF_ERROR(table->Init(), VLOG(4)
                                      << "Failed to initialize Huffman table");

   return std::move(table);
}

absl::Status
LosslessJPEGDecoder::Init() {
   if (initialized_) {
      return absl::OkStatus();
   }

   VLOG(4) << "Initializing Lossless JPEG decoder.";

   if (!validated_) {
      bool validated;
      ASSIGN_OR_RETURN(validated, ValidateFile());
      if (!validated) {
         return absl::FailedPreconditionError("Not a valid JPEG file.");
      }
   }
   assert(validated_);

   uint32_t data_offset = 2;  // header bytes
   bool scan_found = false;
   while (!scan_found) {
      uint16_t marker;
      uint32_t marker_offset = data_offset;
      RETURN_IF_ERROR(
          data_accessor_->CopyBytes(data_offset, sizeof(marker), &marker),
          VLOG(4) << "Failed to copy marker data.");
      marker = big_endian<uint16_t>(marker);
      data_offset += sizeof(marker);

      uint16_t segment_len;
      RETURN_IF_ERROR(data_accessor_->CopyBytes(
                          data_offset, sizeof(segment_len), &segment_len),
                      VLOG(4) << "Failed to copy segment length data.");
      segment_len = big_endian<uint16_t>(segment_len);

      switch (marker) {
         case JPEG_MARKER_SOF_HUFF_LOSSLESS: {
            VLOG(4) << "[marker] offset:" << marker_offset
                    << " Start of Frame (lossless non-diff Huffman)";
            RETURN_IF_ERROR(ParseStartOfFrame(marker_offset, segment_len),
                            VLOG(4) << "Failed to parse Start of Frame");
         } break;
         case JPEG_MARKER_DEFINE_HUFF_TABLE: {
            VLOG(4) << "[marker] offset:" << marker_offset
                    << " Define Huffman Table";
            uint32_t table_offset = data_offset + sizeof(segment_len);
            uint32_t bytes_available = segment_len - sizeof(segment_len);
            while (bytes_available > 0) {
               std::unique_ptr<HuffmanTable> table;
               ASSIGN_OR_RETURN(
                   table, ParseHuffmanTable(table_offset, bytes_available),
                   VLOG(4) << "Failed to parse Define Huffman Table");

               bytes_available -= table->GetAccessorBytesUsed();
               table_offset += table->GetAccessorBytesUsed();

               uint8_t table_id = table->GetTableId();
               if (table->GetTableClass() == HuffmanTableClass::DC) {
                  dc_huff_tables[table_id] = std::move(table);
               } else {
                  ac_huff_tables[table_id] = std::move(table);
               }
            }
         } break;
         case JPEG_MARKER_START_OF_SCAN:
            VLOG(4) << "[marker] offset:" << marker_offset << " Start of Scan";
            scan_found = true;
            RETURN_IF_ERROR(ParseScanHeader(marker_offset, segment_len),
                            VLOG(4) << "Failed to parse Start of Frame");
            break;
         case JPEG_MARKER_DEFINE_RESTART_INTERVAL:
            VLOG(4) << "[marker] offset:" << marker_offset
                    << "  Define Restart Interval";
            uint16_t restart_interval;
            RETURN_IF_ERROR(data_accessor_->CopyBytes(
                data_offset + 2, sizeof(uint16_t), &restart_interval));
            restart_interval_ = big_endian<uint16_t>(restart_interval);
            VLOG(5) << "Restart interval: " << restart_interval_;
            break;
         case JPEG_MARKER_APP0:
         case JPEG_MARKER_APP1:
         case JPEG_MARKER_APP2:
         case JPEG_MARKER_APP3:
         case JPEG_MARKER_APP4:
         case JPEG_MARKER_APP5:
         case JPEG_MARKER_APP6:
         case JPEG_MARKER_APP7:
         case JPEG_MARKER_APP8:
         case JPEG_MARKER_APP9:
         case JPEG_MARKER_APP10:
         case JPEG_MARKER_APP11:
         case JPEG_MARKER_APP12:
         case JPEG_MARKER_APP13:
         case JPEG_MARKER_APP14:
         case JPEG_MARKER_APP15:
            // Ignore application markers
            VLOG(4) << absl::StrFormat(
                "[marker] offset:%d Application marker:0x%x (IGNORED)",
                marker_offset, marker);
            break;
         default:
            VLOG(4) << absl::StrFormat(
                "Invalid frame marker at offset: %d in JPEG data: 0x%x",
                marker_offset, marker);
            return absl::FailedPreconditionError(
                "Invalid frame marker in data");
      }

      data_offset += segment_len;
   }

   if (!sof_parsed_) {
      VLOG(4) << "No SoF header found";
      return absl::FailedPreconditionError("No SoF header found");
   }

   // Determine if there's chroma subsampling
   if (components_ == 3) {
      // Assume component order is Y U V / Y Cb Cr
      uint8_t y_horiz_sampling = component_infos_[0].horizontal_sampling;
      uint8_t u_horiz_sampling = component_infos_[1].horizontal_sampling;
      uint8_t v_horiz_sampling = component_infos_[2].horizontal_sampling;
      uint8_t y_vert_sampling = component_infos_[0].vertical_sampling;
      uint8_t u_vert_sampling = component_infos_[1].vertical_sampling;
      uint8_t v_vert_sampling = component_infos_[2].vertical_sampling;

      if ((y_horiz_sampling == u_horiz_sampling) &&
          (y_horiz_sampling == v_horiz_sampling) &&
          (y_vert_sampling == u_vert_sampling) &&
          (y_vert_sampling == v_vert_sampling)) {
         chroma_subsampling_mode_ = ChromaSubsamplingMode::_4_4_4;
         VLOG(5) << "Chroma subsampling mode is 4:4:4";
      } else if ((y_horiz_sampling == 2 * u_horiz_sampling) &&
                 (y_horiz_sampling == 2 * v_horiz_sampling) &&
                 (y_vert_sampling == u_vert_sampling) &&
                 (y_vert_sampling == v_vert_sampling)) {
         chroma_subsampling_mode_ = ChromaSubsamplingMode::_4_2_2;
         VLOG(5) << "Chroma subsampling mode is 4:2:2";
      } else if ((y_horiz_sampling == 2 * u_horiz_sampling) &&
                 (y_horiz_sampling == 2 * v_horiz_sampling) &&
                 (y_vert_sampling == 2 * u_vert_sampling) &&
                 (y_vert_sampling == 2 * v_vert_sampling)) {
         chroma_subsampling_mode_ = ChromaSubsamplingMode::_4_2_0;
         VLOG(5) << "Chroma subsampling mode is 4:2:0";
      } else {
         VLOG(5) << "Chroma subsampling mode unknown.";
      }
   }

   if (!scan_parsed_) {
      VLOG(4) << "No scan header found";
      return absl::FailedPreconditionError("No scan header found");
   }

   initialized_ = true;
   return absl::OkStatus();
}

absl::Status
LosslessJPEGDecoder::ParseScanHeader(uint32_t scan_offset,
                                     uint16_t segment_len) {
   uint32_t data_offset = scan_offset + 4;  // skip marker and length
   uint8_t num_components;
   RETURN_IF_ERROR(data_accessor_->CopyBytes(
                       data_offset, sizeof(num_components), &num_components),
                   VLOG(4) << "Failed to copy num components data.");
   data_offset += sizeof(num_components);
   CHECK_EQ(num_components, components_);
   VLOG(5) << "Num components: " << absl::StrFormat("%d", num_components);

   scan_components_.resize(num_components);
   for (int comp_idx = 0; comp_idx < num_components; comp_idx++) {
      ScanComponent& scan_component = scan_components_[comp_idx];
      auto status = data_accessor_->CopyBytes(data_offset, sizeof(uint8_t),
                                              &scan_component.component_id);
      data_offset += sizeof(uint8_t);
      if (!status.ok()) {
         VLOG(4) << "Failed to copy data.";
         return status;
      }
      CHECK_EQ(scan_component.component_id,
               component_infos_[comp_idx].component_id);

      uint8_t table_indices;
      status = data_accessor_->CopyBytes(data_offset, sizeof(table_indices),
                                         &table_indices);
      data_offset += sizeof(table_indices);
      if (!status.ok()) {
         VLOG(4) << "Failed to copy data.";
         return status;
      }
      scan_component.dc_table_id = table_indices >> 4;
      scan_component.ac_table_id = table_indices & 0x0F;
      VLOG(5) << absl::StrFormat(
          "Scan component id: %d ac table: %d dc table: %d",
          scan_component.component_id, scan_component.ac_table_id,
          scan_component.dc_table_id);
   }

   RETURN_IF_ERROR(
       data_accessor_->CopyBytes(data_offset, sizeof(predictor_), &predictor_),
       VLOG(4) << "Failed to copy predictor data.");
   data_offset += sizeof(predictor_);
   CHECK_LE(predictor_, 7);
   VLOG(5) << "Predictor: " << absl::StrFormat("%d", predictor_);

   uint8_t se;
   RETURN_IF_ERROR(data_accessor_->CopyBytes(data_offset, sizeof(se), &se),
                   VLOG(4) << "Failed to copy se data.");
   data_offset += sizeof(se);
   CHECK_EQ(se, 0);

   uint8_t ahai;
   RETURN_IF_ERROR(data_accessor_->CopyBytes(data_offset, sizeof(ahai), &ahai),
                   VLOG(4) << "Failed to copy ahai data.");
   data_offset += sizeof(ahai);
   point_transform_ = ahai & 0x0F;
   VLOG(5) << "Point transform: " << absl::StrFormat("%d", point_transform_);

   // Make sure we read the entire header
   CHECK_EQ(data_offset, scan_offset + 2 + segment_len);

   image_data_offset_ = data_offset;
   scan_parsed_ = true;
   return absl::OkStatus();
}

absl::StatusOr<int32_t>
DecodeNextDiff(JPEGBitPump& bit_pump, const HuffmanTable& huff_table) {
   size_t code_len = huff_table.GetShortestCodeLen();
   uint16_t code_candidate;
   ASSIGN_OR_RETURN(code_candidate, bit_pump.GetBits(code_len),
                    VLOG(4) << "Failed to read bit from JPEG data stream.");

   uint8_t ssss = 0, longest_code_len = huff_table.GetLongestCodeLen();
   bool code_found = false;
   do {
      // Code lookup
      auto symbol_or = huff_table.Decode(code_len, code_candidate);
      if (symbol_or.ok()) {
         code_found = true;
         ssss = *symbol_or;
         break;
      } else if (symbol_or.status().code() != absl::StatusCode::kNotFound) {
         VLOG(4) << "Unexpected error while decoding huffman code.";
         return symbol_or.status();
      }

      uint16_t bit_val;
      ASSIGN_OR_RETURN(bit_val, bit_pump.GetBits(1),
                       VLOG(4) << "Failed to read bit from JPEG data stream.");
      code_candidate = (code_candidate << 1) | bit_val;
      code_len++;
   } while (code_len <= longest_code_len);

   if (!code_found) {
      VLOG(4) << absl::StrFormat("Invalid code in JPEG data stream: %d",
                                 code_candidate);
      return absl::FailedPreconditionError("Invalid code in JPEG data stream");
   }

   // The SSSS value tells us how many bits to read from the data stream to
   // decode the diff magnitude
   assert(ssss <= 16);
   int32_t diff_val;
   if (ssss == 16) {
      diff_val = 32768;
   } else if (ssss == 0) {
      diff_val = 0;
   } else {
      uint16_t diff_bits;
      ASSIGN_OR_RETURN(diff_bits, bit_pump.GetBits(ssss),
                       VLOG(4)
                           << "Failed to read diff bits from JPEG data stream");
      diff_val = static_cast<int32_t>(diff_bits);
      /*
       * Sign extend the diff magnitude if it falls in the lower half of the
       * diff range. Ex:
       * SSSS=2        range: -3, -2,  2,  3
       * bit representations: 00, 01, 10, 11
       */
      if (diff_val < (1 << (ssss - 1))) {
         // diff_val falls in the lower half of the range, subtract 2^SSSS-1
         // to get the true negative diff value.
         diff_val -= (1 << ssss) - 1;
      }
   }
   if (VLOG_IS_ON(6)) {
      VLOG(6) << absl::StrFormat("SSSS: %d diff_val: %d", ssss, diff_val);
   }

   return diff_val;
}

#define ADD_NEXT_DIFF_OR_RETURN(var, pred, bit_pump, huff_table)   \
   do {                                                            \
      int32_t diff;                                                \
      ASSIGN_OR_RETURN(diff, DecodeNextDiff(bit_pump, huff_table), \
                       VLOG(4) << "Failed to decode next diff");   \
      var = pred + diff;                                           \
   } while (0)

absl::Status
LosslessJPEGDecoder::Decode(ImageBuf<uint16_t>& output) {
   if (!initialized_) {
      return absl::FailedPreconditionError("Decoder not initialized.");
   }

   CHECK_EQ(output.get_height(), image_height_);
   CHECK_EQ(output.get_width(), image_width_);
   CHECK_EQ(output.get_components(), components_);

   // Set up the huffman tables for each component
   std::vector<const HuffmanTable*> huff_tables(components_);
   for (int i = 0; i < components_; i++) {
      uint8_t table_id = scan_components_[i].dc_table_id;
      huff_tables[i] = dc_huff_tables[table_id].get();
   }

   JPEGBitPump bit_pump(data_accessor_, image_data_offset_);
   size_t restart_counter = 0;

   for (size_t row = 0; row < image_height_; row++) {
      for (size_t col = 0; col < image_width_; col++) {
         bool restart = false;
         /*
          * For files that have a defined restart interval we reset the
          * prediction at every restart interval. We also expect a marker
          * within the entropy coded bit stream.
          */
         if (restart_interval_ > 0) {
            if (restart_counter == restart_interval_) {
               VLOG(6) << "Restart interval reached, resetting prediction";
               restart = true;
               restart_counter = 0;
               // Skip the restart marker in the bit stream
               uint16_t marker;
               ASSIGN_OR_RETURN(marker, bit_pump.SkipMarker(),
                                VLOG(4) << "Failed to skip restart marker.");
               CHECK_GE(marker, JPEG_MARKER_RESTART_0);
               CHECK_LE(marker, JPEG_MARKER_RESTART_7);
            }
            restart_counter++;
         }
         for (size_t comp = 0; comp < components_; comp++) {
            // Calculate prediction
            int32_t prediction;
            if ((row == 0 && col == 0) || restart) {
               /*
                * For the first sample the prediction is:
                * 2 ^ (bits_per_sample - point_transform - 1)
                * We also reset the prediction at every restart interval.
                */
               assert(bits_per_sample_ >= 2);
               prediction = 1 << (bits_per_sample_ - point_transform_ - 1);
            } else if (row == 0) {
               // For the first row prediction uses the sample to the left
               prediction = output(row, col - 1, comp);
            } else if (col == 0) {
               // For the first column of all non-first rows, prediction uses
               // the sample from the row above
               prediction = output(row - 1, 0, comp);
            } else {
               // Prediction is based on the selected predictor
               switch (predictor_) {
                  case 1:  // sample to the left
                     prediction = output(row, col - 1, comp);
                     break;
                  case 2:  // sample above
                     prediction = output(row - 1, col, comp);
                     break;
                  case 3:  // sample diagonally above&left
                     prediction = output(row - 1, col - 1, comp);
                     break;
                  case 4:  // sample to the left + above - diagonal above&left
                     prediction = output(row, col - 1, comp) +
                                  output(row - 1, col, comp) -
                                  output(row - 1, col - 1, comp);
                     break;
                  case 5:  // left + (above - diagonal)/2
                     prediction = output(row, col - 1, comp) +
                                  (output(row - 1, col, comp) -
                                   output(row - 1, col - 1, comp)) /
                                      2;
                     break;
                  case 6:  // above + (left - diagonal)/2
                     prediction = output(row - 1, col, comp) +
                                  (output(row, col - 1, comp) -
                                   output(row - 1, col - 1, comp)) /
                                      2;
                     break;
                  case 7:  // (left + above)/2
                     prediction = (output(row, col - 1, comp) +
                                   output(row - 1, col, comp)) /
                                  2;
                     break;
                  default:
                     VLOG(4) << "Invalid predictor: " << predictor_;
                     return absl::FailedPreconditionError("Invalid predictor");
                     break;
               }
            }
            VLOG(6) << "Prediction: " << prediction;

            int32_t decoded_val;
            ADD_NEXT_DIFF_OR_RETURN(decoded_val, prediction, bit_pump,
                                    *huff_tables[comp]);
            // decoded val mod 2^16
            if (decoded_val < 0) {
               decoded_val += 65535;
            } else if (decoded_val > 65535) {
               decoded_val -= 65535;
            }

            VLOG(6) << "Decoded sample value: " << decoded_val;
            output(row, col, comp) = static_cast<uint16_t>(decoded_val);
         }
      }
   }

   return absl::OkStatus();
}

#define UNPACK_YUV420(output, row, col, y1, y2, y3, y4, cb, cr) \
   output(row, col, 0) = y1;                                    \
   output(row, col, 1) = cb;                                    \
   output(row, col, 2) = cr;                                    \
   output(row, col + 1, 0) = y2;                                \
   output(row, col + 1, 1) = cb;                                \
   output(row, col + 1, 2) = cr;                                \
   output(row + 1, col, 0) = y3;                                \
   output(row + 1, col, 1) = cb;                                \
   output(row + 1, col, 2) = cr;                                \
   output(row + 1, col + 1, 0) = y4;                            \
   output(row + 1, col + 1, 1) = cb;                            \
   output(row + 1, col + 1, 2) = cr;

absl::Status
DecodeYUV420(JPEGBitPump& bit_pump,
             std::vector<const HuffmanTable*>& huff_tables,
             int32_t base_prediction, uint32_t image_width,
             uint32_t image_height, ImageBuf<uint16_t>& output) {
   int32_t y1, y2, y3, y4, cb, cr;

   ADD_NEXT_DIFF_OR_RETURN(y1, base_prediction, bit_pump, *huff_tables[0]);
   assert(y1 >= 0);
   ADD_NEXT_DIFF_OR_RETURN(y2, y1, bit_pump, *huff_tables[0]);
   assert(y2 >= 0);
   ADD_NEXT_DIFF_OR_RETURN(y3, y1, bit_pump, *huff_tables[0]);
   assert(y3 >= 0);
   ADD_NEXT_DIFF_OR_RETURN(y4, y3, bit_pump, *huff_tables[0]);
   assert(y4 >= 0);
   ADD_NEXT_DIFF_OR_RETURN(cb, base_prediction, bit_pump, *huff_tables[1]);
   assert(cb >= 0);
   ADD_NEXT_DIFF_OR_RETURN(cr, base_prediction, bit_pump, *huff_tables[2]);
   assert(cr >= 0);

   VLOG(6) << "y1: " << y1 << " y2: " << y2 << " y3: " << y3 << " y4: " << y4
           << " cb: " << cb << " cr: " << cr;

   UNPACK_YUV420(output, 0, 0, y1, y2, y3, y4, cb, cr);

   for (size_t row = 0; row < image_height; row += 2) {
      for (size_t col = (row == 0 ? 2 : 0); col < image_width; col += 2) {
         int32_t pred_y1, pred_y3, pred_cb, pred_cr;
         if (col == 0) {
            // Interesting quirk with sony encoding, prediction is based on
            // row-2 instead of row-1
            pred_y1 = output(row - 2, 0, 0);
            pred_y3 = 0;  // refers to reconstructed y1 when col == 0
            pred_cb = output(row - 2, 0, 1);
            pred_cr = output(row - 2, 0, 2);
         } else {
            pred_y1 = output(row, col - 1, 0);
            pred_y3 = output(row + 1, col - 1, 0);
            pred_cb = output(row, col - 1, 1);
            pred_cr = output(row, col - 1, 2);
         }

         ADD_NEXT_DIFF_OR_RETURN(y1, pred_y1, bit_pump, *huff_tables[0]);
         assert(y1 >= 0);
         ADD_NEXT_DIFF_OR_RETURN(y2, y1, bit_pump, *huff_tables[0]);
         assert(y2 >= 0);
         ADD_NEXT_DIFF_OR_RETURN(y3, ((col == 0) ? y1 : pred_y3), bit_pump,
                                 *huff_tables[0]);
         assert(y3 >= 0);
         ADD_NEXT_DIFF_OR_RETURN(y4, y3, bit_pump, *huff_tables[0]);
         assert(y4 >= 0);
         ADD_NEXT_DIFF_OR_RETURN(cb, pred_cb, bit_pump, *huff_tables[1]);
         assert(cb >= 0);
         ADD_NEXT_DIFF_OR_RETURN(cr, pred_cr, bit_pump, *huff_tables[2]);
         assert(cr >= 0);

         VLOG(6) << "y1: " << y1 << " y2: " << y2 << " y3: " << y3
                 << " y4: " << y4 << " cb: " << cb << " cr: " << cr;

         UNPACK_YUV420(output, row, col, y1, y2, y3, y4, cb, cr);
      }
   }

   VLOG(5) << bit_pump.GetNumBytesRead() << " bytes read from data stream.";

   return absl::OkStatus();
}

#define UNPACK_YUV422(output, row, col, y1, y2, cb, cr) \
   output(row, col, 0) = y1;                            \
   output(row, col, 1) = cb;                            \
   output(row, col, 2) = cr;                            \
   output(row, col + 1, 0) = y2;                        \
   output(row, col + 1, 1) = cb;                        \
   output(row, col + 1, 2) = cr;

absl::Status
DecodeYUV422(JPEGBitPump& bit_pump,
             std::vector<const HuffmanTable*>& huff_tables,
             int32_t base_prediction, uint32_t image_width,
             uint32_t image_height, ImageBuf<uint16_t>& output) {
   int32_t y1, y2, cb, cr;

   ADD_NEXT_DIFF_OR_RETURN(y1, base_prediction, bit_pump, *huff_tables[0]);
   assert(y1 >= 0);
   ADD_NEXT_DIFF_OR_RETURN(y2, y1, bit_pump, *huff_tables[0]);
   assert(y2 >= 0);
   ADD_NEXT_DIFF_OR_RETURN(cb, base_prediction, bit_pump, *huff_tables[1]);
   assert(cb >= 0);
   ADD_NEXT_DIFF_OR_RETURN(cr, base_prediction, bit_pump, *huff_tables[2]);
   assert(cr >= 0);

   VLOG(6) << "y1: " << y1 << " y2: " << y2 << " cb: " << cb << " cr: " << cr;

   UNPACK_YUV422(output, 0, 0, y1, y2, cb, cr);

   for (size_t row = 0; row < image_height; row++) {
      for (size_t col = (row == 0 ? 2 : 0); col < image_width; col += 2) {
         int32_t pred_y1, pred_cb, pred_cr;
         if (col == 0) {
            pred_y1 = output(row - 1, 0, 0);
            pred_cb = output(row - 1, 0, 1);
            pred_cr = output(row - 1, 0, 2);
         } else {
            pred_y1 = output(row, col - 1, 0);
            pred_cb = output(row, col - 1, 1);
            pred_cr = output(row, col - 1, 2);
         }

         ADD_NEXT_DIFF_OR_RETURN(y1, pred_y1, bit_pump, *huff_tables[0]);
         assert(y1 >= 0);
         ADD_NEXT_DIFF_OR_RETURN(y2, y1, bit_pump, *huff_tables[0]);
         assert(y2 >= 0);
         ADD_NEXT_DIFF_OR_RETURN(cb, pred_cb, bit_pump, *huff_tables[1]);
         assert(cb >= 0);
         ADD_NEXT_DIFF_OR_RETURN(cr, pred_cr, bit_pump, *huff_tables[2]);
         assert(cr >= 0);

         VLOG(6) << "y1: " << y1 << " y2: " << y2 << " cb: " << cb
                 << " cr: " << cr;

         UNPACK_YUV422(output, row, col, y1, y2, cb, cr);
      }
   }

   VLOG(5) << bit_pump.GetNumBytesRead() << " bytes read from data stream.";

   return absl::OkStatus();
}

absl::Status
LosslessJPEGDecoder::DecodeSonyYUV(ImageBuf<uint16_t>& output) {
   if (!initialized_) {
      return absl::FailedPreconditionError("Decoder not initialized.");
   }

   CHECK_EQ(output.get_height(), image_height_);
   CHECK_EQ(output.get_width(), image_width_);
   CHECK_EQ(output.get_components(), components_);
   CHECK_EQ(components_, 3U);

   // Set up the huffman tables for each component
   std::vector<const HuffmanTable*> huff_tables(components_);
   for (int i = 0; i < components_; i++) {
      uint8_t table_id = scan_components_[i].dc_table_id;
      huff_tables[i] = dc_huff_tables[table_id].get();
   }

   JPEGBitPump bit_pump(data_accessor_, image_data_offset_);

   int32_t prediction = 1 << (bits_per_sample_ - point_transform_ - 1);
   /*
    * Sony medium RAW files are YUV 4:2:0 with only Cb and Cr sampled both
    * horizontally and vertically. Sony small RAW files are YUV 4:2:2 where
    * Cb and Cr are only sampled horizontally.
    */
   if (chroma_subsampling_mode_ == ChromaSubsamplingMode::_4_2_0) {
      return DecodeYUV420(bit_pump, huff_tables, prediction, image_width_,
                          image_height_, output);
   } else {
      CHECK_EQ(static_cast<int>(chroma_subsampling_mode_),
               static_cast<int>(ChromaSubsamplingMode::_4_2_2));
      return DecodeYUV422(bit_pump, huff_tables, prediction, image_width_,
                          image_height_, output);
   }
   return absl::OkStatus();
}

}  // namespace cmrw
