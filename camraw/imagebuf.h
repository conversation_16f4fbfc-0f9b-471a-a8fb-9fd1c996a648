#ifndef _IMAGEBUF_H_
#define _IMAGEBUF_H_

#include <cassert>
#include <cstring>
#include <memory>
#include <stdlib.h>

namespace cmrw {

template <class T>
class ImageBuf {
   public:
      // Create an ImageBuf with its own buffer
      ImageBuf(size_t height, size_t width, size_t components = 1)
          : height_(height), width_(width), components_(components) {
         owned_buffer_ = std::make_unique<T[]>(height * width * components);
         buf_ = owned_buffer_.get();
      }

      // Create an ImageBuf that's a reference to an external buffer. If
      // take_ownership is specified then convert it to an owned buffer.
      ImageBuf(T* buffer, bool take_ownership, size_t height, size_t width,
               size_t components = 1)
          : height_(height),
            width_(width),
            components_(components),
            buf_(buffer) {
         if (take_ownership) {
            owned_buffer_ = std::unique_ptr<T[]>(buffer);
         } else {
            owned_buffer_ = nullptr;
         }
      }

      // Copy / Copy assignment not permitted
      ImageBuf(const ImageBuf&) = delete;
      ImageBuf& operator=(const ImageBuf&) = delete;

      T& operator()(size_t row, size_t col, size_t comp = 0) {
         assert(row < height_);
         assert(col < width_);
         assert(comp < components_);
         return buf_[(row * width_ * components_) + (col * components_) + comp];
      }

      T operator()(size_t row, size_t col, size_t comp = 0) const {
         assert(row < height_);
         assert(col < width_);
         assert(comp < components_);
         return buf_[(row * width_ * components_) + (col * components_) + comp];
      }

      T* get_ptr(size_t row, size_t col, size_t comp = 0) const {
         assert(row < height_);
         assert(col < width_);
         assert(comp < components_);
         return &buf_[(row * width_ * components_) + (col * components_) +
                      comp];
      }

      const size_t get_height() const { return height_; }

      const size_t get_width() const { return width_; }

      const size_t get_components() const { return components_; }

      /*
       * Copy a rectangular tile from the in_buf input parameter into this
       * ImageBuf.
       *
       * Parameters:
       *   in_buf - input buffer to copy the tile from. If no other parameters
       *            are specified the entire input buffer is copied in.
       *   [local_row] - which row of this ImageBuf to start copying into.
       *   [local_col] - which column of this ImageBuf to start copying into.
       *   [input_row] - which row of the input buffer to start copying from.
       *   [input_col] - which column of the input buffer to start copying from.
       *   [num_rows] - if 0 all input rows are copied, otherwise specifies
       *                the number of input rows to copy.
       *   [num_cols] - if 0 all input columns are copied, otherwise specifies
       *                the number of input columns to copy.
       */
      void copy_tile_in_from(const ImageBuf& in_buf, size_t local_row = 0,
                             size_t local_col = 0, size_t input_row = 0,
                             size_t input_col = 0, size_t num_rows = 0,
                             size_t num_cols = 0) {
         size_t copy_rows =
             (num_rows == 0) ? in_buf.get_height() - input_row : num_rows;
         size_t copy_cols =
             (num_cols == 0) ? in_buf.get_width() - input_col : num_cols;
         assert(local_row + copy_rows <= height_);
         assert(local_col + copy_cols <= width_);
         assert(input_row + copy_rows <= in_buf.get_height());
         assert(input_col + copy_cols <= in_buf.get_width());

         if (local_row == 0 && local_col == 0 && input_row == 0 &&
             input_col == 0 && copy_rows == height_ && copy_cols == width_ &&
             in_buf.get_components() == components_) {
            // Optimization - we can do a full copy
            memcpy(buf_, in_buf.get_ptr(0, 0),
                   width_ * height_ * components_ * sizeof(T));
         } else {
            // Compute size in bytes for each row to be copied
            size_t num_row_bytes =
                copy_cols * in_buf.get_components() * sizeof(T);

            // Copy one entire row at a time
            for (uint32_t row_idx = 0; row_idx < copy_rows; row_idx++) {
               T* from_ptr = in_buf.get_ptr(input_row + row_idx, input_col);
               T* to_ptr = get_ptr(local_row + row_idx, local_col);
               memcpy(to_ptr, from_ptr, num_row_bytes);
            }
         }
      }

   private:
      size_t height_, width_, components_;
      T* buf_;
      std::unique_ptr<T[]> owned_buffer_;
};

}  // namespace cmrw

#endif  // _IMAGEBUF_H_
