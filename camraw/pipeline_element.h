#ifndef PIPELINE_ELEMENT_H_
#define PIPELINE_ELEMENT_H_

#include <string>
#include <unordered_set>

#include "camraw/imagemeta.h"

namespace cmrw {

struct PipelineElementIOSpec {
      bool one_sample_per_pixel;
      bool three_samples_per_pixel;
};

enum class PipelineElementType {
   SOURCE,
   PROCESSOR,
   SINK,
};

inline const std::string
PipelineElementTypeToString(PipelineElementType t) {
   switch (t) {
      case PipelineElementType::SOURCE:
         return "source";
      case PipelineElementType::PROCESSOR:
         return "processor";
      case PipelineElementType::SINK:
         return "sink";
      default:
         return "element";
   }
}

class PipelineElement {
   public:
      PipelineElement(std::string name, PipelineElementType type,
                      PipelineElementIOSpec input_spec,
                      PipelineElementIOSpec output_spec,
                      std::unordered_set<ImageMetaId> required_metadata,
                      std::unordered_set<ImageMetaId> optional_metadata)
          : required_metadata_(required_metadata),
            optional_metadata_(optional_metadata),
            name_(name),
            type_(type),
            input_spec_(input_spec),
            output_spec_(output_spec) {}
      virtual ~PipelineElement() = default;
      const PipelineElementType type() const { return type_; }
      const std::string& name() const { return name_; }
      const PipelineElementIOSpec& input_spec() const { return input_spec_; }
      const PipelineElementIOSpec& output_spec() const { return output_spec_; }
      const std::unordered_set<ImageMetaId>& required_metadata() const {
         return required_metadata_;
      }
      const std::unordered_set<ImageMetaId>& optional_metadata() const {
         return optional_metadata_;
      }

   protected:
      std::unordered_set<ImageMetaId> required_metadata_;
      std::unordered_set<ImageMetaId> optional_metadata_;

   private:
      std::string name_;
      PipelineElementType type_;
      PipelineElementIOSpec input_spec_;
      PipelineElementIOSpec output_spec_;
};

}  // namespace cmrw

#endif /* PIPELINE_ELEMENT_H_ */
