#ifndef FILE_TYPE_CLASSIFIER_H_
#define FILE_TYPE_CLASSIFIER_H_

#include <memory>
#include <string>
#include <vector>

#include "absl/container/flat_hash_map.h"
#include "absl/status/status.h"
#include "absl/status/statusor.h"

#include "camraw/data_accessor.h"
#include "camraw/file_parser.h"
#include "camraw/file_type_registry.h"
#include "camraw/pipeline_source.h"

namespace cmrw {

struct FileTypeClassifierTreeNode {
      const FileTypeRegistryElement* element;
      std::vector<std::unique_ptr<FileTypeClassifierTreeNode>> children;
};

class FileTypeClassifier {
   public:
      absl::Status ClassifyFile(DataAccessor& data_accessor,
                                std::string file_path = "");
      absl::StatusOr<std::string> GetFileTypeString();
      absl::StatusOr<std::unique_ptr<FileParser>> GetFileParser();
      absl::StatusOr<std::unique_ptr<PipelineSource>> GetPipelineSource(
          FileParser* file_parser);

   private:
      absl::Status BuildTree();
      absl::Status ApplyExtensionHintsToTree(std::string file_path);

      bool classify_called = false;
      const FileTypeRegistryElement* classified_type_ = nullptr;
      std::unique_ptr<FileParser> saved_parser_ = nullptr;

      DataAccessor* data_accessor_;
      std::unique_ptr<FileTypeClassifierTreeNode> tree_ = nullptr;
      absl::flat_hash_map<std::string, FileTypeClassifierTreeNode*> node_map_;
};

}  // namespace cmrw

#endif /* FILE_TYPE_CLASSIFIER_H_ */
