#include <cmath>
#include <glog/logging.h>

#include "camraw/color_space_processor.h"
#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/limits.h"
#include "camraw/matrix.h"

namespace cmrw {

namespace {
float
bring_into_gamut(float val, uint32_t& out_of_gamut_counter) {
   if (val < 0) {
      out_of_gamut_counter++;
      return 0;
   } else if (val > 1) {
      out_of_gamut_counter++;
      return 1;
   }

   return val;
}

float
apply_srgb_gamma(float val) {
   if (val < 0.0031308) {
      return 12.92 * val;
   }

   // 1.055 x val ^ (1/2.4) - 0.055
   return 1.055 * pow(val, 0.4166666) - 0.055;
}
}  // namespace

absl::Status
ColorSpaceProcessor::Init(const ImageMeta& image_meta) {
   // for now we only support converting from camera color space to SRGB
   CHECK_EQ(static_cast<int>(from_space_),
            static_cast<int>(Colorspace::CAMERA));
   CHECK_EQ(static_cast<int>(to_space_), static_cast<int>(Colorspace::SRGB));

   auto color_space_or =
       image_meta.GetOne<Colorspace>(ImageMetaId::COLOR_SPACE);
   CHECK(color_space_or.ok());
   CHECK_EQ(static_cast<int>(*color_space_or),
            static_cast<int>(Colorspace::CAMERA));

   auto camera_color_matrix_or =
       image_meta.GetMulti<float>(ImageMetaId::CAMERA_COLOR_MATRIX);
   CHECK(camera_color_matrix_or.ok());
   CHECK_EQ((*camera_color_matrix_or).size(), 9U);

   Matrix camera_to_xyz_matrix = Matrix<float>(3, 3, *camera_color_matrix_or);
   /*
    * We multiply each (r,g,b) value in camera color space with:
    *
    *   M(XYZ->sRGB) x M(Cam->XYZ) x (r,g,b)
    */
   multiplier_matrix_ = std::make_unique<Matrix<float>>(
       COLORSPACE_XYZ_TO_SRGB_BRADFORD_D50_MATRIX.Multiply(
           camera_to_xyz_matrix));

   return absl::OkStatus();
}

void
ColorSpaceProcessor::LogStart() {
   LOG(INFO) << "Converting from CAMERA color space to sRGB";
   VLOG(3) << "Multiplier matrix: " << multiplier_matrix_->DisplayString();
}

absl::Status
ColorSpaceProcessor::Run(const ImageBuf<uint16_t>& input_buffer,
                         ImageBuf<uint16_t>& output_buffer) {
   CHECK_EQ(input_buffer.get_height(), output_buffer.get_height());
   CHECK_EQ(input_buffer.get_width(), output_buffer.get_width());
   CHECK_EQ(input_buffer.get_components(), 3U);
   CHECK_EQ(output_buffer.get_components(), 3U);

   uint32_t out_of_gamut = 0;

   for (uint32_t row = 0; row < input_buffer.get_height(); row++) {
      for (uint32_t col = 0; col < input_buffer.get_width(); col++) {
         // Map camera RGB values to the [0.0,1.0] range as float
         float cam_red =
             static_cast<float>(input_buffer(row, col, 0)) / MAX_UINT16;
         float cam_green =
             static_cast<float>(input_buffer(row, col, 1)) / MAX_UINT16;
         float cam_blue =
             static_cast<float>(input_buffer(row, col, 2)) / MAX_UINT16;

         Matrix<float> input(/*rows*/ 3, /*cols*/ 1,
                             {cam_red, cam_green, cam_blue});

         Matrix<float> linear_srgb_values = multiplier_matrix_->Multiply(input);

         float linear_srgb_red =
             bring_into_gamut(linear_srgb_values(0, 0), out_of_gamut);
         float linear_srgb_green =
             bring_into_gamut(linear_srgb_values(1, 0), out_of_gamut);
         float linear_srgb_blue =
             bring_into_gamut(linear_srgb_values(2, 0), out_of_gamut);

         float srgb_red = apply_srgb_gamma(linear_srgb_red);
         float srgb_green = apply_srgb_gamma(linear_srgb_green);
         float srgb_blue = apply_srgb_gamma(linear_srgb_blue);

         output_buffer(row, col, 0) =
             static_cast<uint16_t>(srgb_red * MAX_UINT16);
         output_buffer(row, col, 1) =
             static_cast<uint16_t>(srgb_green * MAX_UINT16);
         output_buffer(row, col, 2) =
             static_cast<uint16_t>(srgb_blue * MAX_UINT16);
      }
   }

   CommitStats([&] { out_of_gamut_ += out_of_gamut; });

   return absl::OkStatus();
}

void
ColorSpaceProcessor::LogStats() {
   LOG(INFO) << "Colorspace transform resulted in " << out_of_gamut_
             << " out of gamut samples being clipped into the gamut.";
}

absl::Status
ColorSpaceProcessor::MutateMetadata(ImageMeta& image_meta) {
   image_meta.SetOne<Colorspace>(ImageMetaId::COLOR_SPACE, Colorspace::SRGB);
   VLOG(3) << "Set metadata COLOR_SPACE = sRGB";

   return absl::OkStatus();
}

}  // namespace cmrw
