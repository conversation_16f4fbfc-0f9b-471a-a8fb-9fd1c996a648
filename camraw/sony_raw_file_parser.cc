#include <unordered_set>
#include <utility>

#include "glog/logging.h"

#include "camraw/endian.h"
#include "camraw/sony_raw_file_parser.h"
#include "camraw/status_macros.h"
#include "camraw/tiff_tag_id.h"

namespace cmrw {

TIFFIFDType
SonyRawFileParser::GetIFDTypeFromTag(const TIFFTag& tag) {
   if (tag.GetId() == TIFFTagId::DNG_PRIVATE_DATA) {
      return TIFF_IFD_TYPE_DNG_PRIVATE_DATA;
   }

   return TIFFFileParser::GetIFDTypeFromTag(tag);
}

absl::StatusOr<bool>
SonyRawFileParser::ValidateFile() {
   bool validated;
   ASSIGN_OR_RETURN(validated, TIFFFileParser::ValidateFile());
   if (!validated) {
      return false;
   }

   VLOG(2) << "Validating Sony RAW file.";
   validated_ = false;  // we're not done yet

   CHECK_EQ(ifd_list_.size(), 1U);
   for (size_t cur_idx = 0; cur_idx < ifd_list_.size(); cur_idx++) {
      TIFFIFD& cur_ifd = ifd_list_[cur_idx].ifd;
      // Scan for these tags:
      // * new subfile type - to learn this IFD's type
      // * sub ifds - to discover the primary image IFD
      // * SONY_RAW_TYPE - to validate this as a SONY RAW file
      std::unordered_set<TIFFTagId> tags_to_search = {
          TIFFTagId::NEW_SUBFILE_TYPE, TIFFTagId::SUB_IFDS,
          TIFFTagId::SONY_RAW_TYPE};
      TIFFIFDScanResult scan_result;
      ASSIGN_OR_RETURN(scan_result, cur_ifd.Scan(&tags_to_search));

      if (scan_result.next_ifd_offset != 0) {
         TIFFIFD new_ifd(scan_result.next_ifd_offset, data_accessor_,
                         endianness_);
         InsertNewIFDListEntry(scan_result.next_ifd_offset,
                               TIFFIFDListEntryStatus::DISCOVERED,
                               TIFF_IFD_TYPE_UNKNOWN, new_ifd);
      }

      bool primary_ifd = false, sony_raw_type_present = false;
      for (auto& scanned_tag : scan_result.tags) {
         if (scanned_tag.GetId() == TIFFTagId::SONY_RAW_TYPE) {
            sony_raw_type_present = true;
         } else if (scanned_tag.GetId() == TIFFTagId::SUB_IFDS) {
            // Found new IFD(s), add to the list
            for (size_t i = 0; i < scanned_tag.GetDataCount(); i++) {
               uint32_t ifd_offset;
               ASSIGN_OR_RETURN(ifd_offset,
                                scanned_tag.GetDataValueAtIdx<uint32_t>(i));

               TIFFIFD new_ifd(ifd_offset, data_accessor_, endianness_);
               InsertNewIFDListEntry(ifd_offset,
                                     TIFFIFDListEntryStatus::DISCOVERED,
                                     TIFF_IFD_TYPE_UNKNOWN, new_ifd);
            }
         } else if (scanned_tag.GetId() == TIFFTagId::NEW_SUBFILE_TYPE) {
            uint32_t subfile_type;
            ASSIGN_OR_RETURN(subfile_type,
                             scanned_tag.GetDataValue<uint32_t>());
            if (subfile_type % 2 == 0) {
               primary_ifd = true;
            }
         }
      }

      if (primary_ifd && sony_raw_type_present) {
         VLOG(1) << "Sony RAW file validated.";

         validated_ = true;
         return true;
      } else if (primary_ifd && !sony_raw_type_present) {
         VLOG(2) << "No SONY_RAW_TYPE tag found in PRIMARY_IMAGE ifd.";
         return false;
      }
   }

   VLOG(2) << "No primary image IFDs found.";
   return false;
}

absl::Status
SonyRawFileParser::Init() {
   RETURN_IF_ERROR(TIFFFileParser::Init());

   VLOG(2) << "Initializing Sony RAW file parser.";
   // Add the SR2 IFD to the ifd list
   uint32_t sr2_offset;
   TIFF_PARSER_ASSIGN_TAG_VAL_OR_RETURN(this, uint32_t, sr2_offset,
                                        TIFF_IFD_TYPE_DNG_PRIVATE_DATA,
                                        TIFFTagId::SONY_SR2_SUB_IFD_OFFSET);

   VLOG(2) << "Inserting SR2 IFD to the IFD list";
   // this IFD will be replaced
   TIFFIFD new_ifd(sr2_offset, data_accessor_, endianness_);
   InsertNewIFDListEntry(sr2_offset, TIFFIFDListEntryStatus::DISCOVERED,
                         TIFF_IFD_TYPE_SONY_SR2, new_ifd);
   BuildIFDListIndex();

   VLOG(1) << "Sony RAW file parser initialized.";

   return absl::OkStatus();
}

absl::StatusOr<std::unique_ptr<uint32_t[]>>
SonyRawFileParser::decrypt_sr2(DataAccessor& accessor, uint32_t sr2_offset,
                               uint32_t sr2_length, uint32_t sr2_key) {
   auto sr2_data = std::make_unique<uint32_t[]>(sr2_length);
   auto result = std::make_unique<uint32_t[]>(sr2_length);

   VLOG(1) << "Decrypting SR2 at offset: " << sr2_offset
           << " SR2 len: " << sr2_length << " SR2 key: " << sr2_key;
   RETURN_IF_ERROR(accessor.CopyBytes(sr2_offset, sr2_length, sr2_data.get()),
                   VLOG(2) << "Failed to copy bytes for SR2 decryption");

   uint32_t pad[128], i, low, high, prev;

   for (i = 0; i < 4; i++) {
      prev = (i == 0) ? sr2_key : pad[i - 1];
      low = ((prev & 0xFFFF) * 0x0EDD) + 1;
      high = ((prev >> 16) * 0x0EDD) + ((prev & 0xFFFF) * 0x02E9) + (low >> 16);
      pad[i] = ((high & 0xFFFF) << 16) + (low & 0xFFFF);
   }

   pad[3] = (pad[3] << 1) | ((pad[0] ^ pad[2]) >> 31);

   for (i = 4; i < 127; i++) {
      pad[i] =
          ((pad[i - 4] ^ pad[i - 2]) << 1) | ((pad[i - 3] ^ pad[i - 1]) >> 31);
   }

   uint32_t j;
   for (i = 0, j = 127; i < sr2_length; i++, j++) {
      pad[j & 127] = pad[(j + 1) & 127] ^ pad[(j + 65) & 127];
      result[i] = big_endian<uint32_t>(big_endian<uint32_t>(sr2_data[i]) ^
                                       pad[j & 127]);
   }

   return result;
}

absl::StatusOr<const TIFFIFD*>
SonyRawFileParser::GetIFDAtIndex(size_t idx) {
   if (!initialized_) {
      return absl::FailedPreconditionError("Parser not initialized.");
   }

   if (idx >= ifd_list_.size()) {
      return absl::OutOfRangeError("Index out of range.");
   }

   // Decrypt SR2 if needed
   if (!sr2_decrypted_) {
      TIFFIFDListEntry& list_entry = ifd_list_[idx];
      if (list_entry.type == TIFF_IFD_TYPE_SONY_SR2) {
         uint32_t sr2_offset = list_entry.ifd_offset;
         uint32_t sr2_length;
         TIFF_PARSER_ASSIGN_TAG_VAL_OR_RETURN(
             this, uint32_t, sr2_length, TIFF_IFD_TYPE_DNG_PRIVATE_DATA,
             TIFFTagId::SONY_SR2_SUB_IFD_LENGTH);

         const TIFFTag* sr2_key_tag;
         ASSIGN_OR_RETURN(
             sr2_key_tag,
             GetTagFromIFD(TIFFTagId::SONY_SR2_SUB_IFD_KEY,
                           TIFF_IFD_TYPE_DNG_PRIVATE_DATA),
             VLOG(2) << "Error getting SR2 key tag from DNG private data.");
         // This field is marked as data_count = 4 even though it's a
         // single uint32_t offset.
         CHECK_EQ(sr2_key_tag->GetDataCount(), 4U);
         CHECK_EQ(static_cast<int>(sr2_key_tag->GetDataType()),
                  static_cast<int>(TIFFTagDataType::UNDEF));

         uint32_t sr2_key;
         ASSIGN_OR_RETURN(sr2_key, sr2_key_tag->GetDataOffsetAsValue(),
                          VLOG(2) << "Error getting SR2 key value from tag.");

         ASSIGN_OR_RETURN(
             decrypted_sr2_data_,
             decrypt_sr2(*data_accessor_, sr2_offset, sr2_length, sr2_key),
             VLOG(2) << "Failed to decrypt SR2 data.");

         sr2_data_accessor_ = std::make_unique<MemoryDataAccessor>(
             reinterpret_cast<uint8_t*>(decrypted_sr2_data_.get()), sr2_length,
             /*relocation offset*/ sr2_offset);
         TIFFIFD sr2_ifd(sr2_offset, sr2_data_accessor_.get());
         list_entry.ifd = std::move(sr2_ifd);

         VLOG(1) << "SR2 IFD decrypted.";
         sr2_decrypted_ = true;
      }
   }

   return TIFFFileParser::GetIFDAtIndex(idx);
}

}  // namespace cmrw
