#ifndef _IMAGEMETA_H_
#define _IMAGEMETA_H_

#include <string>
#include <variant>
#include <vector>

#include "absl/container/flat_hash_map.h"
#include "absl/status/statusor.h"
#include "glog/logging.h"

#include "camraw/colorspace.h"

namespace cmrw {

enum class ImageMetaId {
   IMAGE_WIDTH = 0,
   IMAGE_HEIGHT = 1,
   ORIENTATION = 2,
   BITS_PER_SAMPLE = 3,
   SAMPLES_PER_PIXEL = 4,
   BLACK_LEVEL = 5,
   WHITE_LEVEL = 6,
   CFA_PATTERN_DIM = 7,
   CFA_PATTERN = 8,
   CROP_ORIGIN = 9,
   CROP_SIZE = 10,
   WHITE_BALANCE_MULTIPLIERS = 11,
   COMPRESSION = 12,
   PHOTOMETRIC_INTERPRETATION = 13,
   CAMERA_COLOR_MATRIX = 14,
   COLOR_SPACE = 15,
   CAMERA_MAKE = 16,
   CAMERA_MODEL = 17,
   MAX = 18,
};

enum class ImageMetaCompression : uint16_t {
   UNCOMPRESSED = 0,
   JPEG = 1,
   PROPRIETARY = 2,
};

enum class ImageMetaPhotoInt : uint16_t {
   GRAYSCALE = 0,
   RGB = 1,
   YCBCR = 2,
   CFA = 3,
   LINEAR_RAW = 4,
};

class ImageMeta {
      using variant_type = std::vector<
          std::variant<uint16_t, uint32_t, int16_t, int32_t, float, std::string,
                       Colorspace, ImageMetaCompression, ImageMetaPhotoInt>>;

   public:
      bool IsSet(ImageMetaId) const;
      void Clear(ImageMetaId);

      template <typename T>
      void SetOne(ImageMetaId id, T value) {
         CHECK_LT(static_cast<int>(id), static_cast<int>(ImageMetaId::MAX));
         ImageMetaDef def = image_meta_registry_[static_cast<size_t>(id)];
         CHECK_EQ(def.count, 1);
         CHECK(ValidateType<T>(def));

         auto it = map_.find(id);
         if (it == map_.end()) {
            map_.insert({id, {value}});
         } else {
            it->second[0] = value;
         }
      }

      template <typename T>
      absl::StatusOr<T> GetOne(ImageMetaId id) const {
         CHECK_LT(static_cast<int>(id), static_cast<int>(ImageMetaId::MAX));
         ImageMetaDef def = image_meta_registry_[static_cast<size_t>(id)];
         CHECK_EQ(def.count, 1);
         CHECK(ValidateType<T>(def));

         auto it = map_.find(id);
         if (it == map_.end()) {
            return absl::NotFoundError("Not Found");
         } else {
            return std::get<T>(it->second[0]);
         }
      }

      template <typename T>
      void SetMulti(ImageMetaId id, std::vector<T>& values) {
         CHECK_LT(static_cast<int>(id), static_cast<int>(ImageMetaId::MAX));
         ImageMetaDef def = image_meta_registry_[static_cast<size_t>(id)];
         CHECK_GT(def.count, 1);
         CHECK_EQ(values.size(), def.count);
         CHECK(ValidateType<T>(def));

         variant_type new_vec;
         for (auto value : values) {
            new_vec.push_back(value);
         }

         auto it = map_.find(id);
         if (it == map_.end()) {
            map_.insert({id, new_vec});
         } else {
            it->second = new_vec;
         }
      }

      template <typename T>
      absl::StatusOr<std::vector<T>> GetMulti(ImageMetaId id) const {
         CHECK_LT(static_cast<int>(id), static_cast<int>(ImageMetaId::MAX));
         ImageMetaDef def = image_meta_registry_[static_cast<size_t>(id)];
         CHECK_GT(def.count, 1);
         CHECK(ValidateType<T>(def));

         auto it = map_.find(id);
         if (it == map_.end()) {
            return absl::NotFoundError("Not Found");
         } else {
            // build a non-variant vector to return
            auto variant_vec = it->second;
            std::vector<T> result;
            result.reserve(variant_vec.size());
            for (auto variant_elem : variant_vec) {
               result.push_back(std::get<T>(variant_elem));
            }
            return result;
         }
      }

   private:
      enum class ImageMetaType {
         UINT16,
         INT16,
         UINT32,
         INT32,
         FLOAT,
         STRING,
         COMPRESSION,
         COLORSPACE,
         PHOTO_INT,
      };

      struct ImageMetaDef {
            ImageMetaId id;
            ImageMetaType type;
            uint16_t count;
      };

      // This has to follow the same order as ImageMetaId
      // clang-format off
      static constexpr ImageMetaDef image_meta_registry_[] = {
         /*    id                                  type                           count*/
         {ImageMetaId::IMAGE_WIDTH,                ImageMetaType::UINT16,         1},
         {ImageMetaId::IMAGE_HEIGHT,               ImageMetaType::UINT16,         1},
         {ImageMetaId::ORIENTATION,                ImageMetaType::UINT16,         1},
         {ImageMetaId::BITS_PER_SAMPLE,            ImageMetaType::UINT16,         1},
         {ImageMetaId::SAMPLES_PER_PIXEL,          ImageMetaType::UINT16,         1},
         {ImageMetaId::BLACK_LEVEL,                ImageMetaType::UINT16,         1},
         {ImageMetaId::WHITE_LEVEL,                ImageMetaType::UINT16,         1},
         {ImageMetaId::CFA_PATTERN_DIM,            ImageMetaType::UINT16,         2},
         {ImageMetaId::CFA_PATTERN,                ImageMetaType::UINT16,         4},
         {ImageMetaId::CROP_ORIGIN,                ImageMetaType::UINT32,         2},
         {ImageMetaId::CROP_SIZE,                  ImageMetaType::UINT32,         2},
         {ImageMetaId::WHITE_BALANCE_MULTIPLIERS,  ImageMetaType::FLOAT,          3},
         {ImageMetaId::COMPRESSION,                ImageMetaType::COMPRESSION,    1},
         {ImageMetaId::PHOTOMETRIC_INTERPRETATION, ImageMetaType::PHOTO_INT,      1},
         {ImageMetaId::CAMERA_COLOR_MATRIX,        ImageMetaType::FLOAT,          9},
         {ImageMetaId::COLOR_SPACE,                ImageMetaType::COLORSPACE,     1},
         {ImageMetaId::CAMERA_MAKE,                ImageMetaType::STRING,         1},
         {ImageMetaId::CAMERA_MODEL,               ImageMetaType::STRING,         1},
      };
      // clang-format on

      template <typename T>
      bool ValidateType(ImageMetaDef def) const {
         switch (def.type) {
            case ImageMetaType::UINT16:
               return std::is_same<T, uint16_t>::value;
            case ImageMetaType::UINT32:
               return std::is_same<T, uint32_t>::value;
            case ImageMetaType::INT16:
               return std::is_same<T, int16_t>::value;
            case ImageMetaType::INT32:
               return std::is_same<T, int32_t>::value;
            case ImageMetaType::FLOAT:
               return std::is_same<T, float>::value;
            case ImageMetaType::STRING:
               return std::is_same<T, std::string>::value;
            case ImageMetaType::COLORSPACE:
               return std::is_same<T, Colorspace>::value;
            case ImageMetaType::COMPRESSION:
               return std::is_same<T, ImageMetaCompression>::value;
            case ImageMetaType::PHOTO_INT:
               return std::is_same<T, ImageMetaPhotoInt>::value;
            default:
               return false;
         }
      }

      absl::flat_hash_map<ImageMetaId, variant_type> map_;
};

}  // namespace cmrw

#endif /* _IMAGEMETA_H_ */
