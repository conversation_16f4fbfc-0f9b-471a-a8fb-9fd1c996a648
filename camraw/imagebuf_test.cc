#include "gtest/gtest.h"

#include "camraw/imagebuf.h"

using cmrw::ImageBuf;

TEST(ImageBufTest, SingleAssignmentTest) {
   ImageBuf<uint16_t> img(3, 4);
   EXPECT_EQ(img.get_height(), 3);
   EXPECT_EQ(img.get_width(), 4);
   EXPECT_EQ(img.get_components(), 1);

   uint16_t val = 0;
   for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 4; j++) {
         img(i, j) = val++;
      }
   }

   val = 0;
   for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 4; j++) {
         EXPECT_EQ(img(i, j), val++);
      }
   }
}

TEST(ImageBufTest, OutOfBoundsAssignmentTest) {
   ImageBuf<uint16_t> img(3, 4);
   EXPECT_EQ(img.get_height(), 3);
   EXPECT_EQ(img.get_width(), 4);
   EXPECT_EQ(img.get_components(), 1);

   EXPECT_DEATH({ img(3, 0) = 1; }, "Assertion `row < height_' failed.");

   EXPECT_DEATH({ img(0, 4) = 1; }, "Assertion `col < width_' failed.");
}

TEST(ImageBufTest, PtrAssignmentTest) {
   ImageBuf<uint16_t> img(3, 4);

   uint16_t* ptr = img.get_ptr(1, 3);
   *ptr = 7;
   EXPECT_EQ(img(1, 3), 7);

   img(1, 3) = 8;
   EXPECT_EQ(*ptr, 8);
}

TEST(ImageBufTest, CompSingleAssignmentTest) {
   ImageBuf<uint16_t> img(3, 4, 3);
   EXPECT_EQ(img.get_height(), 3);
   EXPECT_EQ(img.get_width(), 4);
   EXPECT_EQ(img.get_components(), 3);

   uint16_t val = 0;
   for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 4; j++) {
         for (int c = 0; c < 3; c++) {
            img(i, j, c) = val++;
         }
      }
   }

   val = 0;
   for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 4; j++) {
         for (int c = 0; c < 3; c++) {
            EXPECT_EQ(img(i, j, c), val++);
         }
      }
   }
}

TEST(ImageBufTest, CompOutOfBoundsAssignmentTest) {
   ImageBuf<uint16_t> img(3, 4, 3);
   EXPECT_EQ(img.get_height(), 3);
   EXPECT_EQ(img.get_width(), 4);
   EXPECT_EQ(img.get_components(), 3);

   EXPECT_DEATH({ img(3, 0, 0) = 1; }, "Assertion `row < height_' failed.");

   EXPECT_DEATH({ img(0, 4, 0) = 1; }, "Assertion `col < width_' failed.");

   EXPECT_DEATH(
       { img(0, 0, 3) = 1; }, "Assertion `comp < components_' failed.");
}

TEST(ImageBufTest, CompPtrAssignmentTest) {
   ImageBuf<uint16_t> img(3, 4, 3);
   EXPECT_EQ(img.get_height(), 3);
   EXPECT_EQ(img.get_width(), 4);
   EXPECT_EQ(img.get_components(), 3);

   uint16_t* ptr = img.get_ptr(1, 3, 1);
   *ptr = 7;
   EXPECT_EQ(img(1, 3, 1), 7);

   img(1, 3, 1) = 8;
   EXPECT_EQ(*ptr, 8);
}

TEST(ImageBufTest, ReferenceBufferTest) {
   uint16_t myArr[] = {1, 2, 3, 4, 5, 6, 7, 8, 9};
   ImageBuf<uint16_t> img(myArr, /*take_ownership*/ false, 3, 3);
   EXPECT_EQ(img.get_height(), 3);
   EXPECT_EQ(img.get_width(), 3);
   EXPECT_EQ(img.get_components(), 1);

   uint16_t val = 1;
   for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 3; j++) {
         EXPECT_EQ(img(i, j), val++);
      }
   }
}

TEST(ImageBufTest, TakeBufferOwnershipTest) {
   uint16_t* myArr = new uint16_t[9]{1, 2, 3, 4, 5, 6, 7, 8, 9};
   ImageBuf<uint16_t> img(myArr, /*take_ownership*/ true, 3, 3);
   EXPECT_EQ(img.get_height(), 3);
   EXPECT_EQ(img.get_width(), 3);
   EXPECT_EQ(img.get_components(), 1);

   uint16_t val = 1;
   for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 3; j++) {
         EXPECT_EQ(img(i, j), val++);
      }
   }
}

TEST(ImageBufTest, CopyTileInFromTestLocalOffset) {
   ImageBuf<uint16_t> img(3, 4);

   uint16_t val = 0;
   for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 4; j++) {
         img(i, j) = val++;
      }
   }

   ImageBuf<uint16_t> img2(6, 8);
   img2.copy_tile_in_from(img, 3, 4);

   val = 0;
   for (int i = 3; i < 6; i++) {
      for (int j = 4; j < 8; j++) {
         EXPECT_EQ(img2(i, j), val++);
      }
   }
}

TEST(ImageBufTest, CopyTileInFromTestFullCopy) {
   ImageBuf<uint16_t> img(3, 4);

   uint16_t val = 0;
   for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 4; j++) {
         img(i, j) = val++;
      }
   }

   ImageBuf<uint16_t> img2(3, 4);
   img2.copy_tile_in_from(img);

   val = 0;
   for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 4; j++) {
         EXPECT_EQ(img2(i, j), val++);
      }
   }
}

TEST(ImageBufTest, CopyTileInFromTestInputOffset) {
   ImageBuf<uint16_t> img(4, 4);

   uint16_t val = 0;
   for (int i = 0; i < 4; i++) {
      for (int j = 0; j < 4; j++) {
         img(i, j) = val++;
      }
   }

   ImageBuf<uint16_t> img2(2, 2);
   img2.copy_tile_in_from(img, /*local_row*/ 0, /*local_col*/ 0,
                          /*input_row*/ 2, /*input_col*/ 2);

   EXPECT_EQ(img2(0, 0), 10);
   EXPECT_EQ(img2(0, 1), 11);
   EXPECT_EQ(img2(1, 0), 14);
   EXPECT_EQ(img2(1, 1), 15);
}
