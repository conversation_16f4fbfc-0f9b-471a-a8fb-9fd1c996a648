#include <fstream>

#include "absl/status/status.h"
#include "absl/strings/str_cat.h"

#include "camraw/file_data_accessor.h"

namespace cmrw {

absl::Status
FileDataAccessor::CopyBytes(uint32_t read_offset, uint32_t num_bytes,
                            void* dst) {
   file_.seekg(read_offset);
   if (file_.fail()) {
      return absl::OutOfRangeError(
          absl::StrCat("File seek returned: ", std::strerror(errno)));
   }

   file_.read(reinterpret_cast<char*>(dst), num_bytes);
   if (file_.fail()) {
      return absl::OutOfRangeError(
          absl::StrCat("File read returned: ", std::strerror(errno)));
   }

   return absl::OkStatus();
}

}  // namespace cmrw
