#ifndef FILE_PARSER_H_
#define FILE_PARSER_H_

#include "absl/status/statusor.h"

#include "camraw/data_accessor.h"

namespace cmrw {

class FileParser {
   public:
      FileParser(DataAccessor& data_accessor)
          : data_accessor_(&data_accessor) {}

      /*
       * Returns true if the given DataAccessor contains an appopriate file
       * for this parser. Returns false if the file is conclusively not an
       * appropriate file for this parser. Returns errors via absl::Status
       * on error cases.
       *
       * Implementations of ValidateFile are intended to be lightweight,
       * doing the minimum possible amount of work to ascertain the validity
       * of the given file.
       */
      virtual absl::StatusOr<bool> ValidateFile() = 0;

      /*
       * Initialize the parser enough to be able to return answers to the
       * rest of the API calls here and declared in subclasses.
       */
      virtual absl::Status Init() = 0;

      // Virtual destructor so delete on subclasses calls their destructors.
      virtual ~FileParser() = default;

      DataAccessor* GetDataAccessor() { return data_accessor_; }

   protected:
      DataAccessor* data_accessor_;
};

}  // namespace cmrw

#endif /* FILE_PARSER_H_ */
