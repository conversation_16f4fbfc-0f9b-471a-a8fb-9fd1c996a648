#ifndef PIPELINE_ACCUMULATOR_H_
#define PIPELINE_ACCUMULATOR_H_

#include <functional>

#include "absl/status/status.h"

#include "camraw/imagebuf.h"

namespace cmrw {

class PipelineAccumulator {
   public:
      PipelineAccumulator(uint16_t image_height, uint16_t image_width,
                          uint16_t components = 1)
          : image_height_(image_height),
            image_width_(image_width),
            components_(components),
            area_(0) {
         buf_ = std::make_unique<ImageBuf<uint16_t>>(image_height_,
                                                     image_width_, components_);
      };

      absl::Status Accumulate(uint16_t y_offset, uint16_t x_offset,
                              const ImageBuf<uint16_t>& data);
      bool IsComplete();
      std::unique_ptr<ImageBuf<uint16_t>> ReleaseBuffer();
      void RegisterCompletionFunc(
          std::function<absl::Status(const ImageBuf<uint16_t>&)> func);

   private:
      uint16_t image_height_;
      uint16_t image_width_;
      uint16_t components_;
      std::unique_ptr<ImageBuf<uint16_t>> buf_;
      uint32_t area_;
      std::function<absl::Status(const ImageBuf<uint16_t>&)> completion_func_;
};

}  // namespace cmrw

#endif  // PIPELINE_ACCUMULATOR_H_
