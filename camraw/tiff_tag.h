#ifndef TIFF_TAG_H_
#define TIFF_TAG_H_

#include <cassert>
#include <cstdint>
#include <functional>
#include <memory>
#include <string>
#include <string_view>
#include <type_traits>

#include "absl/status/status.h"
#include "absl/status/statusor.h"
#include "absl/strings/str_cat.h"

#include "camraw/data_accessor.h"
#include "camraw/endian.h"
#include "camraw/status_macros.h"
#include "camraw/tiff_tag_id.h"

namespace cmrw {

#define TIFF_TAG_MAX_DATA_VAL_TO_PRINT_DEFAULT 10

enum class TIFFTagDataType {
   BYTE = 1,
   STRING = 2,
   SHORT = 3,
   LONG = 4,
   RATIONAL = 5,
   SINT = 6,
   UNDEF = 7,
   SSHORT = 8,
   SLONG = 9,
   SRATIONAL = 10,
   FLOAT = 11,
   DOUBLE = 12,
   MAX = 13,
};

size_t TIFFTagDataTypeByteLength(TIFFTagDataType type);
std::string_view TIFFTagDataTypeToString(TIFFTagDataType type);

struct __attribute__((packed)) TIFFTagWireFormat {
      uint16_t id;
      uint16_t data_type;
      uint32_t data_count;
      uint32_t data_offset;

      TIFFTagWireFormat() = default;

      TIFFTagWireFormat(uint16_t tag_id, uint16_t tag_data_type,
                        uint32_t tag_data_count, uint32_t tag_data_offset)
          : id(tag_id),
            data_type(tag_data_type),
            data_count(tag_data_count),
            data_offset(tag_data_offset) {}

      TIFFTagWireFormat(TIFFTagId tag_id, TIFFTagDataType tag_data_type,
                        uint32_t tag_data_count, uint32_t tag_data_offset)
          : id(static_cast<uint16_t>(tag_id)),
            data_type(static_cast<uint16_t>(tag_data_type)),
            data_count(tag_data_count),
            data_offset(tag_data_offset) {}
};

#define TAG_DATA_ACCESSOR_GET_OR_RETURN(offset, type, varName)            \
   do {                                                                   \
      RETURN_IF_ERROR(data_accessor_->CopyBytes(                          \
          /*src*/ offset, /*num_bytes*/ sizeof(type), /*dst*/ &varName)); \
      if (endianness_ == ENDIANNESS_LITTLE) {                             \
         varName = little_endian<type>(varName);                          \
      } else {                                                            \
         varName = big_endian<type>(varName);                             \
      }                                                                   \
   } while (0)

class TIFFTag {
   public:
      TIFFTag(TIFFTagId id, TIFFTagDataType data_type, uint32_t data_count,
              uint32_t data_offset, DataAccessor* data_accessor = nullptr,
              Endianness endianness = ENDIANNESS_LITTLE)
          : id_(id),
            data_type_(data_type),
            data_count_(data_count),
            data_offset_(data_offset),
            data_accessor_(data_accessor),
            endianness_(endianness) {}
      TIFFTag(TIFFTagWireFormat* tag_data,
              DataAccessor* data_accessor = nullptr,
              Endianness endianness = ENDIANNESS_LITTLE)
          : data_accessor_(data_accessor), endianness_(endianness) {
         if (endianness == ENDIANNESS_LITTLE) {
            id_ = static_cast<TIFFTagId>(little_endian<uint16_t>(tag_data->id));
            data_type_ = static_cast<TIFFTagDataType>(
                little_endian<uint16_t>(tag_data->data_type));
            data_count_ = little_endian<uint32_t>(tag_data->data_count);
            data_offset_ = little_endian<uint32_t>(tag_data->data_offset);
         } else {
            id_ = static_cast<TIFFTagId>(big_endian<uint16_t>(tag_data->id));
            data_type_ = static_cast<TIFFTagDataType>(
                big_endian<uint16_t>(tag_data->data_type));
            data_count_ = big_endian<uint32_t>(tag_data->data_count);
            data_offset_ = big_endian<uint32_t>(tag_data->data_offset);
         }
      }

      // Override the Tag ID -> string mapping
      static std::function<const std::string(TIFFTagId)> TagIdToStringFn;
      static const std::string TagIdToStringDefault(TIFFTagId tag_id);

      TIFFTagId GetId() const { return id_; };
      TIFFTagDataType GetDataType() const { return data_type_; };
      uint32_t GetDataCount() const { return data_count_; };

      // Maximum data value per tag that will be printed by PrettyPrint() etc.
      static size_t MaxDataValuesToPrint;

      const std::string DataToString() const;
      const std::string DebugString() const;
      const std::string PrettyPrint() const;

      // Get tag data, requires data count to be exactly one.
      template <typename T>
      absl::StatusOr<T> GetDataValue() const {
         if (data_count_ > 1) {
            return absl::FailedPreconditionError(absl::StrCat(
                "GetDataValue() requires data_count to be 1, but it is ",
                data_count_));
         }

         return GetDataValueAtIdx<T>(0);
      };
      /*
       * Get tag data at index (out of data_count). Works both for tag data
       * contained within the data_offset field as well as at an external
       * offset.
       */
      template <typename T>
      absl::StatusOr<T> GetDataValueAtIdx(uint16_t index) const {
         if (index >= data_count_) {
            return absl::FailedPreconditionError(absl::StrCat(
                "GetDataValueAtIdx() index out of bounds. Index: ", index,
                " data count: ", data_count_));
         }

         size_t type_len = TypeByteLength();
         assert(type_len > 0);

         RETURN_IF_ERROR(ValidateOutputType<T>());

         if (IsTagDataInOffsetField()) {
            if (endianness_ == ENDIANNESS_LITTLE) {
               /*
                * Shift data offset index * TypeByteLength to the right
                *
                * For output, mask data_offset based on:
                * TIFFTagDataTypeByteLength == 1 -> 0x000000FF
                * TIFFTagDataTypeByteLength == 2 -> 0x0000FFFF
                * TIFFTagDataTypeByteLength == 4 -> 0xFFFFFFFF
                */
               return static_cast<T>((data_offset_ >> (index * type_len * 8)) &
                                     (0xFFFFFFFF >> ((4 - type_len) * 8)));
            } else {
               /*
                * Shift data offset (3-index) * TypeByteLength to the right
                *
                * For output, mask data_offset based on:
                * TIFFTagDataTypeByteLength == 1 -> 0x000000FF
                * TIFFTagDataTypeByteLength == 2 -> 0x0000FFFF
                * TIFFTagDataTypeByteLength == 4 -> 0xFFFFFFFF
                */
               return static_cast<T>(
                   (data_offset_ >> ((3 - index) * type_len * 8)) &
                   (0xFFFFFFFF >> ((4 - type_len) * 8)));
            }
         } else {
            if (data_accessor_ == nullptr) {
               return absl::NotFoundError("TIFF tag missing data accessor");
            }

            if (IsRationalType()) {
               int32_t numerator, denominator;
               TAG_DATA_ACCESSOR_GET_OR_RETURN(
                   data_offset_ + (index * type_len), int32_t, numerator);
               TAG_DATA_ACCESSOR_GET_OR_RETURN(
                   data_offset_ + (index * type_len) + sizeof(int32_t), int32_t,
                   denominator);
               return static_cast<T>(numerator) / denominator;
            } else {
               if (type_len == 1) {
                  uint8_t result;
                  TAG_DATA_ACCESSOR_GET_OR_RETURN(data_offset_ + index, uint8_t,
                                                  result);
                  return static_cast<T>(result);
               } else if (type_len == 2) {
                  uint16_t result;
                  TAG_DATA_ACCESSOR_GET_OR_RETURN(data_offset_ + (index * 2),
                                                  uint16_t, result);
                  return static_cast<T>(result);
               } else if (type_len == 4) {
                  uint32_t result;
                  TAG_DATA_ACCESSOR_GET_OR_RETURN(data_offset_ + (index * 4),
                                                  uint32_t, result);
                  return static_cast<T>(result);
               } else if (type_len == 8) {
                  uint64_t result;
                  TAG_DATA_ACCESSOR_GET_OR_RETURN(data_offset_ + (index * 8),
                                                  uint64_t, result);
                  return static_cast<T>(result);
               } else {
                  return absl::UnimplementedError("Unsupported type length");
               }
            }
         }
      };
      /*
       * Return a buffer containing all data values for this tag
       */
      template <typename T>
      absl::StatusOr<std::unique_ptr<T[]>> GetAllDataValues() const {
         std::unique_ptr<T[]> result = std::make_unique<T[]>(data_count_);

         RETURN_IF_ERROR(CopyAllDataValues<T>(result.get()));

         return result;
      };
      /*
       * Copy all data values for this tag to the given buffer
       */
      template <typename T>
      absl::Status CopyAllDataValues(T* dest) const {
         RETURN_IF_ERROR(ValidateOutputType<T>());

         size_t type_len = TypeByteLength();

         // Optimize if possible - direct copy from external offset
         if (sizeof(T) == type_len && !IsRationalType() &&
             !IsTagDataInOffsetField() &&
             SAME_ENDIANNESS_AS_ARCH(endianness_)) {
            if (data_accessor_ == nullptr) {
               return absl::NotFoundError("TIFF tag missing data accessor");
            }
            return data_accessor_->CopyBytes(
                /*src*/ data_offset_,
                /*num_bytes*/ data_count_ * type_len,
                /*dst*/ dest);
         }

         // Fallback, copy one at a time via for loop
         for (unsigned int i = 0; i < data_count_; i++) {
            T value;
            ASSIGN_OR_RETURN(value, GetDataValueAtIdx<T>(i));
            dest[i] = value;
         }

         return absl::OkStatus();
      };
      // Return the data offset contents as a value
      absl::StatusOr<uint32_t> GetDataOffsetAsValue() const;

   private:
      // Calculate whether the tag data is embedded in the data_offset field
      bool IsTagDataInOffsetField() const {
         return (TIFFTagDataTypeByteLength(data_type_) * data_count_) <= 4;
      };
      // Whether the data type for this tag is a rational type
      bool IsRationalType() const {
         return (data_type_ == TIFFTagDataType::RATIONAL ||
                 data_type_ == TIFFTagDataType::SRATIONAL);
      };
      size_t TypeByteLength() const {
         return TIFFTagDataTypeByteLength(data_type_);
      }
      template <typename T>
      absl::Status ValidateOutputType() const {
         if (IsRationalType()) {
            if (!std::is_same<T, float>::value &&
                !std::is_same<T, double>::value) {
               return absl::FailedPreconditionError(
                   "Rational tag requires float or double type");
            }
         } else if (sizeof(T) < TypeByteLength()) {
            return absl::FailedPreconditionError(absl::StrCat(
                "Function called with a type that's too small. Required: ",
                TypeByteLength(), " Got: ", sizeof(T)));
         }

         return absl::OkStatus();
      }
      const std::string DataValueAtIdxToString(uint16_t idx) const;

      TIFFTagId id_;
      TIFFTagDataType data_type_;
      uint32_t data_count_;
      uint32_t data_offset_;
      DataAccessor* data_accessor_;
      Endianness endianness_;
};

}  // namespace cmrw

#endif  // TIFF_TAG_H_
