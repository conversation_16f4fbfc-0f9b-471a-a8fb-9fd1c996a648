#include <algorithm>
#include <cassert>
#include <memory>
#include <thread>

#include "absl/status/status.h"

#include "camraw/pipeline_task.h"
#include "camraw/pipeline_task_runner.h"

namespace cmrw {

PipelineTaskRunner::PipelineTaskRunner(unsigned int num_threads)
    : num_threads_(num_threads),
      threads_(),
      stop_running_(false),
      highest_stage_id_(0),
      completed_stages_(),
      running_tasks_count_(),
      runnable_tasks_(),
      runnable_tasks_lock_(),
      runnable_tasks_cond_(),
      completed_tasks_(),
      completed_tasks_lock_(),
      completed_tasks_cond_() {
   // Special case num_threads = 0 means use all available cores
   if (num_threads_ == 0) {
      num_threads_ = std::thread::hardware_concurrency();
   }

   threads_.reserve(num_threads_);

   for (unsigned int i = 0; i < num_threads_; i++) {
      threads_.push_back(
          std::thread(&PipelineTaskRunner::PipelineTaskRunnerWorker, this));
   }
}

PipelineTaskRunner::~PipelineTaskRunner() {
   // Signal threads to stop
   {
      std::unique_lock<std::mutex> lock(runnable_tasks_lock_);
      stop_running_ = true;
   }
   runnable_tasks_cond_.notify_all();

   // Join all threads to ensure they exit
   for (std::thread& thread : threads_) {
      thread.join();
   }
   threads_.clear();  // destroy the threads
}

void
PipelineTaskRunner::EnqueueRunnableTask(std::unique_ptr<PipelineTask> task,
                                        int stage_id) {
   {
      std::unique_lock<std::mutex> lock(runnable_tasks_lock_);

      // Enforce monotonically increasing stage_id
      // Assert that we're not enqueueing a task for a previous stage
      assert(stage_id >= highest_stage_id_ &&
             "Cannot enqueue a task for a previous stage");

      // Update highest_stage_id if needed
      highest_stage_id_ = std::max(highest_stage_id_, stage_id);

      // Initialize running_tasks_count_ for this stage if it doesn't exist
      if (running_tasks_count_.find(stage_id) == running_tasks_count_.end()) {
         running_tasks_count_[stage_id] = 0;
      }

      // Add the task to the appropriate stage queue
      runnable_tasks_[stage_id].push(std::move(task));
   }
   runnable_tasks_cond_.notify_one();
}

std::pair<absl::Status, std::unique_ptr<PipelineTask>>
PipelineTaskRunner::DequeueCompletedTask(int stage_id) {
   {
      std::unique_lock<std::mutex> lock(completed_tasks_lock_);

      // Wait until there's a completed task for the requested stage or we're
      // stopping
      completed_tasks_cond_.wait(lock, [this, stage_id]() {
         return (completed_tasks_.find(stage_id) != completed_tasks_.end() &&
                 !completed_tasks_[stage_id].empty()) ||
                stop_running_;
      });

      if (stop_running_) {
         return std::make_pair(absl::CancelledError(), nullptr);
      }

      // Get the completed task from the appropriate stage queue
      auto completed_task_pair = std::move(completed_tasks_[stage_id].front());
      completed_tasks_[stage_id].pop();

      return completed_task_pair;
   }
}

void
PipelineTaskRunner::MarkStageAsComplete(int stage_id) {
   std::unique_lock<std::mutex> lock(runnable_tasks_lock_);
   completed_stages_.insert(stage_id);
}

bool
PipelineTaskRunner::StageHasPendingTasks(int stage_id) {
   std::unique_lock<std::mutex> lock1(runnable_tasks_lock_);
   std::unique_lock<std::mutex> lock2(completed_tasks_lock_);

   // Check if there are runnable tasks for this stage
   bool has_runnable =
       runnable_tasks_.find(stage_id) != runnable_tasks_.end() &&
       !runnable_tasks_[stage_id].empty();

   // Check if there are running tasks for this stage
   bool has_running =
       running_tasks_count_.find(stage_id) != running_tasks_count_.end() &&
       running_tasks_count_[stage_id] > 0;

   // Check if there are completed but not yet dequeued tasks for this stage
   bool has_completed =
       completed_tasks_.find(stage_id) != completed_tasks_.end() &&
       !completed_tasks_[stage_id].empty();

   return has_runnable || has_running || has_completed;
}

int
PipelineTaskRunner::GetEarliestRunnableStage() {
   // This function should be called with runnable_tasks_lock_ already held

   // Find the earliest stage that has runnable tasks
   for (int stage = 0; stage <= highest_stage_id_; stage++) {
      if (runnable_tasks_.find(stage) != runnable_tasks_.end() &&
          !runnable_tasks_[stage].empty()) {
         return stage;
      }
   }

   // If no stage has runnable tasks, return the highest stage_id + 1 (invalid
   // stage)
   return highest_stage_id_ + 1;
}

void
PipelineTaskRunner::PipelineTaskRunnerWorker() {
   while (true) {
      std::unique_ptr<PipelineTask> task;
      int task_stage_id = 0;

      // Get the next runnable task locking the mutex + waiting on cond
      {
         std::unique_lock<std::mutex> lock(runnable_tasks_lock_);

         // Wait until there are runnable tasks or we're stopping
         runnable_tasks_cond_.wait(lock, [this]() {
            // Check if any stage has runnable tasks
            for (const auto& stage_queue : runnable_tasks_) {
               if (!stage_queue.second.empty()) {
                  return true;
               }
            }
            return stop_running_;
         });

         if (stop_running_) {
            return;
         }

         // Get the earliest stage that has runnable tasks
         int stage_id = GetEarliestRunnableStage();

         // If no stage has runnable tasks, check if there are later stages with
         // tasks where earlier stages are marked as complete
         if (stage_id > highest_stage_id_) {
            for (int s = 0; s <= highest_stage_id_; s++) {
               // If this stage is marked complete and has no runnable tasks,
               // check the next stage
               if (completed_stages_.find(s) != completed_stages_.end() &&
                   (runnable_tasks_.find(s) == runnable_tasks_.end() ||
                    runnable_tasks_[s].empty())) {
                  continue;
               }

               // If this stage has runnable tasks, use it
               if (runnable_tasks_.find(s) != runnable_tasks_.end() &&
                   !runnable_tasks_[s].empty()) {
                  stage_id = s;
                  break;
               }
            }
         }

         // If we still don't have a valid stage, wait for more tasks
         if (stage_id > highest_stage_id_) {
            continue;
         }

         // Get the task from the selected stage
         task = std::move(runnable_tasks_[stage_id].front());
         runnable_tasks_[stage_id].pop();
         task_stage_id = stage_id;

         // Increment the running task count for this stage
         running_tasks_count_[stage_id]++;
      }

      // Run the task
      absl::Status status = task->Run();

      // Insert the completed task into the appropriate completed queue
      {
         std::unique_lock<std::mutex> lock(completed_tasks_lock_);
         completed_tasks_[task_stage_id].push(
             std::make_pair(status, std::move(task)));
      }
      // Decrement the running task count for the task's stage
      {
         std::unique_lock<std::mutex> lock(runnable_tasks_lock_);
         running_tasks_count_[task_stage_id]--;
      }

      completed_tasks_cond_.notify_all();
   }
}

}  // namespace cmrw
