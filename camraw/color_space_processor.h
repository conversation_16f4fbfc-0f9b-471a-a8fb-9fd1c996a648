#ifndef COLOR_SPACE_PROCESSOR_H_
#define COLOR_SPACE_PROCESSOR_H_

#include <memory>

#include "absl/status/status.h"

#include "camraw/colorspace.h"
#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/matrix.h"
#include "camraw/pipeline_processor.h"

namespace cmrw {

class ColorSpaceProcessor : public PipelineProcessor {
   public:
      ColorSpaceProcessor(Colorspace from_space, Colorspace to_space)
          : PipelineProcessor("ColorSpaceProcessor",
                              /*input_spec*/
                              {.one_sample_per_pixel = false,
                               .three_samples_per_pixel = true},
                              /*output_spec*/
                              {.one_sample_per_pixel = false,
                               .three_samples_per_pixel = true},
                              /*required metadata*/
                              {
                                  ImageMetaId::CAMERA_COLOR_MATRIX,
                                  ImageMetaId::COLOR_SPACE,
                              },
                              /*optional metadata*/ {}),
            from_space_(from_space),
            to_space_(to_space) {}
      absl::Status Init(const ImageMeta& image_meta) override;
      void LogStart() override;
      absl::Status Run(const ImageBuf<uint16_t>& input_buffer,
                       ImageBuf<uint16_t>& output_buffer) override;
      void LogStats() override;
      absl::Status MutateMetadata(ImageMeta& image_meta) override;

   private:
      Colorspace from_space_;
      Colorspace to_space_;
      std::unique_ptr<Matrix<float>> multiplier_matrix_ = nullptr;
      uint32_t out_of_gamut_ = 0;
};

}  // namespace cmrw

#endif /* COLOR_SPACE_PROCESSOR_H_ */
