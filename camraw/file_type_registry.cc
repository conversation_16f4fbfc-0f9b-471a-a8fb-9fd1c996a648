#include "absl/status/status.h"
#include "absl/status/statusor.h"

#include "camraw/file_type_registry.h"

namespace cmrw {

FileTypeRegistry&
FileTypeRegistry::GetRegistry() {
   static FileTypeRegistry registry;
   return registry;
}

absl::Status
FileTypeRegistry::InsertElement(FileTypeRegistryElement element) {
   auto it = element_map_.find(element.type_name);
   if (it == element_map_.end()) {
      element_map_.insert({element.type_name, element});
      ordered_type_names_.push_back(element.type_name);
   } else {
      return absl::FailedPreconditionError(
          "Element already exists in registry.");
   }
   return absl::OkStatus();
}

absl::StatusOr<const FileTypeRegistryElement*>
FileTypeRegistry::GetElement(const std::string type_name) const {
   auto it = element_map_.find(type_name);
   if (it == element_map_.end()) {
      return absl::NotFoundError("Element not found.");
   }

   return &it->second;
}

}  // namespace cmrw
