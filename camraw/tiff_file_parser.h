#ifndef TIFF_FILE_PARSER_H_
#define TIFF_FILE_PARSER_H_

#include <unordered_set>
#include <vector>

#include "absl/status/status.h"
#include "absl/status/statusor.h"

#include "camraw/data_accessor.h"
#include "camraw/endian.h"
#include "camraw/file_parser.h"
#include "camraw/status_macros.h"
#include "camraw/tiff_ifd.h"
#include "camraw/tiff_tag.h"
#include "camraw/tiff_tag_id.h"

namespace cmrw {

// XXX: this should be namespaced
#define TIFF_PARSER_ASSIGN_TAG_VAL_OR_RETURN(parser, type, varName, ifdType,  \
                                             tagId)                           \
   do {                                                                       \
      const TIFFTag* tag;                                                     \
      ASSIGN_OR_RETURN(tag, parser->GetTagFromIFD(tagId, ifdType),            \
                       VLOG(2)                                                \
                           << "Failed to get tag "                            \
                           << static_cast<uint16_t>(tagId) << " from file."); \
      ASSIGN_OR_RETURN(varName, tag->GetDataValue<type>(),                    \
                       VLOG(2) << "Failed to get value from tag "             \
                               << static_cast<uint16_t>(tagId));              \
   } while (0)

using TIFFIFDType = uint16_t;
constexpr TIFFIFDType TIFF_IFD_TYPE_UNKNOWN = 0;
constexpr TIFFIFDType TIFF_IFD_TYPE_PRIMARY_IMAGE = 1;
constexpr TIFFIFDType TIFF_IFD_TYPE_REDUCED_RES_IMAGE = 2;
constexpr TIFFIFDType TIFF_IFD_TYPE_EXIF = 3;
constexpr TIFFIFDType TIFF_IFD_TYPE_BASE_MAX =
    4;  // subclasses can extend starting at this value

class TIFFFileParser : public FileParser {
   public:
      TIFFFileParser(DataAccessor& data_accessor) : FileParser(data_accessor) {}

      absl::StatusOr<bool> ValidateFile() override;
      absl::Status Init() override;

      size_t GetNumIFDs() { return ifd_list_.size(); };
      virtual absl::StatusOr<const TIFFIFD*> GetIFDAtIndex(size_t idx);
      absl::StatusOr<const TIFFIFD*> GetIFD(TIFFIFDType ifd_type,
                                            size_t ifd_idx = 0);
      absl::StatusOr<std::vector<const TIFFIFD*>> GetAllIFDsOfType(
          TIFFIFDType ifd_type);
      absl::StatusOr<const TIFFTag*> GetTagFromIFD(TIFFTagId tagId,
                                                   TIFFIFDType ifd_type,
                                                   size_t ifd_idx = 0);
      Endianness GetEndianness() { return endianness_; }

   protected:
      enum class TIFFIFDListEntryStatus {
         DISCOVERED,
         SCANNED,
         PARSED,
         PARSING_FAILED,
      };
      struct TIFFIFDListEntry {
            uint32_t ifd_offset;
            TIFFIFDListEntryStatus status;
            TIFFIFDType type;
            TIFFIFD ifd;
      };
      std::unordered_set<TIFFTagId> TIFFNewIFDTagIDs = {
          TIFFTagId::SUB_IFDS,
          TIFFTagId::EXIF_IFD,
      };

      void InsertNewIFDListEntry(uint32_t offset, TIFFIFDListEntryStatus status,
                                 TIFFIFDType type, TIFFIFD& ifd);
      void BuildIFDListIndex();

      // Subclasses can override this to add more tag -> ifd type mappings
      virtual TIFFIFDType GetIFDTypeFromTag(const TIFFTag& tag);

      bool validated_ = false;
      bool initialized_ = false;

      std::vector<TIFFIFDListEntry> ifd_list_;
      absl::flat_hash_map<TIFFIFDType, std::vector<size_t>> ifd_list_index_;

      // Whether the TIFF file is little endian or big endian
      Endianness endianness_ = ENDIANNESS_LITTLE;
};

}  // namespace cmrw

#endif /* TIFF_FILE_PARSER_H_ */
