#ifndef _TIFF_TAG_ID_H_
#define _TIFF_TAG_ID_H_

#include <cstdint>

namespace cmrw {

// clang-format off
enum class TIFFTagId : uint16_t {
   NEW_SUBFILE_TYPE        = 0x00FE,
   IMAGE_WIDTH             = 0x0100,
   IMAGE_LENGTH            = 0x0101,
   BITS_PER_SAMPLE         = 0x0102,
   COMPRESSION             = 0x0103,
   PHOTOMETRIC_INT         = 0x0106,
   MAKE                    = 0x010F,
   MODEL                   = 0x0110,
   STRIP_OFFSETS           = 0x0111,
   ORIENTATION             = 0x0112,
   SAMPLES_PER_PIXEL       = 0x0115,
   ROWS_PER_STRIP          = 0x0116,
   STRIP_BYTE_COUNTS       = 0x0117,
   PLANAR_CONFIG           = 0x011C,
   TILE_WIDTH              = 0x0142,
   TILE_LENGTH             = 0x0143,
   TILE_OFFSETS            = 0x0144,
   TILE_BYTE_COUNTS        = 0x0145,
   SUB_IFDS                = 0x014A,
   JPEG_INT_FMT            = 0x0201,
   JPEG_INT_FMT_LEN        = 0x0202,
   SONY_RAW_TYPE           = 0x7000,
   SONY_TONE_CURVE         = 0x7010,
   SONY_SR2_SUB_IFD_OFFSET = 0x7200,
   SONY_SR2_SUB_IFD_LENGTH = 0x7201,
   SONY_SR2_SUB_IFD_KEY    = 0x7221,
   SONY_BLACK_LEVEL        = 0x7310,
   SONY_WHITE_BALANCE      = 0x7313,
   SONY_COLOR_MATRIX       = 0x7800,
   SONY_WHITE_LEVEL        = 0x787F,
   CFA_REPEAT_PATTERN_DIM  = 0x828D,
   CFA_PATTERN             = 0x828E,
   EXIF_IFD                = 0x8769,
   MAKER_NOTE              = 0x927C,
   DNG_VERSION             = 0xC612,
   BLACK_LEVEL             = 0xC61A,
   WHITE_LEVEL             = 0xC61D,
   DEFAULT_CROP_ORIGIN     = 0xC61F,
   DEFAULT_CROP_SIZE       = 0xC620,
   COLOR_MATRIX1           = 0xC621,
   COLOR_MATRIX2           = 0xC622,
   FORWARD_MATRIX1         = 0xC714,
   FORWARD_MATRIX2         = 0xC715,
   AS_SHOT_NEUTRAL         = 0xC628,
   DNG_PRIVATE_DATA        = 0xC634,
};
// clang-format on

}  // namespace cmrw

#endif /* _TIFF_TAG_ID_H_ */
