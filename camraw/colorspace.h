#ifndef COLORSPACE_H_
#define COLORSPACE_H_

#include <cstdint>

#include "camraw/matrix.h"

namespace cmrw {

enum class Colorspace : uint16_t {
   CAMERA = 0,
   CIE_XYZ = 1,
   SRGB = 2,
};

// clang-format off
inline const Matrix<float> COLORSPACE_XYZ_TO_SRGB_MATRIX =
   Matrix<float>(/*rows*/3, /*cols*/3,
                 {
                    3.2404542,   -1.5371385,   -0.4985314,
                   -0.9692660,    1.8760108,    0.0415560,
                    0.0556434,   -0.2040259,    1.0572252,
                 });

inline const Matrix<float> COLORSPACE_XYZ_TO_SRGB_BRADFORD_D50_MATRIX =
   Matrix<float>(/*rows*/3, /*cols*/3,
                 {
                    3.1338561,   -1.6168667,   -0.4906146,
                   -0.9787684,    1.9161415,    0.0334540,
                    0.0719453,   -0.2289914,    1.4052427,
                 });

inline const Matrix<float> COLORSPACE_YCBCR_TO_RGB_MATRIX =
   Matrix<float>(/*rows*/3, /*cols*/3,
                 {
                   1.000,   0.000,  1.400,
                   1.000,  -0.343, -0.711,
                   1.000,   1.765,  0.000
                 });
// clang-format on

}  // namespace cmrw

#endif /* COLORSPACE_H_ */
