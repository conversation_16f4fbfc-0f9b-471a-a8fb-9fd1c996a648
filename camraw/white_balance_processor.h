#ifndef WHITE_BALANCE_PROCESSOR_H_
#define WHITE_BALANCE_PROCESSOR_H_

#include "absl/status/status.h"

#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/pipeline_processor.h"

namespace cmrw {

class WhiteBalanceProcessor : public PipelineProcessor {
   public:
      WhiteBalanceProcessor()
          : PipelineProcessor(
                "WhiteBalanceProcessor",
                /*input_spec*/
                {.one_sample_per_pixel = true, .three_samples_per_pixel = true},
                /*output_spec*/
                {.one_sample_per_pixel = true, .three_samples_per_pixel = true},
                /*required metadata*/
                {
                    ImageMetaId::WHITE_BALANCE_MULTIPLIERS,
                },
                /*optional metadata*/ {}),
            clipped_red_(0),
            clipped_green_(0),
            clipped_blue_(0) {}
      absl::Status Init(const ImageMeta& image_meta) override;
      void LogStart() override;
      absl::Status Run(const ImageBuf<uint16_t>& input_buffer,
                       ImageBuf<uint16_t>& output_buffer) override;
      void LogStats() override;
      absl::Status MutateMetadata(ImageMeta& image_meta) override;

   private:
      float wb_mult_red_;
      float wb_mult_green_;
      float wb_mult_blue_;
      uint32_t clipped_red_;
      uint32_t clipped_green_;
      uint32_t clipped_blue_;
};

}  // namespace cmrw

#endif /* WHITE_BALANCE_PROCESSOR_H_ */
