#ifndef PIPELINE_SINK_H_
#define PIPELINE_SINK_H_

#include <string>
#include <unordered_set>

#include "absl/status/status.h"

#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/pipeline_element.h"

namespace cmrw {

class PipelineSink : public PipelineElement {
   public:
      PipelineSink(std::string name, PipelineElementIOSpec input_spec,
                   std::unordered_set<ImageMetaId> required_metadata,
                   std::unordered_set<ImageMetaId> optional_metadata)
          : PipelineElement(name, PipelineElementType::SINK, input_spec,
                            {/*output_spec*/}, required_metadata,
                            optional_metadata) {}
      virtual ~PipelineSink() = default;
      virtual absl::Status OutputImage(const ImageBuf<uint16_t>& img,
                                       const ImageMeta& meta) = 0;
};

}  // namespace cmrw

#endif /* PIPELINE_SINK_H_ */
