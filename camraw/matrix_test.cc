#include <gtest/gtest.h>

#include "absl/status/statusor.h"

#include "camraw/matrix.h"

using cmrw::Matrix;

TEST(MatrixTest, MatrixAccess) {
   Matrix<int> m(2, 2);
   m(0, 0) = 1;
   m(0, 1) = 2;
   m(1, 0) = 3;
   m(1, 1) = 4;

   EXPECT_EQ(m(0, 0), 1);
   EXPECT_EQ(m(0, 1), 2);
   EXPECT_EQ(m(1, 0), 3);
   EXPECT_EQ(m(1, 1), 4);
}

TEST(MatrixTest, MatrixVectorInit) {
   std::vector<int> vec = {1, 2, 3, 4};
   Matrix<int> m(2, 2, vec);

   EXPECT_EQ(m(0, 0), 1);
   EXPECT_EQ(m(0, 1), 2);
   EXPECT_EQ(m(1, 0), 3);
   EXPECT_EQ(m(1, 1), 4);
}

TEST(MatrixTest, DisplayString) {
   Matrix<int> m(2, 2, {1, 2, 3, 4});

   EXPECT_EQ(m.DisplayString(), "Matrix(2,2)=[[1,2],[3,4]]");
}

TEST(MatrixTest, Multiplication) {
   Matrix<int> m1(1, 2, {5, 10});
   Matrix<int> m2(2, 2, {1, 2, 3, 4});

   Matrix<int> result = m1.Multiply(m2);
   EXPECT_EQ(result(0, 0), 35);
   EXPECT_EQ(result(0, 1), 50);
}

TEST(MatrixTest, Inversion) {
   Matrix<float> input(3, 3, {1, 0, 0, 0, 1, 0, 0, 0, 2});
   Matrix<float> answer(3, 3, {1, 0, 0, 0, 1, 0, 0, 0, 0.5});

   absl::StatusOr<Matrix<float>> result = input.invert();
   EXPECT_TRUE(result.ok());
   for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 3; j++) {
         EXPECT_EQ((*result)(i, j), answer(i, j));
      }
   }
}
