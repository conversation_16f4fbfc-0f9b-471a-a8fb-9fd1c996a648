#ifndef PIPELINE_SOURCE_H_
#define PIPELINE_SOURCE_H_

#include <memory>
#include <string>
#include <unordered_set>

#include "absl/status/status.h"
#include "absl/status/statusor.h"

#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/pipeline_element.h"

namespace cmrw {

struct PipelineSourceChunk {
      // Owned RAW data (if any) for this chunk
      std::unique_ptr<uint8_t[]> owned_chunk_data = nullptr;
      // Pointer to RAW data (in case of both for unowned and owned buffers)
      uint8_t* chunk_data_ptr = nullptr;
      // Size of the chunk data in bytes
      uint32_t chunk_data_bytes;
      // Whether this chunk needs source processing (decoding etc.)
      bool needs_processing;
      // Byte alignment for breaking the chunk into sub-chunks - ie. if this
      // value is 32 then this chunk has to be split into sub-chunks that are
      // the multiple of 32 bytes.
      uint32_t sub_chunk_byte_alignment;
      // Dimensions of the image data that will result after processing the
      // chunk
      uint32_t processed_height;
      uint32_t processed_width;
      // Position of this chunk in the full image
      uint32_t x_offset;
      uint32_t y_offset;
};

class PipelineSource : public PipelineElement {
   public:
      PipelineSource(std::string name, PipelineElementIOSpec output_spec)
          : PipelineElement(name, PipelineElementType::SOURCE, {/*input_spec*/},
                            output_spec, {/*required metadata*/},
                            {/*optional metadata*/}) {}
      virtual ~PipelineSource() = default;
      virtual absl::Status Init() = 0;
      virtual absl::StatusOr<std::unique_ptr<ImageMeta>> GetMetadata(
          std::unordered_set<ImageMetaId> required,
          std::unordered_set<ImageMetaId> optional) = 0;
      virtual absl::StatusOr<std::vector<std::unique_ptr<PipelineSourceChunk>>>
      GetChunks() = 0;
      virtual absl::Status ProcessChunk(const PipelineSourceChunk& chunk,
                                        ImageBuf<uint16_t>& output) = 0;
      virtual absl::Status MutateMetadata(ImageMeta& image_meta) {
         return absl::OkStatus();
      };
};

}  // namespace cmrw

#endif /* PIPELINE_SOURCE_H_ */
