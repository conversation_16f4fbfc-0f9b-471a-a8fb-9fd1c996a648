#ifndef _BIT_PUMP_H_
#define _BIT_PUMP_H_

#include <cstdint>

#include "absl/status/status.h"
#include "absl/status/statusor.h"

#include "camraw/data_accessor.h"
#include "camraw/endian.h"

namespace cmrw {

class BitPump {
   public:
      BitPump(DataAccessor* data_accessor, uint32_t data_offset,
              Endianness data_endianness = ENDIANNESS_BIG)
          : data_accessor_(data_accessor),
            data_offset_(data_offset),
            next_byte_offset_(data_offset),
            data_endianness_(data_endianness) {}
      absl::StatusOr<uint16_t> GetBits(size_t num_bits);
      uint32_t GetNumBytesRead() { return next_byte_offset_ - data_offset_; };

   protected:
      DataAccessor* data_accessor_;
      uint32_t data_offset_;
      uint32_t next_byte_offset_;
      Endianness data_endianness_;
      uint8_t cur_byte_ = 0;
      size_t cur_bits_left_ = 0;

      virtual absl::Status CopyNextByte();
      absl::StatusOr<uint8_t> PeekNextByte();
};

}  // namespace cmrw

#endif /* _BIT_PUMP_H_ */
