#ifndef _STATUS_MACROS_H_
#define _STATUS_MACROS_H_

namespace cmrw {

#define ASSIGN_OR_RETURN(var, rexpr, ...)             \
   do {                                               \
      auto var##_status_or = (rexpr);                 \
      if (!var##_status_or.ok()) {                    \
         __VA_ARGS__; /* log expression if present */ \
         return var##_status_or.status();             \
      }                                               \
      var = std::move(var##_status_or).value();       \
   } while (0)

#define RETURN_IF_ERROR(expr, ...)                    \
   do {                                               \
      auto _status = (expr);                          \
      if (!_status.ok()) {                            \
         __VA_ARGS__; /* log expression if present */ \
         return _status;                              \
      }                                               \
   } while (0)

}  // namespace cmrw

#endif /* _STATUS_MACROS_H_ */
