#ifndef TIFF_EXPORT_SINK_H_
#define TIFF_EXPORT_SINK_H_

#include "absl/status/status.h"

#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/pipeline_sink.h"

namespace cmrw {

class TiffExportSink : public PipelineSink {
   public:
      TiffExportSink(std::string file_name, bool crop)
          : PipelineSink(
                "TiffExportSink",
                /*input spec*/
                {.one_sample_per_pixel = true, .three_samples_per_pixel = true},
                /*required metadata*/
                {
                    ImageMetaId::IMAGE_WIDTH,
                    ImageMetaId::IMAGE_HEIGHT,
                    ImageMetaId::ORIENTATION,
                    ImageMetaId::BITS_PER_SAMPLE,
                    ImageMetaId::COMPRESSION,
                    ImageMetaId::SAMPLES_PER_PIXEL,
                    ImageMetaId::PHOTOMETRIC_INTERPRETATION,
                },
                /*optional metadata*/
                {}),
            file_name_(file_name),
            crop_(crop) {
         if (crop_) {
            optional_metadata_.insert(ImageMetaId::CROP_ORIGIN);
            optional_metadata_.insert(ImageMetaId::CROP_SIZE);
         }
      }
      absl::Status OutputImage(const ImageBuf<uint16_t>& img,
                               const ImageMeta& image_meta) override;

   private:
      std::string file_name_;
      bool crop_;
};

}  // namespace cmrw

#endif /* TIFF_EXPORT_SINK_H_ */
