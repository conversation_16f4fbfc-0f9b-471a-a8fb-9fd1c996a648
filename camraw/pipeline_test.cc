#include <cassert>
#include <cmath>
#include <gtest/gtest.h>
#include <memory>
#include <vector>

#include "absl/status/status.h"
#include "absl/status/statusor.h"

#include "camraw/imagebuf.h"
#include "camraw/imagemeta.h"
#include "camraw/pipeline.h"
#include "camraw/pipeline_element.h"
#include "camraw/pipeline_processor.h"
#include "camraw/pipeline_sink.h"
#include "camraw/pipeline_source.h"

using cmrw::ImageBuf;
using cmrw::ImageMeta;
using cmrw::ImageMetaId;
using cmrw::Pipeline;
using cmrw::PipelineElementIOSpec;
using cmrw::PipelineProcessor;
using cmrw::PipelineSink;
using cmrw::PipelineSource;
using cmrw::PipelineSourceChunk;

namespace {

// Test constants - using smaller images for faster test execution
constexpr uint16_t kImageWidth = 800;
constexpr uint16_t kImageHeight = 800;
constexpr uint16_t kChunkWidth = 40;
constexpr uint16_t kChunkHeight = 40;
constexpr uint16_t kChunksPerRow = kImageWidth / kChunkWidth;    // 20
constexpr uint16_t kChunksPerCol = kImageHeight / kChunkHeight;  // 20
constexpr uint16_t kNumChunks = kChunksPerRow * kChunksPerCol;   // 400
constexpr uint16_t kProcessorAddition = 100;

// Test PipelineSource that can generate different chunk configurations
class TestPipelineSource : public PipelineSource {
   public:
      enum class ChunkMode {
         SINGLE_CHUNK_NO_PROCESSING,
         MULTIPLE_CHUNKS_NO_PROCESSING,
         SINGLE_CHUNK_NEEDS_PROCESSING,
         MULTIPLE_CHUNKS_NEEDS_PROCESSING
      };

      TestPipelineSource(ChunkMode mode, uint16_t samples_per_pixel = 1)
          : PipelineSource(
                "TestPipelineSource",
                {.one_sample_per_pixel = (samples_per_pixel == 1),
                 .three_samples_per_pixel = (samples_per_pixel == 3)}),
            mode_(mode),
            samples_per_pixel_(samples_per_pixel) {}

      absl::Status Init() override { return absl::OkStatus(); }

      absl::StatusOr<std::unique_ptr<ImageMeta>> GetMetadata(
          std::unordered_set<ImageMetaId> required,
          std::unordered_set<ImageMetaId> optional) override {
         auto meta = std::make_unique<ImageMeta>();
         meta->SetOne<uint16_t>(ImageMetaId::IMAGE_WIDTH, kImageWidth);
         meta->SetOne<uint16_t>(ImageMetaId::IMAGE_HEIGHT, kImageHeight);
         meta->SetOne<uint16_t>(ImageMetaId::SAMPLES_PER_PIXEL,
                                samples_per_pixel_);
         return meta;
      }

      absl::StatusOr<std::vector<std::unique_ptr<PipelineSourceChunk>>>
      GetChunks() override {
         std::vector<std::unique_ptr<PipelineSourceChunk>> chunks;

         bool needs_processing =
             (mode_ == ChunkMode::SINGLE_CHUNK_NEEDS_PROCESSING ||
              mode_ == ChunkMode::MULTIPLE_CHUNKS_NEEDS_PROCESSING);
         bool multiple_chunks =
             (mode_ == ChunkMode::MULTIPLE_CHUNKS_NO_PROCESSING ||
              mode_ == ChunkMode::MULTIPLE_CHUNKS_NEEDS_PROCESSING);

         if (multiple_chunks) {
            // Generate chunks to cover the entire image
            for (int i = 0; i < kNumChunks; ++i) {
               auto chunk = std::make_unique<PipelineSourceChunk>();
               chunk->needs_processing = needs_processing;
               chunk->processed_height = kChunkHeight;
               chunk->processed_width = kChunkWidth;
               chunk->y_offset = (i / kChunksPerRow) * kChunkHeight;
               chunk->x_offset = (i % kChunksPerRow) * kChunkWidth;
               chunk->sub_chunk_byte_alignment =
                   1;  // No special alignment needed

               if (needs_processing) {
                  // Create dummy raw data for processing
                  size_t data_size = kChunkWidth * kChunkHeight *
                                     samples_per_pixel_ * sizeof(uint16_t);
                  chunk->owned_chunk_data =
                      std::make_unique<uint8_t[]>(data_size);
                  chunk->chunk_data_ptr = chunk->owned_chunk_data.get();
                  chunk->chunk_data_bytes = data_size;

                  // Fill with test pattern: y * 1000 + x + c
                  uint16_t* data =
                      reinterpret_cast<uint16_t*>(chunk->chunk_data_ptr);
                  for (int y = 0; y < kChunkHeight; ++y) {
                     for (int x = 0; x < kChunkWidth; ++x) {
                        for (int c = 0; c < samples_per_pixel_; ++c) {
                           int global_x = chunk->x_offset + x;
                           int global_y = chunk->y_offset + y;
                           data[(y * kChunkWidth + x) * samples_per_pixel_ +
                                c] = global_y * 80 + global_x + c;
                        }
                     }
                  }
               } else {
                  // Create already processed data
                  size_t data_size = kChunkWidth * kChunkHeight *
                                     samples_per_pixel_ * sizeof(uint16_t);
                  chunk->owned_chunk_data =
                      std::make_unique<uint8_t[]>(data_size);
                  chunk->chunk_data_ptr = chunk->owned_chunk_data.get();
                  chunk->chunk_data_bytes = data_size;

                  // Fill with test pattern: y * 1000 + x + c
                  uint16_t* data =
                      reinterpret_cast<uint16_t*>(chunk->chunk_data_ptr);
                  for (int y = 0; y < kChunkHeight; ++y) {
                     for (int x = 0; x < kChunkWidth; ++x) {
                        for (int c = 0; c < samples_per_pixel_; ++c) {
                           int global_x = chunk->x_offset + x;
                           int global_y = chunk->y_offset + y;
                           data[(y * kChunkWidth + x) * samples_per_pixel_ +
                                c] = global_y * 80 + global_x + c;
                        }
                     }
                  }
               }

               chunks.push_back(std::move(chunk));
            }
         } else {
            // Single chunk covering entire image
            auto chunk = std::make_unique<PipelineSourceChunk>();
            chunk->needs_processing = needs_processing;
            chunk->processed_height = kImageHeight;
            chunk->processed_width = kImageWidth;
            chunk->y_offset = 0;
            chunk->x_offset = 0;

            size_t data_size = kImageWidth * kImageHeight * samples_per_pixel_ *
                               sizeof(uint16_t);
            // TODO: We could test the "break up chunk" behavior by making the
            // ProcessChunk() function understand the global x/y offsets.
            chunk->sub_chunk_byte_alignment =
                data_size;  // don't break up chunk
            chunk->owned_chunk_data = std::make_unique<uint8_t[]>(data_size);
            chunk->chunk_data_ptr = chunk->owned_chunk_data.get();
            chunk->chunk_data_bytes = data_size;

            // Fill with test pattern: y * 1000 + x + c
            uint16_t* data = reinterpret_cast<uint16_t*>(chunk->chunk_data_ptr);
            for (int y = 0; y < kImageHeight; ++y) {
               for (int x = 0; x < kImageWidth; ++x) {
                  for (int c = 0; c < samples_per_pixel_; ++c) {
                     data[(y * kImageWidth + x) * samples_per_pixel_ + c] =
                         y * 80 + x + c;
                  }
               }
            }

            chunks.push_back(std::move(chunk));
         }

         return chunks;
      }

      absl::Status ProcessChunk(const PipelineSourceChunk& chunk,
                                ImageBuf<uint16_t>& output) override {
         // Simple processing: copy data from chunk to output
         assert(output.get_height() == chunk.processed_height);
         assert(output.get_width() == chunk.processed_width);
         assert(output.get_components() == samples_per_pixel_);

         const uint16_t* data =
             reinterpret_cast<const uint16_t*>(chunk.chunk_data_ptr);
         for (size_t y = 0; y < chunk.processed_height; ++y) {
            for (size_t x = 0; x < chunk.processed_width; ++x) {
               for (size_t c = 0; c < samples_per_pixel_; ++c) {
                  output(y, x, c) = data[(y * chunk.processed_width + x) *
                                             samples_per_pixel_ +
                                         c];
               }
            }
         }

         return absl::OkStatus();
      }

   private:
      ChunkMode mode_;
      uint16_t samples_per_pixel_;
};

// Test PipelineProcessor that multiplies and divides pixel values
class TestPipelineProcessor : public PipelineProcessor {
   public:
      TestPipelineProcessor(uint16_t input_samples, uint16_t output_samples,
                            const std::string& name = "TestPipelineProcessor")
          : PipelineProcessor(
                name,
                {.one_sample_per_pixel = (input_samples == 1),
                 .three_samples_per_pixel = (input_samples == 3)},
                {.one_sample_per_pixel = (output_samples == 1),
                 .three_samples_per_pixel = (output_samples == 3)},
                {}, {}),
            input_samples_(input_samples),
            output_samples_(output_samples) {}

      absl::Status Init(const ImageMeta& image_meta) override {
         return absl::OkStatus();
      }

      absl::Status Run(const ImageBuf<uint16_t>& input_buffer,
                       ImageBuf<uint16_t>& output_buffer) override {
         assert(input_buffer.get_components() == input_samples_);
         assert(output_buffer.get_components() == output_samples_);
         assert(input_buffer.get_height() == output_buffer.get_height());
         assert(input_buffer.get_width() == output_buffer.get_width());

         for (size_t y = 0; y < input_buffer.get_height(); ++y) {
            for (size_t x = 0; x < input_buffer.get_width(); ++x) {
               for (size_t c = 0; c < output_samples_; ++c) {
                  uint16_t input_val;
                  if (c < input_samples_) {
                     input_val = input_buffer(y, x, c);
                  } else {
                     // For 1->3 conversion, replicate the single channel
                     input_val = input_buffer(y, x, 0);
                  }

                  // Apply mathematical transformation for validation
                  output_buffer(y, x, c) = input_val + kProcessorAddition;
               }
            }
         }

         return absl::OkStatus();
      }

   private:
      uint16_t input_samples_;
      uint16_t output_samples_;
};

// Test PipelineSink that captures output for validation
class TestPipelineSink : public PipelineSink {
   public:
      TestPipelineSink(uint16_t samples_per_pixel)
          : PipelineSink("TestPipelineSink",
                         {.one_sample_per_pixel = (samples_per_pixel == 1),
                          .three_samples_per_pixel = (samples_per_pixel == 3)},
                         {}, {}),
            samples_per_pixel_(samples_per_pixel) {}

      absl::Status OutputImage(const ImageBuf<uint16_t>& img,
                               const ImageMeta& meta) override {
         // Store the output for validation
         output_image_ = std::make_unique<ImageBuf<uint16_t>>(
             img.get_height(), img.get_width(), img.get_components());
         output_image_->copy_tile_in_from(img);
         output_meta_ = std::make_unique<ImageMeta>(meta);
         return absl::OkStatus();
      }

      const ImageBuf<uint16_t>* GetOutputImage() const {
         return output_image_.get();
      }

      const ImageMeta* GetOutputMeta() const { return output_meta_.get(); }

   private:
      uint16_t samples_per_pixel_;
      std::unique_ptr<ImageBuf<uint16_t>> output_image_;
      std::unique_ptr<ImageMeta> output_meta_;
};

// Helper function to validate expected pixel values
void
ValidatePixelValues(const ImageBuf<uint16_t>& image,
                    TestPipelineSource::ChunkMode source_mode,
                    int num_processors = 0,
                    bool has_sample_conversion = false) {
   for (size_t y = 0; y < image.get_height(); ++y) {
      for (size_t x = 0; x < image.get_width(); ++x) {
         for (size_t c = 0; c < image.get_components(); ++c) {
            uint16_t expected;

            if (has_sample_conversion && image.get_components() == 3) {
               // For 1->3 sample conversion, all channels should have the same
               // value as the original single channel (channel 0)
               expected = static_cast<int>(y) * 80 + static_cast<int>(x);
            } else {
               // Normal case: y * 80 + x + c
               expected = static_cast<int>(y) * 80 + static_cast<int>(x) + c;
            }

            // Apply processor transformations (add 100 for each processor)
            for (int i = 0; i < num_processors; ++i) {
               expected += kProcessorAddition;
            }

            EXPECT_EQ(image(y, x, c), expected)
                << "Mismatch at (" << y << ", " << x << ", " << c << ")";
         }
      }
   }
}

}  // namespace

// Test cases for Source -> Sink pipeline
TEST(PipelineTest, SourceToSink_SingleChunk_NoProcessing_SingleThread) {
   Pipeline pipeline;

   auto source = std::make_unique<TestPipelineSource>(
       TestPipelineSource::ChunkMode::SINGLE_CHUNK_NO_PROCESSING);
   auto sink = std::make_unique<TestPipelineSink>(1);
   auto* sink_ptr = sink.get();

   EXPECT_TRUE(pipeline.AddSource(std::move(source)).ok());
   EXPECT_TRUE(pipeline.AddSink(std::move(sink)).ok());

   EXPECT_TRUE(pipeline.Run(1).ok());

   const auto* output = sink_ptr->GetOutputImage();
   ASSERT_NE(output, nullptr);
   EXPECT_EQ(output->get_height(), kImageHeight);
   EXPECT_EQ(output->get_width(), kImageWidth);
   EXPECT_EQ(output->get_components(), 1);

   ValidatePixelValues(
       *output, TestPipelineSource::ChunkMode::SINGLE_CHUNK_NO_PROCESSING);
}

TEST(PipelineTest, SourceToSink_SingleChunk_NoProcessing_MaxThreads) {
   Pipeline pipeline;

   auto source = std::make_unique<TestPipelineSource>(
       TestPipelineSource::ChunkMode::SINGLE_CHUNK_NO_PROCESSING);
   auto sink = std::make_unique<TestPipelineSink>(1);
   auto* sink_ptr = sink.get();

   EXPECT_TRUE(pipeline.AddSource(std::move(source)).ok());
   EXPECT_TRUE(pipeline.AddSink(std::move(sink)).ok());

   EXPECT_TRUE(pipeline.Run(0).ok());  // 0 = max threads

   const auto* output = sink_ptr->GetOutputImage();
   ASSERT_NE(output, nullptr);
   ValidatePixelValues(
       *output, TestPipelineSource::ChunkMode::SINGLE_CHUNK_NO_PROCESSING);
}

TEST(PipelineTest, SourceToSink_MultipleChunks_NeedsProcessing_SingleThread) {
   Pipeline pipeline;

   auto source = std::make_unique<TestPipelineSource>(
       TestPipelineSource::ChunkMode::MULTIPLE_CHUNKS_NEEDS_PROCESSING);
   auto sink = std::make_unique<TestPipelineSink>(1);
   auto* sink_ptr = sink.get();

   EXPECT_TRUE(pipeline.AddSource(std::move(source)).ok());
   EXPECT_TRUE(pipeline.AddSink(std::move(sink)).ok());

   EXPECT_TRUE(pipeline.Run(1).ok());

   const auto* output = sink_ptr->GetOutputImage();
   ASSERT_NE(output, nullptr);
   EXPECT_EQ(output->get_height(), kImageHeight);
   EXPECT_EQ(output->get_width(), kImageWidth);
   EXPECT_EQ(output->get_components(), 1);

   ValidatePixelValues(
       *output,
       TestPipelineSource::ChunkMode::MULTIPLE_CHUNKS_NEEDS_PROCESSING);
}

TEST(PipelineTest, SourceToSink_MultipleChunks_NeedsProcessing_MaxThreads) {
   Pipeline pipeline;

   auto source = std::make_unique<TestPipelineSource>(
       TestPipelineSource::ChunkMode::MULTIPLE_CHUNKS_NEEDS_PROCESSING);
   auto sink = std::make_unique<TestPipelineSink>(1);
   auto* sink_ptr = sink.get();

   EXPECT_TRUE(pipeline.AddSource(std::move(source)).ok());
   EXPECT_TRUE(pipeline.AddSink(std::move(sink)).ok());

   EXPECT_TRUE(pipeline.Run(0).ok());

   const auto* output = sink_ptr->GetOutputImage();
   ASSERT_NE(output, nullptr);
   ValidatePixelValues(
       *output,
       TestPipelineSource::ChunkMode::MULTIPLE_CHUNKS_NEEDS_PROCESSING);
}

TEST(PipelineTest, SourceToSink_SingleChunk_NeedsProcessing_SingleThread) {
   Pipeline pipeline;

   auto source = std::make_unique<TestPipelineSource>(
       TestPipelineSource::ChunkMode::SINGLE_CHUNK_NEEDS_PROCESSING);
   auto sink = std::make_unique<TestPipelineSink>(1);
   auto* sink_ptr = sink.get();

   EXPECT_TRUE(pipeline.AddSource(std::move(source)).ok());
   EXPECT_TRUE(pipeline.AddSink(std::move(sink)).ok());

   EXPECT_TRUE(pipeline.Run(1).ok());

   const auto* output = sink_ptr->GetOutputImage();
   ASSERT_NE(output, nullptr);
   ValidatePixelValues(
       *output, TestPipelineSource::ChunkMode::SINGLE_CHUNK_NEEDS_PROCESSING);
}

TEST(PipelineTest, SourceToSink_SingleChunk_NeedsProcessing_MaxThreads) {
   Pipeline pipeline;

   auto source = std::make_unique<TestPipelineSource>(
       TestPipelineSource::ChunkMode::SINGLE_CHUNK_NEEDS_PROCESSING);
   auto sink = std::make_unique<TestPipelineSink>(1);
   auto* sink_ptr = sink.get();

   EXPECT_TRUE(pipeline.AddSource(std::move(source)).ok());
   EXPECT_TRUE(pipeline.AddSink(std::move(sink)).ok());

   EXPECT_TRUE(pipeline.Run(0).ok());

   const auto* output = sink_ptr->GetOutputImage();
   ASSERT_NE(output, nullptr);
   ValidatePixelValues(
       *output, TestPipelineSource::ChunkMode::SINGLE_CHUNK_NEEDS_PROCESSING);
}

// Test cases for Source -> Processor -> Sink pipeline
TEST(PipelineTest, SourceProcessorSink_SingleChunk_NoProcessing_SingleThread) {
   Pipeline pipeline;

   auto source = std::make_unique<TestPipelineSource>(
       TestPipelineSource::ChunkMode::SINGLE_CHUNK_NO_PROCESSING);
   auto processor = std::make_unique<TestPipelineProcessor>(1, 1);
   auto sink = std::make_unique<TestPipelineSink>(1);
   auto* sink_ptr = sink.get();

   EXPECT_TRUE(pipeline.AddSource(std::move(source)).ok());
   EXPECT_TRUE(pipeline.AddProcessor(std::move(processor)).ok());
   EXPECT_TRUE(pipeline.AddSink(std::move(sink)).ok());

   EXPECT_TRUE(pipeline.Run(1).ok());

   const auto* output = sink_ptr->GetOutputImage();
   ASSERT_NE(output, nullptr);
   EXPECT_EQ(output->get_height(), kImageHeight);
   EXPECT_EQ(output->get_width(), kImageWidth);
   EXPECT_EQ(output->get_components(), 1);

   ValidatePixelValues(
       *output, TestPipelineSource::ChunkMode::SINGLE_CHUNK_NO_PROCESSING, 1);
}

TEST(PipelineTest, SourceProcessorSink_SingleChunk_NoProcessing_MaxThreads) {
   Pipeline pipeline;

   auto source = std::make_unique<TestPipelineSource>(
       TestPipelineSource::ChunkMode::SINGLE_CHUNK_NO_PROCESSING);
   auto processor = std::make_unique<TestPipelineProcessor>(1, 1);
   auto sink = std::make_unique<TestPipelineSink>(1);
   auto* sink_ptr = sink.get();

   EXPECT_TRUE(pipeline.AddSource(std::move(source)).ok());
   EXPECT_TRUE(pipeline.AddProcessor(std::move(processor)).ok());
   EXPECT_TRUE(pipeline.AddSink(std::move(sink)).ok());

   EXPECT_TRUE(pipeline.Run(0).ok());

   const auto* output = sink_ptr->GetOutputImage();
   ASSERT_NE(output, nullptr);
   ValidatePixelValues(
       *output, TestPipelineSource::ChunkMode::SINGLE_CHUNK_NO_PROCESSING, 1);
}

TEST(PipelineTest,
     SourceProcessorSink_MultipleChunks_NeedsProcessing_SingleThread) {
   Pipeline pipeline;

   auto source = std::make_unique<TestPipelineSource>(
       TestPipelineSource::ChunkMode::MULTIPLE_CHUNKS_NEEDS_PROCESSING);
   auto processor = std::make_unique<TestPipelineProcessor>(1, 1);
   auto sink = std::make_unique<TestPipelineSink>(1);
   auto* sink_ptr = sink.get();

   EXPECT_TRUE(pipeline.AddSource(std::move(source)).ok());
   EXPECT_TRUE(pipeline.AddProcessor(std::move(processor)).ok());
   EXPECT_TRUE(pipeline.AddSink(std::move(sink)).ok());

   EXPECT_TRUE(pipeline.Run(1).ok());

   const auto* output = sink_ptr->GetOutputImage();
   ASSERT_NE(output, nullptr);
   ValidatePixelValues(
       *output, TestPipelineSource::ChunkMode::MULTIPLE_CHUNKS_NEEDS_PROCESSING,
       1);
}

TEST(PipelineTest,
     SourceProcessorSink_MultipleChunks_NeedsProcessing_MaxThreads) {
   Pipeline pipeline;

   auto source = std::make_unique<TestPipelineSource>(
       TestPipelineSource::ChunkMode::MULTIPLE_CHUNKS_NEEDS_PROCESSING);
   auto processor = std::make_unique<TestPipelineProcessor>(1, 1);
   auto sink = std::make_unique<TestPipelineSink>(1);
   auto* sink_ptr = sink.get();

   EXPECT_TRUE(pipeline.AddSource(std::move(source)).ok());
   EXPECT_TRUE(pipeline.AddProcessor(std::move(processor)).ok());
   EXPECT_TRUE(pipeline.AddSink(std::move(sink)).ok());

   EXPECT_TRUE(pipeline.Run(0).ok());

   const auto* output = sink_ptr->GetOutputImage();
   ASSERT_NE(output, nullptr);
   ValidatePixelValues(
       *output, TestPipelineSource::ChunkMode::MULTIPLE_CHUNKS_NEEDS_PROCESSING,
       1);
}

TEST(PipelineTest,
     SourceProcessorSink_SingleChunk_NeedsProcessing_SingleThread) {
   Pipeline pipeline;

   auto source = std::make_unique<TestPipelineSource>(
       TestPipelineSource::ChunkMode::SINGLE_CHUNK_NEEDS_PROCESSING);
   auto processor = std::make_unique<TestPipelineProcessor>(1, 1);
   auto sink = std::make_unique<TestPipelineSink>(1);
   auto* sink_ptr = sink.get();

   EXPECT_TRUE(pipeline.AddSource(std::move(source)).ok());
   EXPECT_TRUE(pipeline.AddProcessor(std::move(processor)).ok());
   EXPECT_TRUE(pipeline.AddSink(std::move(sink)).ok());

   EXPECT_TRUE(pipeline.Run(1).ok());

   const auto* output = sink_ptr->GetOutputImage();
   ASSERT_NE(output, nullptr);
   ValidatePixelValues(
       *output, TestPipelineSource::ChunkMode::SINGLE_CHUNK_NEEDS_PROCESSING,
       1);
}

TEST(PipelineTest, SourceProcessorSink_SingleChunk_NeedsProcessing_MaxThreads) {
   Pipeline pipeline;

   auto source = std::make_unique<TestPipelineSource>(
       TestPipelineSource::ChunkMode::SINGLE_CHUNK_NEEDS_PROCESSING);
   auto processor = std::make_unique<TestPipelineProcessor>(1, 1);
   auto sink = std::make_unique<TestPipelineSink>(1);
   auto* sink_ptr = sink.get();

   EXPECT_TRUE(pipeline.AddSource(std::move(source)).ok());
   EXPECT_TRUE(pipeline.AddProcessor(std::move(processor)).ok());
   EXPECT_TRUE(pipeline.AddSink(std::move(sink)).ok());

   EXPECT_TRUE(pipeline.Run(0).ok());

   const auto* output = sink_ptr->GetOutputImage();
   ASSERT_NE(output, nullptr);
   ValidatePixelValues(
       *output, TestPipelineSource::ChunkMode::SINGLE_CHUNK_NEEDS_PROCESSING,
       1);
}

// Test cases for Source -> Processor -> Sink -> Processor -> Sink pipeline
// with sample conversion (1 -> 3 -> 3)
TEST(PipelineTest,
     SourceProcessorSinkProcessorSink_SampleConversion_SingleThread) {
   Pipeline pipeline;

   auto source = std::make_unique<TestPipelineSource>(
       TestPipelineSource::ChunkMode::SINGLE_CHUNK_NEEDS_PROCESSING, 1);
   auto processor1 =
       std::make_unique<TestPipelineProcessor>(1, 3, "OneToThreeProcessor");
   auto sink1 = std::make_unique<TestPipelineSink>(3);
   auto processor2 =
       std::make_unique<TestPipelineProcessor>(3, 3, "ThreeToThreeProcessor");
   auto sink2 = std::make_unique<TestPipelineSink>(3);

   auto* sink1_ptr = sink1.get();
   auto* sink2_ptr = sink2.get();

   EXPECT_TRUE(pipeline.AddSource(std::move(source)).ok());
   EXPECT_TRUE(pipeline.AddProcessor(std::move(processor1)).ok());
   EXPECT_TRUE(pipeline.AddSink(std::move(sink1)).ok());
   EXPECT_TRUE(pipeline.AddProcessor(std::move(processor2)).ok());
   EXPECT_TRUE(pipeline.AddSink(std::move(sink2)).ok());

   EXPECT_TRUE(pipeline.Run(1).ok());

   // Validate first sink output (after 1->3 conversion and 1 processor)
   const auto* output1 = sink1_ptr->GetOutputImage();
   ASSERT_NE(output1, nullptr);
   EXPECT_EQ(output1->get_height(), kImageHeight);
   EXPECT_EQ(output1->get_width(), kImageWidth);
   EXPECT_EQ(output1->get_components(), 3);

   // Validate second sink output (after additional processing)
   const auto* output2 = sink2_ptr->GetOutputImage();
   ASSERT_NE(output2, nullptr);
   EXPECT_EQ(output2->get_height(), kImageHeight);
   EXPECT_EQ(output2->get_width(), kImageWidth);
   EXPECT_EQ(output2->get_components(), 3);

   // Validate pixel values with 2 processors applied and sample conversion
   ValidatePixelValues(
       *output2, TestPipelineSource::ChunkMode::SINGLE_CHUNK_NEEDS_PROCESSING,
       2, true);
}

TEST(PipelineTest,
     SourceProcessorSinkProcessorSink_SampleConversion_MaxThreads) {
   Pipeline pipeline;

   auto source = std::make_unique<TestPipelineSource>(
       TestPipelineSource::ChunkMode::MULTIPLE_CHUNKS_NEEDS_PROCESSING, 1);
   auto processor1 =
       std::make_unique<TestPipelineProcessor>(1, 3, "OneToThreeProcessor");
   auto sink1 = std::make_unique<TestPipelineSink>(3);
   auto processor2 =
       std::make_unique<TestPipelineProcessor>(3, 3, "ThreeToThreeProcessor");
   auto sink2 = std::make_unique<TestPipelineSink>(3);

   auto* sink1_ptr = sink1.get();
   auto* sink2_ptr = sink2.get();

   EXPECT_TRUE(pipeline.AddSource(std::move(source)).ok());
   EXPECT_TRUE(pipeline.AddProcessor(std::move(processor1)).ok());
   EXPECT_TRUE(pipeline.AddSink(std::move(sink1)).ok());
   EXPECT_TRUE(pipeline.AddProcessor(std::move(processor2)).ok());
   EXPECT_TRUE(pipeline.AddSink(std::move(sink2)).ok());

   EXPECT_TRUE(pipeline.Run(0).ok());  // 0 = max threads

   // Validate first sink output
   const auto* output1 = sink1_ptr->GetOutputImage();
   ASSERT_NE(output1, nullptr);
   EXPECT_EQ(output1->get_components(), 3);

   // Validate second sink output
   const auto* output2 = sink2_ptr->GetOutputImage();
   ASSERT_NE(output2, nullptr);
   EXPECT_EQ(output2->get_components(), 3);

   // Validate pixel values with 2 processors applied and sample conversion
   ValidatePixelValues(
       *output2,
       TestPipelineSource::ChunkMode::MULTIPLE_CHUNKS_NEEDS_PROCESSING, 2,
       true);
}

// NOTE: Tests for multiple chunks with no processing are commented out
// because the current Pipeline implementation returns UnimplementedError
// for this case (see pipeline.cc line 357-360).
//
// TODO: Uncomment these tests when multiple chunks without processing is
// implemented.
//
// TEST(PipelineTest, SourceToSink_MultipleChunks_NoProcessing_SingleThread) {
//    Pipeline pipeline;
//
//    auto source = std::make_unique<TestPipelineSource>(
//        TestPipelineSource::ChunkMode::MULTIPLE_CHUNKS_NO_PROCESSING);
//    auto sink = std::make_unique<TestPipelineSink>(1);
//    auto* sink_ptr = sink.get();
//
//    EXPECT_TRUE(pipeline.AddSource(std::move(source)).ok());
//    EXPECT_TRUE(pipeline.AddSink(std::move(sink)).ok());
//
//    EXPECT_TRUE(pipeline.Run(1).ok());
//
//    const auto* output = sink_ptr->GetOutputImage();
//    ASSERT_NE(output, nullptr);
//    EXPECT_EQ(output->get_height(), kImageHeight);
//    EXPECT_EQ(output->get_width(), kImageWidth);
//    EXPECT_EQ(output->get_components(), 1);
//
//    ValidatePixelValues(
//        *output,
//        TestPipelineSource::ChunkMode::MULTIPLE_CHUNKS_NO_PROCESSING);
// }
//
// TEST(PipelineTest, SourceToSink_MultipleChunks_NoProcessing_MaxThreads) {
//    Pipeline pipeline;
//
//    auto source = std::make_unique<TestPipelineSource>(
//        TestPipelineSource::ChunkMode::MULTIPLE_CHUNKS_NO_PROCESSING);
//    auto sink = std::make_unique<TestPipelineSink>(1);
//    auto* sink_ptr = sink.get();
//
//    EXPECT_TRUE(pipeline.AddSource(std::move(source)).ok());
//    EXPECT_TRUE(pipeline.AddSink(std::move(sink)).ok());
//
//    EXPECT_TRUE(pipeline.Run(0).ok());
//
//    const auto* output = sink_ptr->GetOutputImage();
//    ASSERT_NE(output, nullptr);
//    ValidatePixelValues(
//        *output,
//        TestPipelineSource::ChunkMode::MULTIPLE_CHUNKS_NO_PROCESSING);
// }