#include "absl/cleanup/cleanup.h"
#include "glog/logging.h"

#include "camraw/dng_file_parser.h"
#include "camraw/status_macros.h"
#include "camraw/tiff_tag_id.h"

namespace cmrw {

absl::StatusOr<bool>
DNGFileParser::ValidateFile() {
   bool validated;
   ASSIGN_OR_RETURN(validated, TIFFFileParser::ValidateFile());
   if (!validated) {
      return false;
   }

   validated_ = false;
   VLOG(2) << "Validating DNG file.";

   const TIFFIFD* ifd;
   initialized_ = true;  // to trick GetIDFAtIndex(), clean up on function exit
   absl::Cleanup unsetInitialized([&]() { initialized_ = false; });
   ASSIGN_OR_RETURN(ifd, GetIFDAtIndex(0));

   auto dng_ver_or = ifd->GetTag(TIFFTagId::DNG_VERSION);
   if (!dng_ver_or.ok()) {
      if (dng_ver_or.status().code() == absl::StatusCode::kNotFound) {
         // not a DNG file if the first IFD doesn't have a DNG version tag.
         VLOG(2) << "No DNG version tag found in first ifd.";
         return false;
      }
      return dng_ver_or.status();
   }

   VLOG(1) << "DNG file validated.";

   validated_ = true;
   return true;
}

}  // namespace cmrw
