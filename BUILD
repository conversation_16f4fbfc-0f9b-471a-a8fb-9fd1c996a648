load("@hedron_compile_commands//:refresh_compile_commands.bzl", "refresh_compile_commands")

refresh_compile_commands(
    name = "refresh_compile_commands",
    targets = {
      "//apps/process-raw:process-raw": "",
      "//apps/dump-tags:dump-tags": "",
      "//apps/extract-thumbnails:extract-thumbnails": "",
      "//camraw:bit_pump_test": "",
      "//camraw:camraw": "",
      "//camraw:data_accessor_test": "",
      "//camraw:endian_test": "",
      "//camraw:huffman_table_test": "",
      "//camraw:imagebuf_test": "",
      "//camraw:imagemeta_test": "",
      "//camraw:jpeg_bit_pump_test": "",
      "//camraw:matrix_test": "",
      "//camraw:pipeline_test": "",
      "//camraw:tiff_file_parser_test": "",
      "//camraw:tiff_ifd_test": "",
      "//camraw:tiff_tag_test": "",
    },
)
